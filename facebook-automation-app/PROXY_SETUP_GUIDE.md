# Proxy Setup Guide for Facebook Automation App

## Overview

This guide explains how to configure proxy settings for your browser profiles to ensure optimal performance with antidetect browser technology.

## Supported Proxy Types

### 1. No Proxy (Local Network)
- **Description**: Direct connection without proxy
- **Use Cases**: Local development, testing, no anonymity required
- **Security Level**: Low
- **Speed**: Fast
- **Configuration**: No additional settings required

### 2. HTTP Proxy
- **Description**: Standard HTTP proxy for web traffic
- **Use Cases**: Web browsing, API requests, basic anonymity
- **Security Level**: Medium
- **Speed**: Fast
- **Common Ports**: 8080, 3128, 8888, 80
- **Configuration**:
  - Host: IP address or domain name
  - Port: Proxy port number
  - Username/Password: Optional authentication

### 3. HTTPS Proxy
- **Description**: HTTPS proxy with SSL encryption
- **Use Cases**: Secure web browsing, encrypted traffic
- **Security Level**: High
- **Speed**: Medium
- **Common Ports**: 8080, 3128, 8888, 443
- **Configuration**:
  - Host: IP address or domain name
  - Port: Proxy port number
  - Username/Password: Optional authentication

### 4. SOCKS5 Proxy
- **Description**: SOCKS5 proxy supporting all traffic types
- **Use Cases**: All protocols, high anonymity, advanced users
- **Security Level**: High
- **Speed**: Medium
- **Common Ports**: 1080, 1081, 9050
- **Configuration**:
  - Host: IP address or domain name
  - Port: Proxy port number
  - Username/Password: Optional authentication

### 5. SSH Proxy
- **Description**: SSH tunnel proxy for maximum security
- **Use Cases**: Secure tunneling, server access, maximum privacy
- **Security Level**: Very High
- **Speed**: Slow
- **Common Ports**: 22, 2222
- **Configuration**:
  - Host: SSH server address
  - Port: SSH port number
  - Username: SSH username (required)
  - Password: SSH password or leave empty for key-based auth

## Configuration Steps

### 1. Create New Profile
1. Click "New Profile" button
2. Enter profile name
3. Configure browser settings (optional)

### 2. Configure Proxy Settings
1. Select proxy type from dropdown
2. Enter proxy host (IP address or domain)
3. Enter proxy port number
4. Add username/password if required
5. Click "Test Proxy Connection" to verify

### 3. Test Configuration
- Use "Test Proxy Connection" to verify proxy works
- Use "Test Browser" to ensure antidetect browser compatibility
- Check response time and IP address change

## Best Practices

### Proxy Selection
- **For Speed**: Use HTTP proxy with reliable provider
- **For Security**: Use HTTPS or SOCKS5 proxy
- **For Maximum Privacy**: Use SSH tunnel or SOCKS5
- **For Testing**: Start with "No Proxy" then upgrade

### Configuration Tips
1. **Always test proxy connection** before using for automation
2. **Use dedicated proxies** for better performance and reliability
3. **Rotate proxies regularly** to avoid detection
4. **Monitor proxy performance** and replace slow/unreliable ones
5. **Keep proxy credentials secure** - they are encrypted in database

### Common Port Numbers
- **HTTP**: 8080, 3128, 8888, 80
- **HTTPS**: 8080, 3128, 8888, 443
- **SOCKS5**: 1080, 1081, 9050
- **SSH**: 22, 2222

## Troubleshooting

### Connection Issues
1. **Verify proxy credentials** - check username/password
2. **Test different ports** - try common ports for your proxy type
3. **Check firewall settings** - ensure proxy traffic is allowed
4. **Contact proxy provider** - verify service status

### Performance Issues
1. **Test response time** - should be under 5 seconds
2. **Try different proxy servers** - some may be overloaded
3. **Check proxy location** - closer servers are usually faster
4. **Monitor proxy usage** - avoid overusing single proxy

### Browser Compatibility
1. **Test browser configuration** - use "Test Browser" button
2. **Check antidetect settings** - ensure proper fingerprinting
3. **Verify proxy integration** - confirm proxy works with zendriver
4. **Update browser profile** - refresh configuration if needed

## Security Considerations

### Data Protection
- All proxy credentials are encrypted in the database
- Passwords are never stored in plain text
- Connection details are secured during transmission

### Privacy Features
- Proxy connections hide your real IP address
- Browser fingerprinting prevents detection
- User agent rotation for additional anonymity
- Timezone and language spoofing available

### Recommendations
1. **Use reputable proxy providers** with good privacy policies
2. **Enable HTTPS whenever possible** for encrypted connections
3. **Regularly update proxy credentials** for security
4. **Monitor for IP leaks** using online tools
5. **Use different proxies for different profiles** to avoid correlation

## API Integration

### Testing Proxy Connection
```javascript
// Test proxy for specific profile
const response = await fetch(`/api/profiles/${profileId}/test-proxy`, {
  method: 'POST'
});
const result = await response.json();
```

### Getting Zendriver Configuration
```javascript
// Get zendriver config for profile
const response = await fetch(`/api/profiles/${profileId}/zendriver-config`);
const config = await response.json();
```

### Available Proxy Types
```javascript
// Get all supported proxy types
const response = await fetch('/api/profiles/proxy-types');
const proxyTypes = await response.json();
```

## Support

For additional help with proxy configuration:
1. Check the application logs for detailed error messages
2. Use the built-in proxy testing tools
3. Verify your proxy provider's documentation
4. Contact support if issues persist

## Advanced Configuration

### Custom Browser Settings
- Screen resolution spoofing
- Timezone manipulation
- Language preferences
- WebRTC blocking
- Canvas fingerprint randomization

### Automation Integration
- Zendriver compatibility
- Profile persistence
- Session management
- Cookie handling
- Local storage isolation

Remember: Proper proxy configuration is crucial for successful Facebook automation while maintaining anonymity and avoiding detection.
