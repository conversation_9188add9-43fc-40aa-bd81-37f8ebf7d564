// Test script to verify frontend API calls work correctly
const axios = require('axios');

const BASE_URL = 'http://localhost:8000';

async function testProfilesAPI() {
  console.log('🧪 Testing Profiles API from Frontend perspective');
  console.log('=' * 50);

  try {
    // Test 1: Get all profiles
    console.log('\n1. Testing GET /api/profiles...');
    const response = await axios.get(`${BASE_URL}/api/profiles`);
    
    console.log(`Status: ${response.status}`);
    console.log(`Response type: ${typeof response.data}`);
    console.log(`Is array: ${Array.isArray(response.data)}`);
    console.log(`Length: ${response.data.length}`);
    
    if (response.data.length > 0) {
      console.log('First profile structure:');
      console.log(JSON.stringify(response.data[0], null, 2));
    }
    
    // Test 2: Create a test profile
    console.log('\n2. Testing POST /api/profiles...');
    const createData = {
      name: `Test Profile ${Date.now()}`,
      user_agent: "Mozilla/5.0 (Test Browser)",
      browser_config: {
        browser_type: "chrome",
        screen_resolution: "1920x1080",
        timezone: "UTC",
        language: "en-US"
      },
      proxy_config: {
        proxy_type: "http",
        host: "proxy.example.com",
        port: 8080,
        username: "testuser",
        password: "testpass"
      }
    };
    
    const createResponse = await axios.post(`${BASE_URL}/api/profiles`, createData);
    console.log(`Create status: ${createResponse.status}`);
    console.log('Created profile:');
    console.log(JSON.stringify(createResponse.data, null, 2));
    
    const profileId = createResponse.data.id;
    
    // Test 3: Test proxy for created profile
    console.log(`\n3. Testing POST /api/profiles/${profileId}/test-proxy...`);
    try {
      const proxyTestResponse = await axios.post(`${BASE_URL}/api/profiles/${profileId}/test-proxy`);
      console.log(`Proxy test status: ${proxyTestResponse.status}`);
      console.log('Proxy test result:');
      console.log(JSON.stringify(proxyTestResponse.data, null, 2));
    } catch (error) {
      console.log(`Proxy test error: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Test 4: Test browser for created profile
    console.log(`\n4. Testing POST /api/profiles/${profileId}/test-browser...`);
    try {
      const browserTestResponse = await axios.post(`${BASE_URL}/api/profiles/${profileId}/test-browser`);
      console.log(`Browser test status: ${browserTestResponse.status}`);
      console.log('Browser test result:');
      console.log(JSON.stringify(browserTestResponse.data, null, 2));
    } catch (error) {
      console.log(`Browser test error: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Test 5: Get zendriver config
    console.log(`\n5. Testing GET /api/profiles/${profileId}/zendriver-config...`);
    try {
      const zendriverResponse = await axios.get(`${BASE_URL}/api/profiles/${profileId}/zendriver-config`);
      console.log(`Zendriver config status: ${zendriverResponse.status}`);
      console.log('Zendriver config:');
      console.log(JSON.stringify(zendriverResponse.data, null, 2));
    } catch (error) {
      console.log(`Zendriver config error: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Test 6: Get proxy types
    console.log('\n6. Testing GET /api/profiles/proxy-types...');
    try {
      const proxyTypesResponse = await axios.get(`${BASE_URL}/api/profiles/proxy-types`);
      console.log(`Proxy types status: ${proxyTypesResponse.status}`);
      console.log('Proxy types:');
      console.log(JSON.stringify(proxyTypesResponse.data, null, 2));
    } catch (error) {
      console.log(`Proxy types error: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Test 7: Update profile
    console.log(`\n7. Testing PUT /api/profiles/${profileId}...`);
    const updateData = {
      name: `Updated Test Profile ${Date.now()}`,
      proxy_config: {
        proxy_type: "socks5",
        host: "socks.example.com",
        port: 1080,
        username: null,
        password: null
      }
    };
    
    try {
      const updateResponse = await axios.put(`${BASE_URL}/api/profiles/${profileId}`, updateData);
      console.log(`Update status: ${updateResponse.status}`);
      console.log('Updated profile:');
      console.log(JSON.stringify(updateResponse.data, null, 2));
    } catch (error) {
      console.log(`Update error: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Test 8: Get updated profiles list
    console.log('\n8. Testing GET /api/profiles (after updates)...');
    const finalResponse = await axios.get(`${BASE_URL}/api/profiles`);
    console.log(`Final status: ${finalResponse.status}`);
    console.log(`Final count: ${finalResponse.data.length}`);
    
    console.log('\n' + '=' * 50);
    console.log('🎉 Frontend API testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testProfilesAPI();
