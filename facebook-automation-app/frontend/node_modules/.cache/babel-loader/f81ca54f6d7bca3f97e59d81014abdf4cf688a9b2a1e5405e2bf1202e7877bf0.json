{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, Button, Grid, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Divider, Avatar, Menu, ListItemIcon, ListItemText, Alert, LinearProgress, FormHelperText } from '@mui/material';\nimport { Add as AddIcon, Person as PersonIcon, MoreVert as MoreVertIcon, Edit as EditIcon, Delete as DeleteIcon, PlayArrow as PlayIcon, Refresh as RefreshIcon, Computer as ComputerIcon, Security as SecurityIcon, Language as LanguageIcon, LocationOn as LocationIcon, Facebook as FacebookIcon, Login as LoginIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProfileCard({\n  profile,\n  onEdit,\n  onDelete,\n  onTest,\n  onFacebookLogin,\n  onFacebookLoginComplete\n}) {\n  _s();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const handleMenuClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const isLoggedIn = profile.facebook_email || profile.facebook_user_id;\n  const isReady = profile.status === 'active' && isLoggedIn;\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'flex-start',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: 'primary.main',\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            children: profile.name.charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                mb: 0.5\n              },\n              children: profile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: profile.status || 'inactive',\n              size: \"small\",\n              color: getStatusColor(profile.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleMenuClick,\n          children: /*#__PURE__*/_jsxDEV(MoreVertIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(ComputerIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"Chrome\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LocationIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.timezone || 'UTC'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Proxy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.proxy_type && profile.proxy_type !== 'no_proxy' ? profile.proxy_type.toUpperCase() : 'No Proxy'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.language || 'en-US'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FacebookIcon, {\n            fontSize: \"small\",\n            color: \"action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              fontSize: '0.75rem'\n            },\n            children: \"Facebook:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), isLoggedIn ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#4caf50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 500,\n                color: '#4caf50'\n              },\n              children: \"Logged In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(LoginIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#ff9800'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 500,\n                color: '#ff9800'\n              },\n              children: \"Not Logged In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 24\n          }, this),\n          onClick: () => onTest(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"text\",\n          startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 24\n          }, this),\n          onClick: () => onEdit(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        anchorEl: anchorEl,\n        open: Boolean(anchorEl),\n        onClose: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onEdit(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(EditIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Edit Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onTest(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(PlayIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Test Browser\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), !isLoggedIn ? /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLogin(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(FacebookIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#1877f2'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Login to Facebook\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLoginComplete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#4caf50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Complete Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onDelete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Delete Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n}\n_s(ProfileCard, \"+aMDa7FPcESUyQDF1vq0RSMn4/k=\");\n_c = ProfileCard;\nfunction Profiles() {\n  _s2();\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: ''\n  });\n  const queryClient = useQueryClient();\n  const {\n    setProfiles\n  } = useApp();\n\n  // Fetch profiles\n  const {\n    data: profilesData,\n    isLoading,\n    error\n  } = useQuery('profiles', () => profilesAPI.getAll().then(response => response.data), {\n    onSuccess: data => {\n      // Handle both array and object with profiles property\n      const profilesList = (data === null || data === void 0 ? void 0 : data.profiles) || data || [];\n      setProfiles(profilesList);\n    },\n    onError: error => {\n      toast.error('Failed to load profiles');\n    }\n  });\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      console.error('Create profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to create profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(({\n    id,\n    data\n  }) => profilesAPI.update(id, data), {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setEditDialogOpen(false);\n      resetForm();\n      toast.success('Profile updated successfully');\n    },\n    onError: error => {\n      var _error$response2, _error$response2$data;\n      console.error('Update profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to update profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: error => {\n      toast.error('Failed to delete profile');\n    }\n  });\n\n  // Test proxy mutation\n  const testProxyMutation = useMutation(profileId => profilesAPI.testProxy(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'success') {\n        toast.success(`Proxy test successful! Response time: ${result.response_time}s`);\n      } else if (result.status === 'no_proxy') {\n        toast.info('No proxy configured for this profile');\n      } else {\n        toast.error(`Proxy test failed: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response3, _error$response4, _error$response4$data;\n      console.error('Proxy test error:', error);\n\n      // Handle specific error cases\n      if ((error === null || error === void 0 ? void 0 : (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 404) {\n        toast.warning('Proxy test endpoint not available. This feature may not be implemented yet.');\n      } else if ((error === null || error === void 0 ? void 0 : (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) === \"Endpoint not found\") {\n        toast.warning('Test feature not available in current backend version.');\n      } else {\n        var _error$response5, _error$response5$data, _error$response6, _error$response6$data;\n        const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || (error === null || error === void 0 ? void 0 : (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || (error === null || error === void 0 ? void 0 : error.message) || 'Proxy test failed';\n        toast.error(errorMessage);\n      }\n    }\n  });\n\n  // Facebook login mutations\n  const facebookLoginMutation = useMutation(profileId => profilesAPI.facebookLogin(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'login_initiated') {\n        toast.success('Facebook login session started! Complete login manually in the browser.');\n        // Show instructions\n        const instructions = result.instructions.join('\\n');\n        setTimeout(() => {\n          toast.info(`Login Instructions:\\n${instructions}`, {\n            duration: 10000\n          });\n        }, 1000);\n      } else {\n        toast.error(`Failed to start login: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response7, _error$response7$data;\n      console.error('Facebook login error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to start Facebook login';\n      toast.error(errorMessage);\n    }\n  });\n  const facebookLoginCompleteMutation = useMutation(({\n    profileId,\n    facebookData\n  }) => profilesAPI.facebookLoginComplete(profileId, facebookData), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'login_complete') {\n        toast.success(`Facebook login completed! Profile \"${result.profile_name}\" is now ready.`);\n        queryClient.invalidateQueries('profiles');\n      } else {\n        toast.error(`Failed to complete login: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response8, _error$response8$data;\n      console.error('Facebook login complete error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to complete Facebook login';\n      toast.error(errorMessage);\n    }\n  });\n  const profiles = Array.isArray(profilesData === null || profilesData === void 0 ? void 0 : profilesData.profiles) ? profilesData.profiles : Array.isArray(profilesData) ? profilesData : [];\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: ''\n    });\n    setSelectedProfile(null);\n  };\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n  const handleEdit = profile => {\n    setSelectedProfile(profile);\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome',\n      // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '',\n      // Not in current API response\n      proxy_password: '' // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n  const handleDelete = profile => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n  const handleTest = profile => {\n    // Show profile information\n    const proxyInfo = profile.proxy_type && profile.proxy_type !== 'no_proxy' ? `${profile.proxy_type.toUpperCase()}${profile.proxy_host ? ` (${profile.proxy_host}:${profile.proxy_port})` : ''}` : 'No Proxy';\n\n    // Create a detailed info message\n    const profileInfo = [`Profile: ${profile.name}`, `Status: ${profile.status}`, `Proxy: ${proxyInfo}`, `Language: ${profile.language || 'en-US'}`, `Timezone: ${profile.timezone || 'UTC'}`, `Created: ${new Date(profile.created_at).toLocaleDateString()}`].join('\\n');\n\n    // Show info first\n    toast.success(`Profile Information:\\n${profileInfo}`, {\n      duration: 5000\n    });\n\n    // Try to test proxy if configured\n    if (profile.proxy_type && profile.proxy_type !== 'no_proxy' && profile.proxy_host) {\n      setTimeout(() => {\n        testProxyMutation.mutate(profile.id);\n      }, 1000);\n    }\n  };\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = {\n      ...formData\n    };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n    if (selectedProfile) {\n      updateMutation.mutate({\n        id: selectedProfile.id,\n        data: finalData\n      });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleFacebookLogin = profile => {\n    facebookLoginMutation.mutate(profile.id);\n  };\n  const handleFacebookLoginComplete = profile => {\n    // For now, complete without additional data\n    // In a real implementation, you might collect Facebook user data\n    facebookLoginCompleteMutation.mutate({\n      profileId: profile.id,\n      facebookData: {\n        email: \"<EMAIL>\",\n        // This would be extracted from browser\n        username: \"facebook_user\",\n        user_id: \"123456789\"\n      }\n    });\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      className: \"fade-in\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        children: \"Failed to load profiles. Please check your connection to the backend.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 592,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 700\n        },\n        children: \"Profile Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 24\n          }, this),\n          onClick: () => queryClient.invalidateQueries('profiles'),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 24\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"New Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 7\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(LinearProgress, {\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 21\n    }, this), profiles.length === 0 && !isLoading ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center',\n          py: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n          sx: {\n            fontSize: 64,\n            color: 'text.secondary',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1\n          },\n          children: \"No Profiles Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 3\n          },\n          children: \"Create your first browser profile to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 26\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Create Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 631,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: profiles.map(profile => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(ProfileCard, {\n          profile: profile,\n          onEdit: handleEdit,\n          onDelete: handleDelete,\n          onTest: handleTest,\n          onFacebookLogin: handleFacebookLogin,\n          onFacebookLoginComplete: handleFacebookLoginComplete\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 15\n        }, this)\n      }, profile.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 653,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: createDialogOpen || editDialogOpen,\n      onClose: () => {\n        setCreateDialogOpen(false);\n        setEditDialogOpen(false);\n        resetForm();\n      },\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: selectedProfile ? 'Edit Profile' : 'Create New Profile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Profile Name\",\n                value: formData.name,\n                onChange: e => handleFormChange('name', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Browser Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.browser_type,\n                  onChange: e => handleFormChange('browser_type', e.target.value),\n                  label: \"Browser Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"chrome\",\n                    children: \"Chrome\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"firefox\",\n                    children: \"Firefox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"edge\",\n                    children: \"Edge\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Screen Resolution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.screen_resolution,\n                  onChange: e => handleFormChange('screen_resolution', e.target.value),\n                  label: \"Screen Resolution\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1920x1080\",\n                    children: \"1920x1080\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1366x768\",\n                    children: \"1366x768\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1440x900\",\n                    children: \"1440x900\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1280x720\",\n                    children: \"1280x720\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Timezone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.timezone,\n                  onChange: e => handleFormChange('timezone', e.target.value),\n                  label: \"Timezone\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"UTC\",\n                    children: \"UTC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 733,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Asia/Ho_Chi_Minh\",\n                    children: \"Asia/Ho_Chi_Minh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"America/New_York\",\n                    children: \"America/New_York\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Europe/London\",\n                    children: \"Europe/London\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.language,\n                  onChange: e => handleFormChange('language', e.target.value),\n                  label: \"Language\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"en-US\",\n                    children: \"English (US)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"vi-VN\",\n                    children: \"Vietnamese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"zh-CN\",\n                    children: \"Chinese (Simplified)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ja-JP\",\n                    children: \"Japanese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 752,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"User Agent (Optional)\",\n                value: formData.user_agent,\n                onChange: e => handleFormChange('user_agent', e.target.value),\n                placeholder: \"Leave empty for automatic generation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 600\n                },\n                children: \"Proxy Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Proxy Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.proxy_type,\n                  onChange: e => {\n                    const proxyType = e.target.value;\n                    handleFormChange('proxy_type', proxyType);\n                    handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                  },\n                  label: \"Proxy Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"no_proxy\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"No Proxy (Local Network)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 787,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Direct connection without proxy\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 788,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 786,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 785,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"http\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTP\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 795,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Standard HTTP proxy (ports: 8080, 3128, 8888)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 796,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 794,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 793,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"https\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTPS\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 803,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Encrypted HTTPS proxy (ports: 8080, 3128, 443)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 804,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 802,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"socks5\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SOCKS5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 811,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"High anonymity proxy (ports: 1080, 1081, 9050)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 812,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 810,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 809,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ssh\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SSH\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 819,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Secure SSH tunnel (ports: 22, 2222)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 820,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 818,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: [formData.proxy_type === 'no_proxy' && 'Using direct connection', formData.proxy_type === 'http' && 'Fast, suitable for web browsing', formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data', formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols', formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 15\n            }, this), formData.proxy_type !== 'no_proxy' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Host\",\n                  value: formData.proxy_host,\n                  onChange: e => handleFormChange('proxy_host', e.target.value),\n                  required: true,\n                  placeholder: formData.proxy_type === 'ssh' ? \"e.g., ssh.example.com or *************\" : \"e.g., proxy.example.com or *************\",\n                  helperText: \"IP address or domain name of the proxy server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Port\",\n                  value: formData.proxy_port,\n                  onChange: e => handleFormChange('proxy_port', e.target.value),\n                  type: \"number\",\n                  required: true,\n                  placeholder: formData.proxy_type === 'http' ? \"8080, 3128, 8888\" : formData.proxy_type === 'https' ? \"8080, 3128, 443\" : formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" : formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\",\n                  helperText: `Common ${formData.proxy_type.toUpperCase()} ports`,\n                  inputProps: {\n                    min: 1,\n                    max: 65535\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\",\n                  value: formData.proxy_username,\n                  onChange: e => handleFormChange('proxy_username', e.target.value),\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH username for authentication\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Required for SSH connections\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\",\n                  value: formData.proxy_password,\n                  onChange: e => handleFormChange('proxy_password', e.target.value),\n                  type: \"password\",\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH password or leave empty for key-based auth\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Password or private key authentication\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 889,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2,\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    onClick: () => {\n                      if (selectedProfile) {\n                        testProxyMutation.mutate(selectedProfile.id);\n                      } else {\n                        // Test proxy configuration without saving\n                        const proxyInfo = `${formData.proxy_type.toUpperCase()} proxy: ${formData.proxy_host}:${formData.proxy_port}`;\n                        const authInfo = formData.proxy_username ? ` (Auth: ${formData.proxy_username})` : ' (No Auth)';\n                        toast.info(`Proxy Configuration:\\n${proxyInfo}${authInfo}\\n\\nSave profile first to test connection.`, {\n                          duration: 4000\n                        });\n                      }\n                    },\n                    disabled: !formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading,\n                    sx: {\n                      textTransform: 'none'\n                    },\n                    children: testProxyMutation.isLoading ? 'Testing...' : selectedProfile ? 'Test Connection' : 'Preview Config'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Test proxy connection to ensure it works with antidetect browser\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setCreateDialogOpen(false);\n            setEditDialogOpen(false);\n            resetForm();\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 940,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleSubmit,\n          disabled: !formData.name || createMutation.isLoading || updateMutation.isLoading,\n          children: selectedProfile ? 'Update' : 'Create'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 939,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 601,\n    columnNumber: 5\n  }, this);\n}\n_s2(Profiles, \"gSyhGb+eSbbGh1oPmjgQPLIZ5WY=\", false, function () {\n  return [useQueryClient, useApp, useQuery, useMutation, useMutation, useMutation, useMutation, useMutation, useMutation];\n});\n_c2 = Profiles;\nexport default Profiles;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProfileCard\");\n$RefreshReg$(_c2, \"Profiles\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "Avatar", "<PERSON><PERSON>", "ListItemIcon", "ListItemText", "<PERSON><PERSON>", "LinearProgress", "FormHelperText", "Add", "AddIcon", "Person", "PersonIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "PlayArrow", "PlayIcon", "Refresh", "RefreshIcon", "Computer", "ComputerIcon", "Security", "SecurityIcon", "Language", "LanguageIcon", "LocationOn", "LocationIcon", "Facebook", "FacebookIcon", "<PERSON><PERSON>", "LoginIcon", "CheckCircle", "CheckCircleIcon", "useQuery", "useMutation", "useQueryClient", "toast", "profilesAPI", "useApp", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfileCard", "profile", "onEdit", "onDelete", "onTest", "onFacebookLogin", "onFacebookLoginComplete", "_s", "anchorEl", "setAnchorEl", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "isLoggedIn", "facebook_email", "facebook_user_id", "isReady", "status", "getStatusColor", "sx", "height", "children", "display", "justifyContent", "alignItems", "mb", "bgcolor", "width", "mr", "name", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "label", "size", "color", "onClick", "my", "container", "spacing", "item", "xs", "fontSize", "timezone", "proxy_type", "language", "mt", "gap", "startIcon", "textTransform", "open", "Boolean", "onClose", "_c", "Profiles", "_s2", "createDialogOpen", "setCreateDialogOpen", "editDialogOpen", "setEditDialogOpen", "selectedProfile", "setSelectedProfile", "formData", "setFormData", "browser_type", "user_agent", "screen_resolution", "proxy_enabled", "proxy_host", "proxy_port", "proxy_username", "proxy_password", "queryClient", "setProfiles", "data", "profilesData", "isLoading", "error", "getAll", "then", "response", "onSuccess", "profilesList", "profiles", "onError", "createMutation", "create", "invalidateQueries", "resetForm", "success", "_error$response", "_error$response$data", "console", "errorMessage", "detail", "message", "updateMutation", "id", "update", "_error$response2", "_error$response2$data", "deleteMutation", "delete", "testProxyMutation", "profileId", "testProxy", "result", "response_time", "info", "_error$response3", "_error$response4", "_error$response4$data", "warning", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data", "facebookLoginMutation", "facebookLogin", "instructions", "join", "setTimeout", "duration", "_error$response7", "_error$response7$data", "facebookLoginCompleteMutation", "facebookData", "facebookLoginComplete", "profile_name", "_error$response8", "_error$response8$data", "Array", "isArray", "handleCreate", "handleEdit", "toString", "handleDelete", "window", "confirm", "mutate", "handleTest", "proxyInfo", "profileInfo", "Date", "created_at", "toLocaleDateString", "handleSubmit", "submitData", "port", "parseInt", "isNaN", "finalData", "browser_config", "proxy_config", "host", "username", "password", "handleFormChange", "field", "value", "prev", "handleFacebookLogin", "handleFacebookLoginComplete", "email", "user_id", "className", "severity", "length", "textAlign", "py", "map", "sm", "md", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "onChange", "e", "target", "required", "placeholder", "proxyType", "helperText", "type", "inputProps", "min", "max", "authInfo", "disabled", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  Button,\n  Grid,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Avatar,\n  Menu,\n  ListItemIcon,\n  ListItemText,\n  Alert,\n  LinearProgress,\n  FormHelperText,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Person as PersonIcon,\n  MoreVert as MoreVertIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  PlayArrow as PlayIcon,\n  Refresh as RefreshIcon,\n  Computer as ComputerIcon,\n  Security as SecurityIcon,\n  Language as LanguageIcon,\n  LocationOn as LocationIcon,\n  Facebook as FacebookIcon,\n  Login as LoginIcon,\n  CheckCircle as CheckCircleIcon,\n} from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\n\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\n\nfunction ProfileCard({ profile, onEdit, onDelete, onTest, onFacebookLogin, onFacebookLoginComplete }) {\n  const [anchorEl, setAnchorEl] = useState(null);\n\n  const handleMenuClick = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const isLoggedIn = profile.facebook_email || profile.facebook_user_id;\n  const isReady = profile.status === 'active' && isLoggedIn;\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Avatar\n              sx={{\n                bgcolor: 'primary.main',\n                width: 48,\n                height: 48,\n                mr: 2,\n              }}\n            >\n              {profile.name.charAt(0).toUpperCase()}\n            </Avatar>\n            <Box>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                {profile.name}\n              </Typography>\n              <Chip\n                label={profile.status || 'inactive'}\n                size=\"small\"\n                color={getStatusColor(profile.status)}\n              />\n            </Box>\n          </Box>\n          <IconButton onClick={handleMenuClick}>\n            <MoreVertIcon />\n          </IconButton>\n        </Box>\n\n        <Divider sx={{ my: 2 }} />\n\n        <Grid container spacing={2}>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <ComputerIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Browser\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              Chrome\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LocationIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Location\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.timezone || 'UTC'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <SecurityIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Proxy\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.proxy_type && profile.proxy_type !== 'no_proxy'\n                ? profile.proxy_type.toUpperCase()\n                : 'No Proxy'}\n            </Typography>\n          </Grid>\n          <Box item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LanguageIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Language\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.language || 'en-US'}\n            </Typography>\n          </Box>\n\n        </Grid>\n\n        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <FacebookIcon fontSize=\"small\" color=\"action\" />\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ fontSize: '0.75rem' }}>\n              Facebook:\n            </Typography>\n            {isLoggedIn ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <CheckCircleIcon fontSize=\"small\" sx={{ color: '#4caf50' }} />\n                <Typography variant=\"body2\" sx={{ fontWeight: 500, color: '#4caf50' }}>\n                  Logged In\n                </Typography>\n              </Box>\n            ) : (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <LoginIcon fontSize=\"small\" sx={{ color: '#ff9800' }} />\n                <Typography variant=\"body2\" sx={{ fontWeight: 500, color: '#ff9800' }}>\n                  Not Logged In\n                </Typography>\n              </Box>\n            )}\n          </Box>\n        </Box>\n\n        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>\n          <Button\n            size=\"small\"\n            variant=\"outlined\"\n            startIcon={<PlayIcon />}\n            onClick={() => onTest(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Test\n          </Button>\n          <Button\n            size=\"small\"\n            variant=\"text\"\n            startIcon={<EditIcon />}\n            onClick={() => onEdit(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Edit\n          </Button>\n        </Box>\n\n        <Menu\n          anchorEl={anchorEl}\n          open={Boolean(anchorEl)}\n          onClose={handleMenuClose}\n        >\n          <MenuItem onClick={() => { onEdit(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <EditIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Edit Profile</ListItemText>\n          </MenuItem>\n          <MenuItem onClick={() => { onTest(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <PlayIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Test Browser</ListItemText>\n          </MenuItem>\n\n          <Divider />\n\n          {!isLoggedIn ? (\n            <MenuItem onClick={() => { onFacebookLogin(profile); handleMenuClose(); }}>\n              <ListItemIcon>\n                <FacebookIcon fontSize=\"small\" sx={{ color: '#1877f2' }} />\n              </ListItemIcon>\n              <ListItemText>Login to Facebook</ListItemText>\n            </MenuItem>\n          ) : (\n            <MenuItem onClick={() => { onFacebookLoginComplete(profile); handleMenuClose(); }}>\n              <ListItemIcon>\n                <CheckCircleIcon fontSize=\"small\" sx={{ color: '#4caf50' }} />\n              </ListItemIcon>\n              <ListItemText>Complete Login</ListItemText>\n            </MenuItem>\n          )}\n\n          <Divider />\n\n          <MenuItem onClick={() => { onDelete(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <DeleteIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Delete Profile</ListItemText>\n          </MenuItem>\n        </Menu>\n      </CardContent>\n    </Card>\n  );\n}\n\nfunction Profiles() {\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: '',\n  });\n\n  const queryClient = useQueryClient();\n  const { setProfiles } = useApp();\n\n  // Fetch profiles\n  const { data: profilesData, isLoading, error } = useQuery(\n    'profiles',\n    () => profilesAPI.getAll().then(response => response.data),\n    {\n      onSuccess: (data) => {\n        // Handle both array and object with profiles property\n        const profilesList = data?.profiles || data || [];\n        setProfiles(profilesList);\n      },\n      onError: (error) => {\n        toast.error('Failed to load profiles');\n      },\n    }\n  );\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: (error) => {\n      console.error('Create profile error:', error);\n      const errorMessage = error?.response?.data?.detail ||\n                          error?.message ||\n                          'Failed to create profile';\n      toast.error(errorMessage);\n    },\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(\n    ({ id, data }) => profilesAPI.update(id, data),\n    {\n      onSuccess: () => {\n        queryClient.invalidateQueries('profiles');\n        setEditDialogOpen(false);\n        resetForm();\n        toast.success('Profile updated successfully');\n      },\n      onError: (error) => {\n        console.error('Update profile error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to update profile';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: (error) => {\n      toast.error('Failed to delete profile');\n    },\n  });\n\n\n\n  // Test proxy mutation\n  const testProxyMutation = useMutation(\n    (profileId) => profilesAPI.testProxy(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'success') {\n          toast.success(`Proxy test successful! Response time: ${result.response_time}s`);\n        } else if (result.status === 'no_proxy') {\n          toast.info('No proxy configured for this profile');\n        } else {\n          toast.error(`Proxy test failed: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Proxy test error:', error);\n\n        // Handle specific error cases\n        if (error?.response?.status === 404) {\n          toast.warning('Proxy test endpoint not available. This feature may not be implemented yet.');\n        } else if (error?.response?.data?.message === \"Endpoint not found\") {\n          toast.warning('Test feature not available in current backend version.');\n        } else {\n          const errorMessage = error?.response?.data?.detail ||\n                              error?.response?.data?.message ||\n                              error?.message ||\n                              'Proxy test failed';\n          toast.error(errorMessage);\n        }\n      },\n    }\n  );\n\n  // Facebook login mutations\n  const facebookLoginMutation = useMutation(\n    (profileId) => profilesAPI.facebookLogin(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'login_initiated') {\n          toast.success('Facebook login session started! Complete login manually in the browser.');\n          // Show instructions\n          const instructions = result.instructions.join('\\n');\n          setTimeout(() => {\n            toast.info(`Login Instructions:\\n${instructions}`, { duration: 10000 });\n          }, 1000);\n        } else {\n          toast.error(`Failed to start login: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Facebook login error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to start Facebook login';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const facebookLoginCompleteMutation = useMutation(\n    ({ profileId, facebookData }) => profilesAPI.facebookLoginComplete(profileId, facebookData),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'login_complete') {\n          toast.success(`Facebook login completed! Profile \"${result.profile_name}\" is now ready.`);\n          queryClient.invalidateQueries('profiles');\n        } else {\n          toast.error(`Failed to complete login: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Facebook login complete error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to complete Facebook login';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const profiles = Array.isArray(profilesData?.profiles)\n    ? profilesData.profiles\n    : Array.isArray(profilesData)\n    ? profilesData\n    : [];\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: '',\n    });\n    setSelectedProfile(null);\n  };\n\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n\n  const handleEdit = (profile) => {\n    setSelectedProfile(profile);\n\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome', // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '', // Not in current API response\n      proxy_password: '', // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n\n  const handleDelete = (profile) => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n\n  const handleTest = (profile) => {\n    // Show profile information\n    const proxyInfo = profile.proxy_type && profile.proxy_type !== 'no_proxy'\n      ? `${profile.proxy_type.toUpperCase()}${profile.proxy_host ? ` (${profile.proxy_host}:${profile.proxy_port})` : ''}`\n      : 'No Proxy';\n\n    // Create a detailed info message\n    const profileInfo = [\n      `Profile: ${profile.name}`,\n      `Status: ${profile.status}`,\n      `Proxy: ${proxyInfo}`,\n      `Language: ${profile.language || 'en-US'}`,\n      `Timezone: ${profile.timezone || 'UTC'}`,\n      `Created: ${new Date(profile.created_at).toLocaleDateString()}`\n    ].join('\\n');\n\n    // Show info first\n    toast.success(`Profile Information:\\n${profileInfo}`, { duration: 5000 });\n\n    // Try to test proxy if configured\n    if (profile.proxy_type && profile.proxy_type !== 'no_proxy' && profile.proxy_host) {\n      setTimeout(() => {\n        testProxyMutation.mutate(profile.id);\n      }, 1000);\n    }\n  };\n\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = { ...formData };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n\n    if (selectedProfile) {\n      updateMutation.mutate({ id: selectedProfile.id, data: finalData });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleFacebookLogin = (profile) => {\n    facebookLoginMutation.mutate(profile.id);\n  };\n\n  const handleFacebookLoginComplete = (profile) => {\n    // For now, complete without additional data\n    // In a real implementation, you might collect Facebook user data\n    facebookLoginCompleteMutation.mutate({\n      profileId: profile.id,\n      facebookData: {\n        email: \"<EMAIL>\", // This would be extracted from browser\n        username: \"facebook_user\",\n        user_id: \"123456789\"\n      }\n    });\n  };\n\n  if (error) {\n    return (\n      <Box className=\"fade-in\">\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          Failed to load profiles. Please check your connection to the backend.\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box className=\"fade-in\">\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n          Profile Management\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => queryClient.invalidateQueries('profiles')}\n            sx={{ textTransform: 'none' }}\n          >\n            Refresh\n          </Button>\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={handleCreate}\n            sx={{ textTransform: 'none' }}\n          >\n            New Profile\n          </Button>\n        </Box>\n      </Box>\n\n      {isLoading && <LinearProgress sx={{ mb: 3 }} />}\n\n\n\n      {profiles.length === 0 && !isLoading ? (\n        <Card>\n          <CardContent sx={{ textAlign: 'center', py: 8 }}>\n            <PersonIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n            <Typography variant=\"h6\" sx={{ mb: 1 }}>\n              No Profiles Found\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              Create your first browser profile to get started\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={handleCreate}\n              sx={{ textTransform: 'none' }}\n            >\n              Create Profile\n            </Button>\n          </CardContent>\n        </Card>\n      ) : (\n        <Grid container spacing={3}>\n          {profiles.map((profile) => (\n            <Grid item xs={12} sm={6} md={4} key={profile.id}>\n              <ProfileCard\n                profile={profile}\n                onEdit={handleEdit}\n                onDelete={handleDelete}\n                onTest={handleTest}\n                onFacebookLogin={handleFacebookLogin}\n                onFacebookLoginComplete={handleFacebookLoginComplete}\n              />\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Create/Edit Profile Dialog */}\n      <Dialog\n        open={createDialogOpen || editDialogOpen}\n        onClose={() => {\n          setCreateDialogOpen(false);\n          setEditDialogOpen(false);\n          resetForm();\n        }}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          {selectedProfile ? 'Edit Profile' : 'Create New Profile'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Grid container spacing={3}>\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Profile Name\"\n                  value={formData.name}\n                  onChange={(e) => handleFormChange('name', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Browser Type</InputLabel>\n                  <Select\n                    value={formData.browser_type}\n                    onChange={(e) => handleFormChange('browser_type', e.target.value)}\n                    label=\"Browser Type\"\n                  >\n                    <MenuItem value=\"chrome\">Chrome</MenuItem>\n                    <MenuItem value=\"firefox\">Firefox</MenuItem>\n                    <MenuItem value=\"edge\">Edge</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Screen Resolution</InputLabel>\n                  <Select\n                    value={formData.screen_resolution}\n                    onChange={(e) => handleFormChange('screen_resolution', e.target.value)}\n                    label=\"Screen Resolution\"\n                  >\n                    <MenuItem value=\"1920x1080\">1920x1080</MenuItem>\n                    <MenuItem value=\"1366x768\">1366x768</MenuItem>\n                    <MenuItem value=\"1440x900\">1440x900</MenuItem>\n                    <MenuItem value=\"1280x720\">1280x720</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Timezone</InputLabel>\n                  <Select\n                    value={formData.timezone}\n                    onChange={(e) => handleFormChange('timezone', e.target.value)}\n                    label=\"Timezone\"\n                  >\n                    <MenuItem value=\"UTC\">UTC</MenuItem>\n                    <MenuItem value=\"Asia/Ho_Chi_Minh\">Asia/Ho_Chi_Minh</MenuItem>\n                    <MenuItem value=\"America/New_York\">America/New_York</MenuItem>\n                    <MenuItem value=\"Europe/London\">Europe/London</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Language</InputLabel>\n                  <Select\n                    value={formData.language}\n                    onChange={(e) => handleFormChange('language', e.target.value)}\n                    label=\"Language\"\n                  >\n                    <MenuItem value=\"en-US\">English (US)</MenuItem>\n                    <MenuItem value=\"vi-VN\">Vietnamese</MenuItem>\n                    <MenuItem value=\"zh-CN\">Chinese (Simplified)</MenuItem>\n                    <MenuItem value=\"ja-JP\">Japanese</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"User Agent (Optional)\"\n                  value={formData.user_agent}\n                  onChange={(e) => handleFormChange('user_agent', e.target.value)}\n                  placeholder=\"Leave empty for automatic generation\"\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n                  Proxy Configuration\n                </Typography>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Proxy Type</InputLabel>\n                  <Select\n                    value={formData.proxy_type}\n                    onChange={(e) => {\n                      const proxyType = e.target.value;\n                      handleFormChange('proxy_type', proxyType);\n                      handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                    }}\n                    label=\"Proxy Type\"\n                  >\n                    <MenuItem value=\"no_proxy\">\n                      <Box>\n                        <Typography variant=\"body2\">No Proxy (Local Network)</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Direct connection without proxy\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"http\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTP</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Standard HTTP proxy (ports: 8080, 3128, 8888)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"https\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTPS</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Encrypted HTTPS proxy (ports: 8080, 3128, 443)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"socks5\">\n                      <Box>\n                        <Typography variant=\"body2\">SOCKS5</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          High anonymity proxy (ports: 1080, 1081, 9050)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"ssh\">\n                      <Box>\n                        <Typography variant=\"body2\">SSH</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Secure SSH tunnel (ports: 22, 2222)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  </Select>\n                  <FormHelperText>\n                    {formData.proxy_type === 'no_proxy' && 'Using direct connection'}\n                    {formData.proxy_type === 'http' && 'Fast, suitable for web browsing'}\n                    {formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data'}\n                    {formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols'}\n                    {formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials'}\n                  </FormHelperText>\n                </FormControl>\n              </Grid>\n\n              {formData.proxy_type !== 'no_proxy' && (\n                <>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Host\"\n                      value={formData.proxy_host}\n                      onChange={(e) => handleFormChange('proxy_host', e.target.value)}\n                      required\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"e.g., ssh.example.com or *************\"\n                          : \"e.g., proxy.example.com or *************\"\n                      }\n                      helperText=\"IP address or domain name of the proxy server\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Port\"\n                      value={formData.proxy_port}\n                      onChange={(e) => handleFormChange('proxy_port', e.target.value)}\n                      type=\"number\"\n                      required\n                      placeholder={\n                        formData.proxy_type === 'http' ? \"8080, 3128, 8888\" :\n                        formData.proxy_type === 'https' ? \"8080, 3128, 443\" :\n                        formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" :\n                        formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\"\n                      }\n                      helperText={`Common ${formData.proxy_type.toUpperCase()} ports`}\n                      inputProps={{ min: 1, max: 65535 }}\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\"}\n                      value={formData.proxy_username}\n                      onChange={(e) => handleFormChange('proxy_username', e.target.value)}\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH username for authentication\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Required for SSH connections\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\"}\n                      value={formData.proxy_password}\n                      onChange={(e) => handleFormChange('proxy_password', e.target.value)}\n                      type=\"password\"\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH password or leave empty for key-based auth\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Password or private key authentication\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n\n                  <Grid item xs={12}>\n                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>\n                      <Button\n                        variant=\"outlined\"\n                        size=\"small\"\n                        onClick={() => {\n                          if (selectedProfile) {\n                            testProxyMutation.mutate(selectedProfile.id);\n                          } else {\n                            // Test proxy configuration without saving\n                            const proxyInfo = `${formData.proxy_type.toUpperCase()} proxy: ${formData.proxy_host}:${formData.proxy_port}`;\n                            const authInfo = formData.proxy_username ? ` (Auth: ${formData.proxy_username})` : ' (No Auth)';\n                            toast.info(`Proxy Configuration:\\n${proxyInfo}${authInfo}\\n\\nSave profile first to test connection.`, { duration: 4000 });\n                          }\n                        }}\n                        disabled={!formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading}\n                        sx={{ textTransform: 'none' }}\n                      >\n                        {testProxyMutation.isLoading ? 'Testing...' : selectedProfile ? 'Test Connection' : 'Preview Config'}\n                      </Button>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Test proxy connection to ensure it works with antidetect browser\n                      </Typography>\n                    </Box>\n                  </Grid>\n                </>\n              )}\n            </Grid>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => {\n              setCreateDialogOpen(false);\n              setEditDialogOpen(false);\n              resetForm();\n            }}\n          >\n            Cancel\n          </Button>\n          <Button\n            variant=\"contained\"\n            onClick={handleSubmit}\n            disabled={!formData.name || createMutation.isLoading || updateMutation.isLoading}\n          >\n            {selectedProfile ? 'Update' : 'Create'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n}\n\nexport default Profiles;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,cAAc,EACdC,cAAc,QACT,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,QAAQ,EACrBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,YAAY,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACnE,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,MAAM,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,SAASC,WAAWA,CAAC;EAAEC,OAAO;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,MAAM;EAAEC,eAAe;EAAEC;AAAwB,CAAC,EAAE;EAAAC,EAAA;EACpG,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAM0E,eAAe,GAAIC,KAAK,IAAK;IACjCF,WAAW,CAACE,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BJ,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMK,UAAU,GAAGb,OAAO,CAACc,cAAc,IAAId,OAAO,CAACe,gBAAgB;EACrE,MAAMC,OAAO,GAAGhB,OAAO,CAACiB,MAAM,KAAK,QAAQ,IAAIJ,UAAU;EAEzD,MAAMK,cAAc,GAAID,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACErB,OAAA,CAAC1D,IAAI;IAACiF,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC3BzB,OAAA,CAACzD,WAAW;MAAAkF,QAAA,gBACVzB,OAAA,CAAC5D,GAAG;QAACmF,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,YAAY;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAC7FzB,OAAA,CAAC5D,GAAG;UAACmF,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjDzB,OAAA,CAAC1C,MAAM;YACLiE,EAAE,EAAE;cACFO,OAAO,EAAE,cAAc;cACvBC,KAAK,EAAE,EAAE;cACTP,MAAM,EAAE,EAAE;cACVQ,EAAE,EAAE;YACN,CAAE;YAAAP,QAAA,EAEDrB,OAAO,CAAC6B,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACTvC,OAAA,CAAC5D,GAAG;YAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,IAAI;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEZ,EAAE,EAAE;cAAI,CAAE;cAAAJ,QAAA,EACvDrB,OAAO,CAAC6B;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACbvC,OAAA,CAACtD,IAAI;cACHgG,KAAK,EAAEtC,OAAO,CAACiB,MAAM,IAAI,UAAW;cACpCsB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEtB,cAAc,CAAClB,OAAO,CAACiB,MAAM;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvC,OAAA,CAACrD,UAAU;UAACkG,OAAO,EAAEhC,eAAgB;UAAAY,QAAA,eACnCzB,OAAA,CAAC9B,YAAY;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENvC,OAAA,CAAC3C,OAAO;QAACkE,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BvC,OAAA,CAACvD,IAAI;QAACsG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAvB,QAAA,gBACzBzB,OAAA,CAACvD,IAAI;UAACwG,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfzB,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDzB,OAAA,CAACpB,YAAY;cAAC2C,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNvC,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EAAC;UAErD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPvC,OAAA,CAACvD,IAAI;UAACwG,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfzB,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDzB,OAAA,CAACd,YAAY;cAACqC,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNvC,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDrB,OAAO,CAACgD,QAAQ,IAAI;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPvC,OAAA,CAACvD,IAAI;UAACwG,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfzB,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDzB,OAAA,CAAClB,YAAY;cAACyC,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNvC,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDrB,OAAO,CAACiD,UAAU,IAAIjD,OAAO,CAACiD,UAAU,KAAK,UAAU,GACpDjD,OAAO,CAACiD,UAAU,CAAClB,WAAW,CAAC,CAAC,GAChC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPvC,OAAA,CAAC5D,GAAG;UAAC6G,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACdzB,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDzB,OAAA,CAAChB,YAAY;cAACuC,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNvC,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDrB,OAAO,CAACkD,QAAQ,IAAI;UAAO;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEF,CAAC,eAEPvC,OAAA,CAAC5D,GAAG;QAACmF,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE7B,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,cAAc,EAAE;QAAgB,CAAE;QAAAF,QAAA,eACzFzB,OAAA,CAAC5D,GAAG;UAACmF,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE4B,GAAG,EAAE;UAAE,CAAE;UAAA/B,QAAA,gBACzDzB,OAAA,CAACZ,YAAY;YAAC+D,QAAQ,EAAC,OAAO;YAACP,KAAK,EAAC;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDvC,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAACI,KAAK,EAAC,gBAAgB;YAACrB,EAAE,EAAE;cAAE4B,QAAQ,EAAE;YAAU,CAAE;YAAA1B,QAAA,EAAC;UAEhF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZtB,UAAU,gBACTjB,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE4B,GAAG,EAAE;YAAI,CAAE;YAAA/B,QAAA,gBAC3DzB,OAAA,CAACR,eAAe;cAAC2D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAEvE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAENvC,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE4B,GAAG,EAAE;YAAI,CAAE;YAAA/B,QAAA,gBAC3DzB,OAAA,CAACV,SAAS;cAAC6D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAEvE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvC,OAAA,CAAC5D,GAAG;QAACmF,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE7B,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBAC1CzB,OAAA,CAACxD,MAAM;UACLmG,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,UAAU;UAClBiB,SAAS,eAAEzD,OAAA,CAACxB,QAAQ;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAMtC,MAAM,CAACH,OAAO,CAAE;UAC/BmB,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA,CAACxD,MAAM;UACLmG,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,MAAM;UACdiB,SAAS,eAAEzD,OAAA,CAAC5B,QAAQ;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAMxC,MAAM,CAACD,OAAO,CAAE;UAC/BmB,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENvC,OAAA,CAACzC,IAAI;QACHoD,QAAQ,EAAEA,QAAS;QACnBgD,IAAI,EAAEC,OAAO,CAACjD,QAAQ,CAAE;QACxBkD,OAAO,EAAE7C,eAAgB;QAAAS,QAAA,gBAEzBzB,OAAA,CAAC5C,QAAQ;UAACyF,OAAO,EAAEA,CAAA,KAAM;YAAExC,MAAM,CAACD,OAAO,CAAC;YAAEY,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBAC/DzB,OAAA,CAACxC,YAAY;YAAAiE,QAAA,eACXzB,OAAA,CAAC5B,QAAQ;cAAC+E,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfvC,OAAA,CAACvC,YAAY;YAAAgE,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACXvC,OAAA,CAAC5C,QAAQ;UAACyF,OAAO,EAAEA,CAAA,KAAM;YAAEtC,MAAM,CAACH,OAAO,CAAC;YAAEY,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBAC/DzB,OAAA,CAACxC,YAAY;YAAAiE,QAAA,eACXzB,OAAA,CAACxB,QAAQ;cAAC2E,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfvC,OAAA,CAACvC,YAAY;YAAAgE,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEXvC,OAAA,CAAC3C,OAAO;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAEV,CAACtB,UAAU,gBACVjB,OAAA,CAAC5C,QAAQ;UAACyF,OAAO,EAAEA,CAAA,KAAM;YAAErC,eAAe,CAACJ,OAAO,CAAC;YAAEY,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBACxEzB,OAAA,CAACxC,YAAY;YAAAiE,QAAA,eACXzB,OAAA,CAACZ,YAAY;cAAC+D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACfvC,OAAA,CAACvC,YAAY;YAAAgE,QAAA,EAAC;UAAiB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,gBAEXvC,OAAA,CAAC5C,QAAQ;UAACyF,OAAO,EAAEA,CAAA,KAAM;YAAEpC,uBAAuB,CAACL,OAAO,CAAC;YAAEY,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBAChFzB,OAAA,CAACxC,YAAY;YAAAiE,QAAA,eACXzB,OAAA,CAACR,eAAe;cAAC2D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACfvC,OAAA,CAACvC,YAAY;YAAAgE,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACX,eAEDvC,OAAA,CAAC3C,OAAO;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEXvC,OAAA,CAAC5C,QAAQ;UAACyF,OAAO,EAAEA,CAAA,KAAM;YAAEvC,QAAQ,CAACF,OAAO,CAAC;YAAEY,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBACjEzB,OAAA,CAACxC,YAAY;YAAAiE,QAAA,eACXzB,OAAA,CAAC1B,UAAU;cAAC6E,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACfvC,OAAA,CAACvC,YAAY;YAAAgE,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX;AAAC7B,EAAA,CA3MQP,WAAW;AAAA2D,EAAA,GAAX3D,WAAW;AA6MpB,SAAS4D,QAAQA,CAAA,EAAG;EAAAC,GAAA;EAClB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/H,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgI,cAAc,EAAEC,iBAAiB,CAAC,GAAGjI,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkI,eAAe,EAAEC,kBAAkB,CAAC,GAAGnI,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoI,QAAQ,EAAEC,WAAW,CAAC,GAAGrI,QAAQ,CAAC;IACvC8F,IAAI,EAAE,EAAE;IACRwC,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,WAAW;IAC9BvB,QAAQ,EAAE,KAAK;IACfE,QAAQ,EAAE,OAAO;IACjBsB,aAAa,EAAE,KAAK;IACpBvB,UAAU,EAAE,UAAU;IACtBwB,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGtF,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEuF;EAAY,CAAC,GAAGpF,MAAM,CAAC,CAAC;;EAEhC;EACA,MAAM;IAAEqF,IAAI,EAAEC,YAAY;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAG7F,QAAQ,CACvD,UAAU,EACV,MAAMI,WAAW,CAAC0F,MAAM,CAAC,CAAC,CAACC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACN,IAAI,CAAC,EAC1D;IACEO,SAAS,EAAGP,IAAI,IAAK;MACnB;MACA,MAAMQ,YAAY,GAAG,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,QAAQ,KAAIT,IAAI,IAAI,EAAE;MACjDD,WAAW,CAACS,YAAY,CAAC;IAC3B,CAAC;IACDE,OAAO,EAAGP,KAAK,IAAK;MAClB1F,KAAK,CAAC0F,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CACF,CAAC;;EAED;EACA,MAAMQ,cAAc,GAAGpG,WAAW,CAACG,WAAW,CAACkG,MAAM,EAAE;IACrDL,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzC9B,mBAAmB,CAAC,KAAK,CAAC;MAC1B+B,SAAS,CAAC,CAAC;MACXrG,KAAK,CAACsG,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAa,eAAA,EAAAC,oBAAA;MAClBC,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAa,eAAA,GAALb,KAAK,CAAEG,QAAQ,cAAAU,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAiBhB,IAAI,cAAAiB,oBAAA,uBAArBA,oBAAA,CAAuBG,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,0BAA0B;MAC9C5G,KAAK,CAAC0F,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CAAC,CAAC;;EAEF;EACA,MAAMG,cAAc,GAAG/G,WAAW,CAChC,CAAC;IAAEgH,EAAE;IAAEvB;EAAK,CAAC,KAAKtF,WAAW,CAAC8G,MAAM,CAACD,EAAE,EAAEvB,IAAI,CAAC,EAC9C;IACEO,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzC5B,iBAAiB,CAAC,KAAK,CAAC;MACxB6B,SAAS,CAAC,CAAC;MACXrG,KAAK,CAACsG,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAsB,gBAAA,EAAAC,qBAAA;MAClBR,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAsB,gBAAA,GAALtB,KAAK,CAAEG,QAAQ,cAAAmB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBzB,IAAI,cAAA0B,qBAAA,uBAArBA,qBAAA,CAAuBN,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,0BAA0B;MAC9C5G,KAAK,CAAC0F,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;;EAED;EACA,MAAMQ,cAAc,GAAGpH,WAAW,CAACG,WAAW,CAACkH,MAAM,EAAE;IACrDrB,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzCpG,KAAK,CAACsG,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAClB1F,KAAK,CAAC0F,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,CAAC;;EAIF;EACA,MAAM0B,iBAAiB,GAAGtH,WAAW,CAClCuH,SAAS,IAAKpH,WAAW,CAACqH,SAAS,CAACD,SAAS,CAAC,EAC/C;IACEvB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B,IAAIgC,MAAM,CAAC9F,MAAM,KAAK,SAAS,EAAE;QAC/BzB,KAAK,CAACsG,OAAO,CAAC,yCAAyCiB,MAAM,CAACC,aAAa,GAAG,CAAC;MACjF,CAAC,MAAM,IAAID,MAAM,CAAC9F,MAAM,KAAK,UAAU,EAAE;QACvCzB,KAAK,CAACyH,IAAI,CAAC,sCAAsC,CAAC;MACpD,CAAC,MAAM;QACLzH,KAAK,CAAC0F,KAAK,CAAC,sBAAsB6B,MAAM,CAACX,OAAO,EAAE,CAAC;MACrD;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAgC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAClBnB,OAAO,CAACf,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;;MAEzC;MACA,IAAI,CAAAA,KAAK,aAALA,KAAK,wBAAAgC,gBAAA,GAALhC,KAAK,CAAEG,QAAQ,cAAA6B,gBAAA,uBAAfA,gBAAA,CAAiBjG,MAAM,MAAK,GAAG,EAAE;QACnCzB,KAAK,CAAC6H,OAAO,CAAC,6EAA6E,CAAC;MAC9F,CAAC,MAAM,IAAI,CAAAnC,KAAK,aAALA,KAAK,wBAAAiC,gBAAA,GAALjC,KAAK,CAAEG,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBpC,IAAI,cAAAqC,qBAAA,uBAArBA,qBAAA,CAAuBhB,OAAO,MAAK,oBAAoB,EAAE;QAClE5G,KAAK,CAAC6H,OAAO,CAAC,wDAAwD,CAAC;MACzE,CAAC,MAAM;QAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACL,MAAMvB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAoC,gBAAA,GAALpC,KAAK,CAAEG,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBvC,IAAI,cAAAwC,qBAAA,uBAArBA,qBAAA,CAAuBpB,MAAM,MAC9BjB,KAAK,aAALA,KAAK,wBAAAsC,gBAAA,GAALtC,KAAK,CAAEG,QAAQ,cAAAmC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBzC,IAAI,cAAA0C,qBAAA,uBAArBA,qBAAA,CAAuBrB,OAAO,MAC9BlB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,mBAAmB;QACvC5G,KAAK,CAAC0F,KAAK,CAACgB,YAAY,CAAC;MAC3B;IACF;EACF,CACF,CAAC;;EAED;EACA,MAAMwB,qBAAqB,GAAGpI,WAAW,CACtCuH,SAAS,IAAKpH,WAAW,CAACkI,aAAa,CAACd,SAAS,CAAC,EACnD;IACEvB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B,IAAIgC,MAAM,CAAC9F,MAAM,KAAK,iBAAiB,EAAE;QACvCzB,KAAK,CAACsG,OAAO,CAAC,yEAAyE,CAAC;QACxF;QACA,MAAM8B,YAAY,GAAGb,MAAM,CAACa,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC;QACnDC,UAAU,CAAC,MAAM;UACftI,KAAK,CAACyH,IAAI,CAAC,wBAAwBW,YAAY,EAAE,EAAE;YAAEG,QAAQ,EAAE;UAAM,CAAC,CAAC;QACzE,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLvI,KAAK,CAAC0F,KAAK,CAAC,0BAA0B6B,MAAM,CAACX,OAAO,EAAE,CAAC;MACzD;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAA8C,gBAAA,EAAAC,qBAAA;MAClBhC,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAA8C,gBAAA,GAAL9C,KAAK,CAAEG,QAAQ,cAAA2C,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBjD,IAAI,cAAAkD,qBAAA,uBAArBA,qBAAA,CAAuB9B,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,gCAAgC;MACpD5G,KAAK,CAAC0F,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAMgC,6BAA6B,GAAG5I,WAAW,CAC/C,CAAC;IAAEuH,SAAS;IAAEsB;EAAa,CAAC,KAAK1I,WAAW,CAAC2I,qBAAqB,CAACvB,SAAS,EAAEsB,YAAY,CAAC,EAC3F;IACE7C,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B,IAAIgC,MAAM,CAAC9F,MAAM,KAAK,gBAAgB,EAAE;QACtCzB,KAAK,CAACsG,OAAO,CAAC,sCAAsCiB,MAAM,CAACsB,YAAY,iBAAiB,CAAC;QACzFxD,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MAC3C,CAAC,MAAM;QACLpG,KAAK,CAAC0F,KAAK,CAAC,6BAA6B6B,MAAM,CAACX,OAAO,EAAE,CAAC;MAC5D;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAoD,gBAAA,EAAAC,qBAAA;MAClBtC,OAAO,CAACf,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAoD,gBAAA,GAALpD,KAAK,CAAEG,QAAQ,cAAAiD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBvD,IAAI,cAAAwD,qBAAA,uBAArBA,qBAAA,CAAuBpC,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,mCAAmC;MACvD5G,KAAK,CAAC0F,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAMV,QAAQ,GAAGgD,KAAK,CAACC,OAAO,CAACzD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEQ,QAAQ,CAAC,GAClDR,YAAY,CAACQ,QAAQ,GACrBgD,KAAK,CAACC,OAAO,CAACzD,YAAY,CAAC,GAC3BA,YAAY,GACZ,EAAE;EAEN,MAAMa,SAAS,GAAGA,CAAA,KAAM;IACtBzB,WAAW,CAAC;MACVvC,IAAI,EAAE,EAAE;MACRwC,YAAY,EAAE,QAAQ;MACtBC,UAAU,EAAE,EAAE;MACdC,iBAAiB,EAAE,WAAW;MAC9BvB,QAAQ,EAAE,KAAK;MACfE,QAAQ,EAAE,OAAO;MACjBsB,aAAa,EAAE,KAAK;MACpBvB,UAAU,EAAE,UAAU;MACtBwB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFV,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMwE,YAAY,GAAGA,CAAA,KAAM;IACzB5E,mBAAmB,CAAC,IAAI,CAAC;IACzB+B,SAAS,CAAC,CAAC;EACb,CAAC;EAED,MAAM8C,UAAU,GAAI3I,OAAO,IAAK;IAC9BkE,kBAAkB,CAAClE,OAAO,CAAC;IAE3BoE,WAAW,CAAC;MACVvC,IAAI,EAAE7B,OAAO,CAAC6B,IAAI,IAAI,EAAE;MACxBwC,YAAY,EAAE,QAAQ;MAAE;MACxBC,UAAU,EAAEtE,OAAO,CAACsE,UAAU,IAAI,EAAE;MACpCC,iBAAiB,EAAEvE,OAAO,CAACuE,iBAAiB,IAAI,WAAW;MAC3DvB,QAAQ,EAAEhD,OAAO,CAACgD,QAAQ,IAAI,KAAK;MACnCE,QAAQ,EAAElD,OAAO,CAACkD,QAAQ,IAAI,OAAO;MACrCsB,aAAa,EAAExE,OAAO,CAACiD,UAAU,IAAIjD,OAAO,CAACiD,UAAU,KAAK,UAAU;MACtEA,UAAU,EAAEjD,OAAO,CAACiD,UAAU,IAAI,UAAU;MAC5CwB,UAAU,EAAEzE,OAAO,CAACyE,UAAU,IAAI,EAAE;MACpCC,UAAU,EAAE1E,OAAO,CAAC0E,UAAU,GAAG1E,OAAO,CAAC0E,UAAU,CAACkE,QAAQ,CAAC,CAAC,GAAG,EAAE;MACnEjE,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE,CAAE;IACtB,CAAC,CAAC;IACFZ,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM6E,YAAY,GAAI7I,OAAO,IAAK;IAChC,IAAI8I,MAAM,CAACC,OAAO,CAAC,4CAA4C/I,OAAO,CAAC6B,IAAI,IAAI,CAAC,EAAE;MAChF6E,cAAc,CAACsC,MAAM,CAAChJ,OAAO,CAACsG,EAAE,CAAC;IACnC;EACF,CAAC;EAED,MAAM2C,UAAU,GAAIjJ,OAAO,IAAK;IAC9B;IACA,MAAMkJ,SAAS,GAAGlJ,OAAO,CAACiD,UAAU,IAAIjD,OAAO,CAACiD,UAAU,KAAK,UAAU,GACrE,GAAGjD,OAAO,CAACiD,UAAU,CAAClB,WAAW,CAAC,CAAC,GAAG/B,OAAO,CAACyE,UAAU,GAAG,KAAKzE,OAAO,CAACyE,UAAU,IAAIzE,OAAO,CAAC0E,UAAU,GAAG,GAAG,EAAE,EAAE,GAClH,UAAU;;IAEd;IACA,MAAMyE,WAAW,GAAG,CAClB,YAAYnJ,OAAO,CAAC6B,IAAI,EAAE,EAC1B,WAAW7B,OAAO,CAACiB,MAAM,EAAE,EAC3B,UAAUiI,SAAS,EAAE,EACrB,aAAalJ,OAAO,CAACkD,QAAQ,IAAI,OAAO,EAAE,EAC1C,aAAalD,OAAO,CAACgD,QAAQ,IAAI,KAAK,EAAE,EACxC,YAAY,IAAIoG,IAAI,CAACpJ,OAAO,CAACqJ,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,CAChE,CAACzB,IAAI,CAAC,IAAI,CAAC;;IAEZ;IACArI,KAAK,CAACsG,OAAO,CAAC,yBAAyBqD,WAAW,EAAE,EAAE;MAAEpB,QAAQ,EAAE;IAAK,CAAC,CAAC;;IAEzE;IACA,IAAI/H,OAAO,CAACiD,UAAU,IAAIjD,OAAO,CAACiD,UAAU,KAAK,UAAU,IAAIjD,OAAO,CAACyE,UAAU,EAAE;MACjFqD,UAAU,CAAC,MAAM;QACflB,iBAAiB,CAACoC,MAAM,CAAChJ,OAAO,CAACsG,EAAE,CAAC;MACtC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMiD,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAMC,UAAU,GAAG;MAAE,GAAGrF;IAAS,CAAC;;IAElC;IACA,IAAIqF,UAAU,CAAC/E,UAAU,KAAK,EAAE,EAAE+E,UAAU,CAAC/E,UAAU,GAAG,IAAI;IAC9D,IAAI+E,UAAU,CAAC9E,UAAU,KAAK,EAAE,EAAE8E,UAAU,CAAC9E,UAAU,GAAG,IAAI;IAC9D,IAAI8E,UAAU,CAAC7E,cAAc,KAAK,EAAE,EAAE6E,UAAU,CAAC7E,cAAc,GAAG,IAAI;IACtE,IAAI6E,UAAU,CAAC5E,cAAc,KAAK,EAAE,EAAE4E,UAAU,CAAC5E,cAAc,GAAG,IAAI;IACtE,IAAI4E,UAAU,CAAClF,UAAU,KAAK,EAAE,EAAEkF,UAAU,CAAClF,UAAU,GAAG,IAAI;;IAE9D;IACA,IAAIkF,UAAU,CAAC9E,UAAU,EAAE;MACzB,MAAM+E,IAAI,GAAGC,QAAQ,CAACF,UAAU,CAAC9E,UAAU,CAAC;MAC5C,IAAIiF,KAAK,CAACF,IAAI,CAAC,EAAE;QACfjK,KAAK,CAAC0F,KAAK,CAAC,mCAAmC,CAAC;QAChD;MACF;MACAsE,UAAU,CAAC9E,UAAU,GAAG+E,IAAI;IAC9B;;IAEA;IACA,IAAItF,QAAQ,CAAClB,UAAU,KAAK,UAAU,EAAE;MACtC,IAAI,CAACkB,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,EAAE;QAChDlF,KAAK,CAAC0F,KAAK,CAAC,wDAAwD,CAAC;QACrE;MACF;MAEA,MAAMuE,IAAI,GAAGC,QAAQ,CAACvF,QAAQ,CAACO,UAAU,CAAC;MAC1C,IAAIiF,KAAK,CAACF,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,KAAK,EAAE;QAC3CjK,KAAK,CAAC0F,KAAK,CAAC,uDAAuD,CAAC;QACpE;MACF;IACF;;IAEA;IACA,MAAM0E,SAAS,GAAG;MAChB/H,IAAI,EAAE2H,UAAU,CAAC3H,IAAI;MACrByC,UAAU,EAAEkF,UAAU,CAAClF,UAAU;MACjCuF,cAAc,EAAE;QACdxF,YAAY,EAAEmF,UAAU,CAACnF,YAAY,IAAI,QAAQ;QACjDE,iBAAiB,EAAEiF,UAAU,CAACjF,iBAAiB,IAAI,WAAW;QAC9DvB,QAAQ,EAAEwG,UAAU,CAACxG,QAAQ,IAAI,KAAK;QACtCE,QAAQ,EAAEsG,UAAU,CAACtG,QAAQ,IAAI;MACnC,CAAC;MACD4G,YAAY,EAAEN,UAAU,CAACvG,UAAU,KAAK,UAAU,GAAG;QACnDA,UAAU,EAAEuG,UAAU,CAACvG,UAAU;QACjC8G,IAAI,EAAEP,UAAU,CAAC/E,UAAU;QAC3BgF,IAAI,EAAED,UAAU,CAAC9E,UAAU;QAC3BsF,QAAQ,EAAER,UAAU,CAAC7E,cAAc;QACnCsF,QAAQ,EAAET,UAAU,CAAC5E;MACvB,CAAC,GAAG;IACN,CAAC;IAED,IAAIX,eAAe,EAAE;MACnBoC,cAAc,CAAC2C,MAAM,CAAC;QAAE1C,EAAE,EAAErC,eAAe,CAACqC,EAAE;QAAEvB,IAAI,EAAE6E;MAAU,CAAC,CAAC;IACpE,CAAC,MAAM;MACLlE,cAAc,CAACsD,MAAM,CAACY,SAAS,CAAC;IAClC;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzChG,WAAW,CAACiG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAME,mBAAmB,GAAItK,OAAO,IAAK;IACvC0H,qBAAqB,CAACsB,MAAM,CAAChJ,OAAO,CAACsG,EAAE,CAAC;EAC1C,CAAC;EAED,MAAMiE,2BAA2B,GAAIvK,OAAO,IAAK;IAC/C;IACA;IACAkI,6BAA6B,CAACc,MAAM,CAAC;MACnCnC,SAAS,EAAE7G,OAAO,CAACsG,EAAE;MACrB6B,YAAY,EAAE;QACZqC,KAAK,EAAE,kBAAkB;QAAE;QAC3BR,QAAQ,EAAE,eAAe;QACzBS,OAAO,EAAE;MACX;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIvF,KAAK,EAAE;IACT,oBACEtF,OAAA,CAAC5D,GAAG;MAAC0O,SAAS,EAAC,SAAS;MAAArJ,QAAA,eACtBzB,OAAA,CAACtC,KAAK;QAACqN,QAAQ,EAAC,OAAO;QAACxJ,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAEvC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACEvC,OAAA,CAAC5D,GAAG;IAAC0O,SAAS,EAAC,SAAS;IAAArJ,QAAA,gBACtBzB,OAAA,CAAC5D,GAAG;MAACmF,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFzB,OAAA,CAAC3D,UAAU;QAACmG,OAAO,EAAC,IAAI;QAACjB,EAAE,EAAE;UAAEkB,UAAU,EAAE;QAAI,CAAE;QAAAhB,QAAA,EAAC;MAElD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvC,OAAA,CAAC5D,GAAG;QAACmF,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBACnCzB,OAAA,CAACxD,MAAM;UACLgG,OAAO,EAAC,UAAU;UAClBiB,SAAS,eAAEzD,OAAA,CAACtB,WAAW;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BM,OAAO,EAAEA,CAAA,KAAMoC,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAE;UACzDzE,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA,CAACxD,MAAM;UACLgG,OAAO,EAAC,WAAW;UACnBiB,SAAS,eAAEzD,OAAA,CAAClC,OAAO;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAEiG,YAAa;UACtBvH,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL8C,SAAS,iBAAIrF,OAAA,CAACrC,cAAc;MAAC4D,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE;IAAE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAI9CqD,QAAQ,CAACoF,MAAM,KAAK,CAAC,IAAI,CAAC3F,SAAS,gBAClCrF,OAAA,CAAC1D,IAAI;MAAAmF,QAAA,eACHzB,OAAA,CAACzD,WAAW;QAACgF,EAAE,EAAE;UAAE0J,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAzJ,QAAA,gBAC9CzB,OAAA,CAAChC,UAAU;UAACuD,EAAE,EAAE;YAAE4B,QAAQ,EAAE,EAAE;YAAEP,KAAK,EAAE,gBAAgB;YAAEf,EAAE,EAAE;UAAE;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpEvC,OAAA,CAAC3D,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACjB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAExC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvC,OAAA,CAAC3D,UAAU;UAACmG,OAAO,EAAC,OAAO;UAACI,KAAK,EAAC,gBAAgB;UAACrB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvC,OAAA,CAACxD,MAAM;UACLgG,OAAO,EAAC,WAAW;UACnBiB,SAAS,eAAEzD,OAAA,CAAClC,OAAO;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAEiG,YAAa;UACtBvH,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEPvC,OAAA,CAACvD,IAAI;MAACsG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvB,QAAA,EACxBmE,QAAQ,CAACuF,GAAG,CAAE/K,OAAO,iBACpBJ,OAAA,CAACvD,IAAI;QAACwG,IAAI;QAACC,EAAE,EAAE,EAAG;QAACkI,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5J,QAAA,eAC9BzB,OAAA,CAACG,WAAW;UACVC,OAAO,EAAEA,OAAQ;UACjBC,MAAM,EAAE0I,UAAW;UACnBzI,QAAQ,EAAE2I,YAAa;UACvB1I,MAAM,EAAE8I,UAAW;UACnB7I,eAAe,EAAEkK,mBAAoB;UACrCjK,uBAAuB,EAAEkK;QAA4B;UAAAvI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC,GARkCnC,OAAO,CAACsG,EAAE;QAAAtE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAS1C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDvC,OAAA,CAACpD,MAAM;MACL+G,IAAI,EAAEM,gBAAgB,IAAIE,cAAe;MACzCN,OAAO,EAAEA,CAAA,KAAM;QACbK,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,iBAAiB,CAAC,KAAK,CAAC;QACxB6B,SAAS,CAAC,CAAC;MACb,CAAE;MACFqF,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAA9J,QAAA,gBAETzB,OAAA,CAACnD,WAAW;QAAA4E,QAAA,EACT4C,eAAe,GAAG,cAAc,GAAG;MAAoB;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACdvC,OAAA,CAAClD,aAAa;QAAA2E,QAAA,eACZzB,OAAA,CAAC5D,GAAG;UAACmF,EAAE,EAAE;YAAEiK,EAAE,EAAE;UAAE,CAAE;UAAA/J,QAAA,eACjBzB,OAAA,CAACvD,IAAI;YAACsG,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAvB,QAAA,gBACzBzB,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBzB,OAAA,CAAChD,SAAS;gBACRuO,SAAS;gBACT7I,KAAK,EAAC,cAAc;gBACpB8H,KAAK,EAAEjG,QAAQ,CAACtC,IAAK;gBACrBwJ,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAAC,MAAM,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;gBAC1DoB,QAAQ;cAAA;gBAAAxJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkI,EAAE,EAAE,CAAE;cAAA3J,QAAA,eACvBzB,OAAA,CAAC/C,WAAW;gBAACsO,SAAS;gBAAA9J,QAAA,gBACpBzB,OAAA,CAAC9C,UAAU;kBAAAuE,QAAA,EAAC;gBAAY;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCvC,OAAA,CAAC7C,MAAM;kBACLqN,KAAK,EAAEjG,QAAQ,CAACE,YAAa;kBAC7BgH,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAAC,cAAc,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBAClE9H,KAAK,EAAC,cAAc;kBAAAjB,QAAA,gBAEpBzB,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,QAAQ;oBAAA/I,QAAA,EAAC;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,SAAS;oBAAA/I,QAAA,EAAC;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5CvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,MAAM;oBAAA/I,QAAA,EAAC;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkI,EAAE,EAAE,CAAE;cAAA3J,QAAA,eACvBzB,OAAA,CAAC/C,WAAW;gBAACsO,SAAS;gBAAA9J,QAAA,gBACpBzB,OAAA,CAAC9C,UAAU;kBAAAuE,QAAA,EAAC;gBAAiB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CvC,OAAA,CAAC7C,MAAM;kBACLqN,KAAK,EAAEjG,QAAQ,CAACI,iBAAkB;kBAClC8G,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAAC,mBAAmB,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBACvE9H,KAAK,EAAC,mBAAmB;kBAAAjB,QAAA,gBAEzBzB,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,WAAW;oBAAA/I,QAAA,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,UAAU;oBAAA/I,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,UAAU;oBAAA/I,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,UAAU;oBAAA/I,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkI,EAAE,EAAE,CAAE;cAAA3J,QAAA,eACvBzB,OAAA,CAAC/C,WAAW;gBAACsO,SAAS;gBAAA9J,QAAA,gBACpBzB,OAAA,CAAC9C,UAAU;kBAAAuE,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCvC,OAAA,CAAC7C,MAAM;kBACLqN,KAAK,EAAEjG,QAAQ,CAACnB,QAAS;kBACzBqI,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAAC,UAAU,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBAC9D9H,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhBzB,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,KAAK;oBAAA/I,QAAA,EAAC;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpCvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,kBAAkB;oBAAA/I,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9DvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,kBAAkB;oBAAA/I,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9DvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,eAAe;oBAAA/I,QAAA,EAAC;kBAAa;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkI,EAAE,EAAE,CAAE;cAAA3J,QAAA,eACvBzB,OAAA,CAAC/C,WAAW;gBAACsO,SAAS;gBAAA9J,QAAA,gBACpBzB,OAAA,CAAC9C,UAAU;kBAAAuE,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCvC,OAAA,CAAC7C,MAAM;kBACLqN,KAAK,EAAEjG,QAAQ,CAACjB,QAAS;kBACzBmI,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAAC,UAAU,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBAC9D9H,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhBzB,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,OAAO;oBAAA/I,QAAA,EAAC;kBAAY;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC/CvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,OAAO;oBAAA/I,QAAA,EAAC;kBAAU;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC7CvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,OAAO;oBAAA/I,QAAA,EAAC;kBAAoB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACvDvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,OAAO;oBAAA/I,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBzB,OAAA,CAAChD,SAAS;gBACRuO,SAAS;gBACT7I,KAAK,EAAC,uBAAuB;gBAC7B8H,KAAK,EAAEjG,QAAQ,CAACG,UAAW;gBAC3B+G,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAAC,YAAY,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;gBAChEqB,WAAW,EAAC;cAAsC;gBAAAzJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBzB,OAAA,CAAC3D,UAAU;gBAACmG,OAAO,EAAC,IAAI;gBAACjB,EAAE,EAAE;kBAAEM,EAAE,EAAE,CAAC;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAAC;cAEzD;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkI,EAAE,EAAE,CAAE;cAAA3J,QAAA,eACvBzB,OAAA,CAAC/C,WAAW;gBAACsO,SAAS;gBAAA9J,QAAA,gBACpBzB,OAAA,CAAC9C,UAAU;kBAAAuE,QAAA,EAAC;gBAAU;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCvC,OAAA,CAAC7C,MAAM;kBACLqN,KAAK,EAAEjG,QAAQ,CAAClB,UAAW;kBAC3BoI,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMI,SAAS,GAAGJ,CAAC,CAACC,MAAM,CAACnB,KAAK;oBAChCF,gBAAgB,CAAC,YAAY,EAAEwB,SAAS,CAAC;oBACzCxB,gBAAgB,CAAC,eAAe,EAAEwB,SAAS,KAAK,UAAU,CAAC;kBAC7D,CAAE;kBACFpJ,KAAK,EAAC,YAAY;kBAAAjB,QAAA,gBAElBzB,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,UAAU;oBAAA/I,QAAA,eACxBzB,OAAA,CAAC5D,GAAG;sBAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAwB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjEvC,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,MAAM;oBAAA/I,QAAA,eACpBzB,OAAA,CAAC5D,GAAG;sBAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC7CvC,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,OAAO;oBAAA/I,QAAA,eACrBzB,OAAA,CAAC5D,GAAG;sBAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAK;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC9CvC,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,QAAQ;oBAAA/I,QAAA,eACtBzB,OAAA,CAAC5D,GAAG;sBAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAM;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC/CvC,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXvC,OAAA,CAAC5C,QAAQ;oBAACoN,KAAK,EAAC,KAAK;oBAAA/I,QAAA,eACnBzB,OAAA,CAAC5D,GAAG;sBAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAG;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC5CvC,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACTvC,OAAA,CAACpC,cAAc;kBAAA6D,QAAA,GACZ8C,QAAQ,CAAClB,UAAU,KAAK,UAAU,IAAI,yBAAyB,EAC/DkB,QAAQ,CAAClB,UAAU,KAAK,MAAM,IAAI,iCAAiC,EACnEkB,QAAQ,CAAClB,UAAU,KAAK,OAAO,IAAI,sCAAsC,EACzEkB,QAAQ,CAAClB,UAAU,KAAK,QAAQ,IAAI,wCAAwC,EAC5EkB,QAAQ,CAAClB,UAAU,KAAK,KAAK,IAAI,4CAA4C;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAENgC,QAAQ,CAAClB,UAAU,KAAK,UAAU,iBACjCrD,OAAA,CAAAE,SAAA;cAAAuB,QAAA,gBACEzB,OAAA,CAACvD,IAAI;gBAACwG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAA3J,QAAA,eACvBzB,OAAA,CAAChD,SAAS;kBACRuO,SAAS;kBACT7I,KAAK,EAAC,YAAY;kBAClB8H,KAAK,EAAEjG,QAAQ,CAACM,UAAW;kBAC3B4G,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAAC,YAAY,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBAChEoB,QAAQ;kBACRC,WAAW,EACTtH,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC,0CACL;kBACD0I,UAAU,EAAC;gBAA+C;kBAAA3J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPvC,OAAA,CAACvD,IAAI;gBAACwG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAA3J,QAAA,eACvBzB,OAAA,CAAChD,SAAS;kBACRuO,SAAS;kBACT7I,KAAK,EAAC,YAAY;kBAClB8H,KAAK,EAAEjG,QAAQ,CAACO,UAAW;kBAC3B2G,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAAC,YAAY,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBAChEwB,IAAI,EAAC,QAAQ;kBACbJ,QAAQ;kBACRC,WAAW,EACTtH,QAAQ,CAAClB,UAAU,KAAK,MAAM,GAAG,kBAAkB,GACnDkB,QAAQ,CAAClB,UAAU,KAAK,OAAO,GAAG,iBAAiB,GACnDkB,QAAQ,CAAClB,UAAU,KAAK,QAAQ,GAAG,kBAAkB,GACrDkB,QAAQ,CAAClB,UAAU,KAAK,KAAK,GAAG,UAAU,GAAG,aAC9C;kBACD0I,UAAU,EAAE,UAAUxH,QAAQ,CAAClB,UAAU,CAAClB,WAAW,CAAC,CAAC,QAAS;kBAChE8J,UAAU,EAAE;oBAAEC,GAAG,EAAE,CAAC;oBAAEC,GAAG,EAAE;kBAAM;gBAAE;kBAAA/J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPvC,OAAA,CAACvD,IAAI;gBAACwG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAA3J,QAAA,eACvBzB,OAAA,CAAChD,SAAS;kBACRuO,SAAS;kBACT7I,KAAK,EAAE6B,QAAQ,CAAClB,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEmH,KAAK,EAAEjG,QAAQ,CAACQ,cAAe;kBAC/B0G,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAAC,gBAAgB,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBACpEqB,WAAW,EACTtH,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,iCAAiC,GACjC,2CACL;kBACD0I,UAAU,EACRxH,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,8BAA8B,GAC9B;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPvC,OAAA,CAACvD,IAAI;gBAACwG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAA3J,QAAA,eACvBzB,OAAA,CAAChD,SAAS;kBACRuO,SAAS;kBACT7I,KAAK,EAAE6B,QAAQ,CAAClB,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEmH,KAAK,EAAEjG,QAAQ,CAACS,cAAe;kBAC/ByG,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAAC,gBAAgB,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;kBACpEwB,IAAI,EAAC,UAAU;kBACfH,WAAW,EACTtH,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,gDAAgD,GAChD,2CACL;kBACD0I,UAAU,EACRxH,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPvC,OAAA,CAACvD,IAAI;gBAACwG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAzB,QAAA,eAChBzB,OAAA,CAAC5D,GAAG;kBAACmF,EAAE,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAE8B,GAAG,EAAE,CAAC;oBAAE5B,UAAU,EAAE;kBAAS,CAAE;kBAAAH,QAAA,gBACzDzB,OAAA,CAACxD,MAAM;oBACLgG,OAAO,EAAC,UAAU;oBAClBG,IAAI,EAAC,OAAO;oBACZE,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAIwB,eAAe,EAAE;wBACnB2C,iBAAiB,CAACoC,MAAM,CAAC/E,eAAe,CAACqC,EAAE,CAAC;sBAC9C,CAAC,MAAM;wBACL;wBACA,MAAM4C,SAAS,GAAG,GAAG/E,QAAQ,CAAClB,UAAU,CAAClB,WAAW,CAAC,CAAC,WAAWoC,QAAQ,CAACM,UAAU,IAAIN,QAAQ,CAACO,UAAU,EAAE;wBAC7G,MAAMsH,QAAQ,GAAG7H,QAAQ,CAACQ,cAAc,GAAG,WAAWR,QAAQ,CAACQ,cAAc,GAAG,GAAG,YAAY;wBAC/FnF,KAAK,CAACyH,IAAI,CAAC,yBAAyBiC,SAAS,GAAG8C,QAAQ,4CAA4C,EAAE;0BAAEjE,QAAQ,EAAE;wBAAK,CAAC,CAAC;sBAC3H;oBACF,CAAE;oBACFkE,QAAQ,EAAE,CAAC9H,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,IAAIkC,iBAAiB,CAAC3B,SAAU;oBACtF9D,EAAE,EAAE;sBAAEmC,aAAa,EAAE;oBAAO,CAAE;oBAAAjC,QAAA,EAE7BuF,iBAAiB,CAAC3B,SAAS,GAAG,YAAY,GAAGhB,eAAe,GAAG,iBAAiB,GAAG;kBAAgB;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC,eACTvC,OAAA,CAAC3D,UAAU;oBAACmG,OAAO,EAAC,SAAS;oBAACI,KAAK,EAAC,gBAAgB;oBAAAnB,QAAA,EAAC;kBAErD;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,eACP,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBvC,OAAA,CAACjD,aAAa;QAAA0E,QAAA,gBACZzB,OAAA,CAACxD,MAAM;UACLqG,OAAO,EAAEA,CAAA,KAAM;YACbqB,mBAAmB,CAAC,KAAK,CAAC;YAC1BE,iBAAiB,CAAC,KAAK,CAAC;YACxB6B,SAAS,CAAC,CAAC;UACb,CAAE;UAAAxE,QAAA,EACH;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA,CAACxD,MAAM;UACLgG,OAAO,EAAC,WAAW;UACnBK,OAAO,EAAE8G,YAAa;UACtB0C,QAAQ,EAAE,CAAC9H,QAAQ,CAACtC,IAAI,IAAI6D,cAAc,CAACT,SAAS,IAAIoB,cAAc,CAACpB,SAAU;UAAA5D,QAAA,EAEhF4C,eAAe,GAAG,QAAQ,GAAG;QAAQ;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACyB,GAAA,CAhsBQD,QAAQ;EAAA,QAmBKpE,cAAc,EACVG,MAAM,EAGmBL,QAAQ,EAgBlCC,WAAW,EAiBXA,WAAW,EAoBXA,WAAW,EAaRA,WAAW,EAiCPA,WAAW,EA0BHA,WAAW;AAAA;AAAA4M,GAAA,GApJ1CvI,QAAQ;AAksBjB,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAAwI,GAAA;AAAAC,YAAA,CAAAzI,EAAA;AAAAyI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}