<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profiles API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .profile-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            background: #f9f9f9;
        }
        .profile-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .profile-name {
            font-size: 18px;
            font-weight: bold;
        }
        .profile-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            text-transform: uppercase;
        }
        .status-active { background: #4caf50; color: white; }
        .status-inactive { background: #9e9e9e; color: white; }
        .profile-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }
        .detail-item {
            display: flex;
            flex-direction: column;
        }
        .detail-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        .detail-value {
            font-weight: 500;
        }
        .error {
            color: red;
            padding: 16px;
            background: #ffebee;
            border-radius: 4px;
            margin: 16px 0;
        }
        .loading {
            text-align: center;
            padding: 32px;
            color: #666;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 8px 4px;
        }
        button:hover {
            background: #1976d2;
        }
        .debug {
            background: #e3f2fd;
            padding: 16px;
            border-radius: 4px;
            margin: 16px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Facebook Automation - Profiles Test</h1>
    
    <div>
        <button onclick="loadProfiles()">Load Profiles</button>
        <button onclick="clearResults()">Clear</button>
    </div>

    <div id="debug" class="debug" style="display: none;"></div>
    <div id="loading" class="loading" style="display: none;">Loading profiles...</div>
    <div id="error" class="error" style="display: none;"></div>
    <div id="results"></div>

    <script>
        async function loadProfiles() {
            const debugDiv = document.getElementById('debug');
            const loadingDiv = document.getElementById('loading');
            const errorDiv = document.getElementById('error');
            const resultsDiv = document.getElementById('results');
            
            // Clear previous results
            clearResults();
            
            // Show loading
            loadingDiv.style.display = 'block';
            debugDiv.style.display = 'block';
            
            try {
                console.log('Fetching profiles from API...');
                debugDiv.innerHTML = 'Fetching profiles from http://localhost:8000/api/profiles...';
                
                const response = await fetch('http://localhost:8000/api/profiles');
                
                debugDiv.innerHTML += `<br>Response status: ${response.status}`;
                debugDiv.innerHTML += `<br>Response headers: ${JSON.stringify([...response.headers.entries()])}`;
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                debugDiv.innerHTML += `<br>Response data type: ${typeof data}`;
                debugDiv.innerHTML += `<br>Response is array: ${Array.isArray(data)}`;
                debugDiv.innerHTML += `<br>Response structure: ${JSON.stringify(Object.keys(data))}`;
                
                // Handle both array and object responses
                let profiles = [];
                if (Array.isArray(data)) {
                    profiles = data;
                } else if (data.profiles && Array.isArray(data.profiles)) {
                    profiles = data.profiles;
                    debugDiv.innerHTML += `<br>Found profiles array with ${profiles.length} items`;
                    debugDiv.innerHTML += `<br>Total: ${data.total}, Page: ${data.page}, Per page: ${data.per_page}`;
                } else {
                    throw new Error('Unexpected response format');
                }
                
                debugDiv.innerHTML += `<br>Final profiles count: ${profiles.length}`;
                
                // Hide loading
                loadingDiv.style.display = 'none';
                
                if (profiles.length === 0) {
                    resultsDiv.innerHTML = '<p>No profiles found.</p>';
                    return;
                }
                
                // Render profiles
                resultsDiv.innerHTML = `<h2>Found ${profiles.length} profiles:</h2>`;
                
                profiles.forEach(profile => {
                    const profileCard = createProfileCard(profile);
                    resultsDiv.appendChild(profileCard);
                });
                
            } catch (error) {
                console.error('Error loading profiles:', error);
                loadingDiv.style.display = 'none';
                errorDiv.style.display = 'block';
                errorDiv.innerHTML = `Error: ${error.message}`;
                debugDiv.innerHTML += `<br>ERROR: ${error.message}`;
            }
        }
        
        function createProfileCard(profile) {
            const card = document.createElement('div');
            card.className = 'profile-card';
            
            const proxyType = profile.proxy_type || 'No Proxy';
            const proxyDisplay = profile.proxy_type && profile.proxy_type !== 'no_proxy' 
                ? `${profile.proxy_type.toUpperCase()}${profile.proxy_host ? ` (${profile.proxy_host}:${profile.proxy_port})` : ''}`
                : 'No Proxy';
            
            card.innerHTML = `
                <div class="profile-header">
                    <div class="profile-name">${profile.name}</div>
                    <div class="profile-status status-${profile.status}">${profile.status}</div>
                </div>
                <div class="profile-details">
                    <div class="detail-item">
                        <div class="detail-label">Browser</div>
                        <div class="detail-value">Chrome</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Location</div>
                        <div class="detail-value">${profile.timezone || 'UTC'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Proxy</div>
                        <div class="detail-value">${proxyDisplay}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Language</div>
                        <div class="detail-value">${profile.language || 'en-US'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Created</div>
                        <div class="detail-value">${new Date(profile.created_at).toLocaleDateString()}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">ID</div>
                        <div class="detail-value">${profile.id}</div>
                    </div>
                </div>
            `;
            
            return card;
        }
        
        function clearResults() {
            document.getElementById('debug').style.display = 'none';
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            document.getElementById('results').innerHTML = '';
        }
        
        // Auto-load on page load
        window.addEventListener('load', loadProfiles);
    </script>
</body>
</html>
