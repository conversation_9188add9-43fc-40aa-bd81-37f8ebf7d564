"""
Profile management API endpoints.
"""
import logging
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from pydantic import BaseModel, Field, field_validator

from ..core.database import get_db
from ..core.security import security_manager
from ..models import Profile, ProxyConfig, ProfileStatus, ProxyType
from ..services.browser_service import BrowserService
from ..services.proxy_service import ProxyService
from ..services.facebook_service import FacebookService

router = APIRouter()
browser_service = BrowserService()
proxy_service = ProxyService()
facebook_service = FacebookService()
logger = logging.getLogger(__name__)


# Pydantic models for API
class ProxyConfigCreate(BaseModel):
    proxy_type: ProxyType = ProxyType.NO_PROXY
    host: Optional[str] = None
    port: Optional[int] = Field(None, ge=1, le=65535)
    username: Optional[str] = None
    password: Optional[str] = None

    @field_validator('port', mode='before')
    @classmethod
    def validate_port(cls, v):
        if v == "" or v is None:
            return None
        if isinstance(v, str):
            try:
                return int(v) if v.strip() else None
            except ValueError:
                raise ValueError("Port must be a valid integer")
        return v

    @field_validator('host', mode='before')
    @classmethod
    def validate_host(cls, v):
        if v == "":
            return None
        return v

    @field_validator('username', mode='before')
    @classmethod
    def validate_username(cls, v):
        if v == "":
            return None
        return v

    @field_validator('password', mode='before')
    @classmethod
    def validate_password(cls, v):
        if v == "":
            return None
        return v


class ProxyConfigResponse(BaseModel):
    id: int
    proxy_type: ProxyType
    host: Optional[str]
    port: Optional[int]
    username: Optional[str]
    is_working: bool
    validation_message: Optional[str] = None

    class Config:
        from_attributes = True


class BrowserConfigCreate(BaseModel):
    browser_type: str = "chrome"
    screen_resolution: str = "1920x1080"
    timezone: str = "UTC"
    language: str = "en-US"
    disable_images: bool = False
    disable_javascript: bool = False
    disable_webgl: bool = False


class ProfileCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    user_agent: Optional[str] = None
    browser_config: Optional[BrowserConfigCreate] = None
    proxy_config: Optional[ProxyConfigCreate] = None


class ProfileUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    status: Optional[ProfileStatus] = None
    user_agent: Optional[str] = None
    browser_config: Optional[BrowserConfigCreate] = None
    proxy_config: Optional[ProxyConfigCreate] = None


class ProfileResponse(BaseModel):
    id: int
    name: str
    status: ProfileStatus
    user_agent: Optional[str]
    profile_path: Optional[str]
    browser_config: Optional[str] = None
    created_at: str
    updated_at: str
    last_used_at: Optional[str]
    login_count: int
    message_sent_count: int
    proxy_config: Optional[ProxyConfigResponse]

    class Config:
        from_attributes = True


@router.get("/", response_model=List[ProfileResponse])
async def get_profiles(
    skip: int = 0,
    limit: int = 100,
    status: Optional[ProfileStatus] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get list of profiles with optional filtering."""
    query = select(Profile).options(selectinload(Profile.proxy_config))
    
    if status:
        query = query.where(Profile.status == status)
    
    query = query.offset(skip).limit(limit).order_by(Profile.created_at.desc())
    
    result = await db.execute(query)
    profiles = result.scalars().all()
    
    # Format datetime fields for response
    response_profiles = []
    for profile in profiles:
        profile_dict = {
            "id": profile.id,
            "name": profile.name,
            "status": profile.status,
            "user_agent": profile.user_agent,
            "profile_path": profile.profile_path,
            "created_at": profile.created_at.isoformat() if profile.created_at else None,
            "updated_at": profile.updated_at.isoformat() if profile.updated_at else None,
            "last_used_at": profile.last_used_at.isoformat() if profile.last_used_at else None,
            "login_count": profile.login_count,
            "message_sent_count": profile.message_sent_count,
            "proxy_config": profile.proxy_config
        }
        response_profiles.append(profile_dict)
    
    return response_profiles


@router.get("/{profile_id}", response_model=ProfileResponse)
async def get_profile(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Get specific profile by ID."""
    query = select(Profile).options(selectinload(Profile.proxy_config)).where(Profile.id == profile_id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()
    
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )
    
    return {
        "id": profile.id,
        "name": profile.name,
        "status": profile.status,
        "user_agent": profile.user_agent,
        "profile_path": profile.profile_path,
        "created_at": profile.created_at.isoformat() if profile.created_at else None,
        "updated_at": profile.updated_at.isoformat() if profile.updated_at else None,
        "last_used_at": profile.last_used_at.isoformat() if profile.last_used_at else None,
        "login_count": profile.login_count,
        "message_sent_count": profile.message_sent_count,
        "proxy_config": profile.proxy_config
    }


@router.post("/", response_model=ProfileResponse, status_code=status.HTTP_201_CREATED)
async def create_profile(profile_data: ProfileCreate, db: AsyncSession = Depends(get_db)):
    """Create new profile with browser and proxy configuration."""
    # Check if name already exists
    existing = await db.execute(select(Profile).where(Profile.name == profile_data.name))
    if existing.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Profile name already exists"
        )

    # Prepare browser configuration
    browser_config = {}
    if profile_data.browser_config:
        browser_config = profile_data.browser_config.dict()

    # Create profile
    profile = Profile(
        name=profile_data.name,
        status=ProfileStatus.ACTIVE,
        user_agent=profile_data.user_agent,
        browser_config=browser_config if browser_config else None
    )

    # Set browser config using the model method
    if browser_config:
        profile.set_browser_config(browser_config)

    db.add(profile)
    await db.flush()  # Get profile ID

    # Create profile directory using browser service
    browser_config_dict = browser_service.create_browser_config(profile)
    profile.profile_path = browser_config_dict["profile_path"]

    # Create proxy config if provided
    proxy_config = None
    if profile_data.proxy_config:
        proxy_data = profile_data.proxy_config

        # Validate proxy configuration
        temp_proxy = ProxyConfig(
            profile_id=profile.id,
            proxy_type=proxy_data.proxy_type,
            host=proxy_data.host,
            port=proxy_data.port
        )

        is_valid, validation_message = temp_proxy.validate_proxy_config()
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid proxy configuration: {validation_message}"
            )

        # Encrypt sensitive data
        encrypted_username = None
        encrypted_password = None

        if proxy_data.username:
            encrypted_username = security_manager.encrypt_string(proxy_data.username)
        if proxy_data.password:
            encrypted_password = security_manager.encrypt_string(proxy_data.password)

        proxy_config = ProxyConfig(
            profile_id=profile.id,
            proxy_type=proxy_data.proxy_type,
            host=proxy_data.host,
            port=proxy_data.port,
            username=encrypted_username,
            password=encrypted_password
        )

        db.add(proxy_config)

    await db.commit()

    # Save browser configuration file
    try:
        final_config = browser_service.create_browser_config(profile)
        browser_service.save_browser_config(profile, final_config)
    except Exception as e:
        # Log error but don't fail the profile creation
        pass

    # Return created profile
    return await get_profile(profile.id, db)


@router.put("/{profile_id}", response_model=ProfileResponse)
async def update_profile(
    profile_id: str,
    profile_data: ProfileUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update existing profile."""
    # Get profile
    query = select(Profile).options(selectinload(Profile.proxy_config)).where(Profile.id == profile_id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()
    
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )
    
    # Update profile fields
    if profile_data.name is not None:
        # Check name uniqueness
        existing = await db.execute(
            select(Profile).where(Profile.name == profile_data.name, Profile.id != profile_id)
        )
        if existing.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Profile name already exists"
            )
        profile.name = profile_data.name
    
    if profile_data.status is not None:
        profile.status = profile_data.status
    
    if profile_data.user_agent is not None:
        profile.user_agent = profile_data.user_agent
    
    # Update proxy config
    if profile_data.proxy_config is not None:
        if profile.proxy_config:
            # Update existing proxy config
            proxy_data = profile_data.proxy_config
            profile.proxy_config.proxy_type = proxy_data.proxy_type
            profile.proxy_config.host = proxy_data.host
            profile.proxy_config.port = proxy_data.port
            
            if proxy_data.username:
                profile.proxy_config.username = security_manager.encrypt_string(proxy_data.username)
            if proxy_data.password:
                profile.proxy_config.password = security_manager.encrypt_string(proxy_data.password)
        else:
            # Create new proxy config
            proxy_data = profile_data.proxy_config
            
            encrypted_username = None
            encrypted_password = None
            
            if proxy_data.username:
                encrypted_username = security_manager.encrypt_string(proxy_data.username)
            if proxy_data.password:
                encrypted_password = security_manager.encrypt_string(proxy_data.password)
            
            proxy_config = ProxyConfig(
                profile_id=profile.id,
                proxy_type=proxy_data.proxy_type,
                host=proxy_data.host,
                port=proxy_data.port,
                username=encrypted_username,
                password=encrypted_password
            )
            
            db.add(proxy_config)
    
    await db.commit()
    
    return await get_profile(profile.id, db)


@router.delete("/{profile_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_profile(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Delete profile."""
    query = select(Profile).where(Profile.id == profile_id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )
    
    await db.delete(profile)
    await db.commit()


@router.post("/{profile_id}/test-proxy")
async def test_proxy_connection(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Test proxy connection for profile."""
    query = select(Profile).options(selectinload(Profile.proxy_config)).where(Profile.id == profile_id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )

    if not profile.proxy_config:
        return {"status": "no_proxy", "message": "No proxy configured"}

    # Test proxy connection using proxy service
    try:
        test_result = await proxy_service.test_proxy_connection(profile.proxy_config)

        # Update proxy config status
        profile.proxy_config.is_working = test_result.get("working", False)
        profile.proxy_config.last_tested_at = datetime.utcnow()
        await db.commit()

        # Add additional information
        test_result["proxy_url"] = profile.proxy_config.get_proxy_url()
        test_result["proxy_config"] = profile.proxy_config.get_browser_proxy_config()
        test_result["recommendations"] = proxy_service.get_proxy_recommendations(profile.proxy_config.proxy_type)

        return test_result

    except Exception as e:
        return {
            "status": "error",
            "message": f"Proxy test failed: {str(e)}",
            "proxy_url": profile.proxy_config.get_proxy_url() if profile.proxy_config else None,
            "working": False
        }


@router.post("/{profile_id}/test-browser")
async def test_browser_configuration(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Test browser configuration for antidetect browser."""
    query = select(Profile).options(selectinload(Profile.proxy_config)).where(Profile.id == profile_id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )

    try:
        # Create browser configuration
        browser_config = browser_service.create_browser_config(profile)

        # Validate configuration
        is_valid, validation_message = browser_service.validate_browser_config(browser_config)

        if not is_valid:
            return {
                "status": "error",
                "message": validation_message,
                "config": browser_config
            }

        # Get zendriver configuration
        zendriver_config = browser_service.get_zendriver_config(profile)

        return {
            "status": "success",
            "message": "Browser configuration is valid and ready for antidetect browser",
            "browser_config": browser_config,
            "zendriver_config": zendriver_config
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Error testing browser configuration: {str(e)}"
        }


@router.get("/{profile_id}/zendriver-config")
async def get_zendriver_config(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Get zendriver configuration for profile."""
    query = select(Profile).options(selectinload(Profile.proxy_config)).where(Profile.id == profile_id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )

    try:
        # Get zendriver configuration
        zendriver_config = browser_service.get_zendriver_config(profile)
        browser_config = browser_service.create_browser_config(profile)

        return {
            "profile_id": profile.id,
            "profile_name": profile.name,
            "zendriver_config": zendriver_config,
            "browser_config": browser_config,
            "ready_for_automation": True
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating zendriver configuration: {str(e)}"
        )


@router.get("/proxy-types")
async def get_proxy_types():
    """Get available proxy types and their recommendations."""
    proxy_types = {}

    for proxy_type in ProxyType:
        proxy_types[proxy_type.value] = {
            "name": proxy_type.value.replace("_", " ").title(),
            "value": proxy_type.value,
            "recommendations": proxy_service.get_proxy_recommendations(proxy_type)
        }

    return {
        "proxy_types": proxy_types,
        "default": ProxyType.NO_PROXY.value
    }


@router.post("/{profile_id}/facebook-login")
async def facebook_login(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Initiate Facebook login for profile using antidetect browser."""
    query = select(Profile).options(selectinload(Profile.proxy_config)).where(Profile.id == profile_id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )

    try:
        # Use Facebook service to initiate login
        login_result = await facebook_service.initiate_login(profile)

        if login_result["status"] == "error":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=login_result["message"]
            )

        # Update profile status to indicate login in progress
        profile.status = ProfileStatus.ACTIVE
        profile.last_used_at = datetime.utcnow()
        await db.commit()

        return {
            "status": login_result["status"],
            "message": login_result["message"],
            "profile_id": profile.id,
            "profile_name": profile.name,
            "login_url": "https://www.facebook.com/login",
            "instructions": login_result.get("instructions", []),
            "browser_config": login_result.get("session_data", {}).get("browser_config"),
            "zendriver_config": login_result.get("session_data", {}).get("zendriver_config")
        }

    except Exception as e:
        logger.error(f"Error initiating Facebook login for profile {profile_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate Facebook login: {str(e)}"
        )


@router.post("/{profile_id}/facebook-login-complete")
async def facebook_login_complete(
    profile_id: str,
    facebook_data: dict = None,
    db: AsyncSession = Depends(get_db)
):
    """Mark Facebook login as complete and update profile status."""
    query = select(Profile).where(Profile.id == profile_id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )

    try:
        # Use Facebook service to complete login
        complete_result = await facebook_service.complete_login(profile, facebook_data)

        if complete_result["status"] == "error":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=complete_result["message"]
            )

        # Update profile with Facebook login information
        extracted_data = complete_result.get("facebook_data", {})
        if extracted_data:
            if extracted_data.get('email'):
                profile.facebook_email = extracted_data['email']
            if extracted_data.get('user_id'):
                profile.facebook_user_id = extracted_data['user_id']
            if extracted_data.get('username'):
                profile.facebook_username = extracted_data['username']

        # Update profile status to ready
        profile.status = ProfileStatus.ACTIVE
        profile.last_used_at = datetime.utcnow()
        profile.login_count = (profile.login_count or 0) + 1

        await db.commit()

        return {
            "status": "login_complete",
            "message": "Facebook login completed successfully. Profile is now ready for automation.",
            "profile_id": profile.id,
            "profile_name": profile.name,
            "profile_status": profile.status.value,
            "login_count": profile.login_count,
            "facebook_email": profile.facebook_email,
            "facebook_username": profile.facebook_username
        }

    except Exception as e:
        logger.error(f"Error completing Facebook login for profile {profile_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to complete Facebook login: {str(e)}"
        )


@router.get("/{profile_id}/facebook-status")
async def get_facebook_status(profile_id: str, db: AsyncSession = Depends(get_db)):
    """Get Facebook login status for profile."""
    query = select(Profile).where(Profile.id == profile_id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )

    is_logged_in = bool(profile.facebook_email or profile.facebook_user_id)

    return {
        "profile_id": profile.id,
        "profile_name": profile.name,
        "is_logged_in": is_logged_in,
        "facebook_email": profile.facebook_email,
        "facebook_username": profile.facebook_username,
        "facebook_user_id": profile.facebook_user_id,
        "login_count": profile.login_count or 0,
        "last_used": profile.last_used_at.isoformat() if profile.last_used_at else None,
        "status": profile.status.value
    }
