{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, Button, Grid, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Divider, Avatar, Menu, ListItemIcon, ListItemText, Alert, LinearProgress, FormHelperText } from '@mui/material';\nimport { Add as AddIcon, Person as PersonIcon, MoreVert as MoreVertIcon, Edit as EditIcon, Delete as DeleteIcon, PlayArrow as PlayIcon, Refresh as RefreshIcon, Computer as ComputerIcon, Security as SecurityIcon, Language as LanguageIcon, LocationOn as LocationIcon, Facebook as FacebookIcon, Login as LoginIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProfileCard({\n  profile,\n  onEdit,\n  onDelete,\n  onTest,\n  onFacebookLogin,\n  onFacebookLoginComplete\n}) {\n  _s();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const handleMenuClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const isLoggedIn = profile.facebook_email || profile.facebook_user_id;\n  const isReady = profile.status === 'active' && isLoggedIn;\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'flex-start',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: 'primary.main',\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            children: profile.name.charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                mb: 0.5\n              },\n              children: profile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: profile.status || 'inactive',\n              size: \"small\",\n              color: getStatusColor(profile.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleMenuClick,\n          children: /*#__PURE__*/_jsxDEV(MoreVertIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(ComputerIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"Chrome\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LocationIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.timezone || 'UTC'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Proxy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.proxy_type && profile.proxy_type !== 'no_proxy' ? profile.proxy_type.toUpperCase() : 'No Proxy'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.language || 'en-US'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FacebookIcon, {\n            fontSize: \"small\",\n            color: \"action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              fontSize: '0.75rem'\n            },\n            children: \"Facebook:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), isLoggedIn ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#4caf50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 500,\n                color: '#4caf50'\n              },\n              children: \"Logged In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(LoginIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#ff9800'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 500,\n                color: '#ff9800'\n              },\n              children: \"Not Logged In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 24\n          }, this),\n          onClick: () => onTest(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"text\",\n          startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 24\n          }, this),\n          onClick: () => onEdit(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        anchorEl: anchorEl,\n        open: Boolean(anchorEl),\n        onClose: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onEdit(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(EditIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Edit Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onTest(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(PlayIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Test Browser\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), !isLoggedIn ? /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLogin(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(FacebookIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#1877f2'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Login to Facebook\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLoginComplete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#4caf50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Complete Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onDelete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Delete Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n}\n_s(ProfileCard, \"+aMDa7FPcESUyQDF1vq0RSMn4/k=\");\n_c = ProfileCard;\nfunction Profiles() {\n  _s2();\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [loginInstructionsOpen, setLoginInstructionsOpen] = useState(false);\n  const [loginInstructions, setLoginInstructions] = useState([]);\n  const [loginStatusPolling, setLoginStatusPolling] = useState(new Map()); // profileId -> intervalId\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: ''\n  });\n  const queryClient = useQueryClient();\n  const {\n    setProfiles\n  } = useApp();\n\n  // Fetch profiles\n  const {\n    data: profilesData,\n    isLoading,\n    error\n  } = useQuery('profiles', () => profilesAPI.getAll().then(response => response.data), {\n    onSuccess: data => {\n      // Handle both array and object with profiles property\n      const profilesList = (data === null || data === void 0 ? void 0 : data.profiles) || data || [];\n      setProfiles(profilesList);\n    },\n    onError: error => {\n      toast.error('Failed to load profiles');\n    }\n  });\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      console.error('Create profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to create profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(({\n    id,\n    data\n  }) => profilesAPI.update(id, data), {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setEditDialogOpen(false);\n      resetForm();\n      toast.success('Profile updated successfully');\n    },\n    onError: error => {\n      var _error$response2, _error$response2$data;\n      console.error('Update profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to update profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: error => {\n      toast.error('Failed to delete profile');\n    }\n  });\n\n  // Test proxy mutation\n  const testProxyMutation = useMutation(profileId => profilesAPI.testProxy(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'success') {\n        toast.success(`Proxy test successful! Response time: ${result.response_time}s`);\n      } else if (result.status === 'no_proxy') {\n        toast.info('No proxy configured for this profile');\n      } else {\n        toast.error(`Proxy test failed: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response3, _error$response4, _error$response4$data;\n      console.error('Proxy test error:', error);\n\n      // Handle specific error cases\n      if ((error === null || error === void 0 ? void 0 : (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 404) {\n        toast.warning('Proxy test endpoint not available. This feature may not be implemented yet.');\n      } else if ((error === null || error === void 0 ? void 0 : (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) === \"Endpoint not found\") {\n        toast.warning('Test feature not available in current backend version.');\n      } else {\n        var _error$response5, _error$response5$data, _error$response6, _error$response6$data;\n        const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || (error === null || error === void 0 ? void 0 : (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || (error === null || error === void 0 ? void 0 : error.message) || 'Proxy test failed';\n        toast.error(errorMessage);\n      }\n    }\n  });\n\n  // Facebook login mutations\n  const facebookLoginMutation = useMutation(profileId => profilesAPI.facebookLogin(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'login_initiated' || result.status === 'browser_launched') {\n        toast.success('Antidetect browser launched! Complete Facebook login manually in the browser window.');\n        // Show instructions dialog\n        setLoginInstructions(result.instructions || []);\n        setLoginInstructionsOpen(true);\n\n        // Start polling for login status\n        startLoginStatusPolling(result.profile_id);\n      } else if (result.status === 'session_exists') {\n        toast.info('Browser session already active. Complete login in the existing browser window.');\n        setLoginInstructions([\"Browser session is already running\", \"Complete Facebook login in the existing browser window\", \"Click 'Complete Login' button when done\"]);\n        setLoginInstructionsOpen(true);\n      } else {\n        toast.error(`Failed to start login: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response7, _error$response7$data;\n      console.error('Facebook login error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to start Facebook login';\n      toast.error(errorMessage);\n    }\n  });\n  const facebookLoginCompleteMutation = useMutation(({\n    profileId,\n    facebookData\n  }) => profilesAPI.facebookLoginComplete(profileId, facebookData), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'login_complete') {\n        toast.success(`Facebook login completed! Profile \"${result.profile_name}\" is now ready.`);\n        queryClient.invalidateQueries('profiles');\n      } else {\n        toast.error(`Failed to complete login: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response8, _error$response8$data;\n      console.error('Facebook login complete error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to complete Facebook login';\n      toast.error(errorMessage);\n    }\n  });\n  const facebookLoginTerminateMutation = useMutation(profileId => profilesAPI.facebookLoginTerminate(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      toast.success('Browser session terminated successfully.');\n      stopLoginStatusPolling(result.profile_id);\n      setLoginInstructionsOpen(false);\n    },\n    onError: error => {\n      var _error$response9, _error$response9$data;\n      console.error('Facebook login terminate error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to terminate browser session';\n      toast.error(errorMessage);\n    }\n  });\n  const profiles = Array.isArray(profilesData === null || profilesData === void 0 ? void 0 : profilesData.profiles) ? profilesData.profiles : Array.isArray(profilesData) ? profilesData : [];\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: ''\n    });\n    setSelectedProfile(null);\n  };\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n  const handleEdit = profile => {\n    setSelectedProfile(profile);\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome',\n      // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '',\n      // Not in current API response\n      proxy_password: '' // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n  const handleDelete = profile => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n  const handleTest = profile => {\n    // Show profile information\n    const proxyInfo = profile.proxy_type && profile.proxy_type !== 'no_proxy' ? `${profile.proxy_type.toUpperCase()}${profile.proxy_host ? ` (${profile.proxy_host}:${profile.proxy_port})` : ''}` : 'No Proxy';\n\n    // Create a detailed info message\n    const profileInfo = [`Profile: ${profile.name}`, `Status: ${profile.status}`, `Proxy: ${proxyInfo}`, `Language: ${profile.language || 'en-US'}`, `Timezone: ${profile.timezone || 'UTC'}`, `Created: ${new Date(profile.created_at).toLocaleDateString()}`].join('\\n');\n\n    // Show info first\n    toast.success(`Profile Information:\\n${profileInfo}`, {\n      duration: 5000\n    });\n\n    // Try to test proxy if configured\n    if (profile.proxy_type && profile.proxy_type !== 'no_proxy' && profile.proxy_host) {\n      setTimeout(() => {\n        testProxyMutation.mutate(profile.id);\n      }, 1000);\n    }\n  };\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = {\n      ...formData\n    };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n    if (selectedProfile) {\n      updateMutation.mutate({\n        id: selectedProfile.id,\n        data: finalData\n      });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleFacebookLogin = profile => {\n    facebookLoginMutation.mutate(profile.id);\n  };\n\n  // Login status polling functions\n  const startLoginStatusPolling = profileId => {\n    // Clear any existing polling for this profile\n    stopLoginStatusPolling(profileId);\n    const intervalId = setInterval(async () => {\n      try {\n        const response = await profilesAPI.facebookLoginStatus(profileId);\n        const status = response.data.login_status;\n        if (status.status === 'browser_closed' || status.status === 'expired') {\n          toast.warning('Browser session ended. Please restart login process.');\n          stopLoginStatusPolling(profileId);\n        } else if (status.status === 'no_session') {\n          stopLoginStatusPolling(profileId);\n        }\n        // Continue polling if status is 'in_progress' or 'active'\n      } catch (error) {\n        console.error('Error polling login status:', error);\n        // Don't stop polling on error, might be temporary\n      }\n    }, 10000); // Poll every 10 seconds\n\n    setLoginStatusPolling(prev => new Map(prev.set(profileId, intervalId)));\n  };\n  const stopLoginStatusPolling = profileId => {\n    const intervalId = loginStatusPolling.get(profileId);\n    if (intervalId) {\n      clearInterval(intervalId);\n      setLoginStatusPolling(prev => {\n        const newMap = new Map(prev);\n        newMap.delete(profileId);\n        return newMap;\n      });\n    }\n  };\n\n  // Cleanup polling on component unmount\n  React.useEffect(() => {\n    return () => {\n      loginStatusPolling.forEach(intervalId => {\n        clearInterval(intervalId);\n      });\n    };\n  }, [loginStatusPolling]);\n  const handleFacebookLoginComplete = profile => {\n    // Stop polling for this profile\n    stopLoginStatusPolling(profile.id);\n\n    // For now, complete without additional data\n    // In a real implementation, you might collect Facebook user data\n    facebookLoginCompleteMutation.mutate({\n      profileId: profile.id,\n      facebookData: {\n        email: \"<EMAIL>\",\n        // This would be extracted from browser\n        username: \"facebook_user\",\n        user_id: \"123456789\"\n      }\n    });\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      className: \"fade-in\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        children: \"Failed to load profiles. Please check your connection to the backend.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 674,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 673,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 700\n        },\n        children: \"Profile Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 24\n          }, this),\n          onClick: () => queryClient.invalidateQueries('profiles'),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 24\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"New Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 683,\n      columnNumber: 7\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(LinearProgress, {\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 707,\n      columnNumber: 21\n    }, this), profiles.length === 0 && !isLoading ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center',\n          py: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n          sx: {\n            fontSize: 64,\n            color: 'text.secondary',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1\n          },\n          children: \"No Profiles Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 3\n          },\n          children: \"Create your first browser profile to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 26\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Create Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: profiles.map(profile => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(ProfileCard, {\n          profile: profile,\n          onEdit: handleEdit,\n          onDelete: handleDelete,\n          onTest: handleTest,\n          onFacebookLogin: handleFacebookLogin,\n          onFacebookLoginComplete: handleFacebookLoginComplete\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 15\n        }, this)\n      }, profile.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 734,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: createDialogOpen || editDialogOpen,\n      onClose: () => {\n        setCreateDialogOpen(false);\n        setEditDialogOpen(false);\n        resetForm();\n      },\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: selectedProfile ? 'Edit Profile' : 'Create New Profile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Profile Name\",\n                value: formData.name,\n                onChange: e => handleFormChange('name', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Browser Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.browser_type,\n                  onChange: e => handleFormChange('browser_type', e.target.value),\n                  label: \"Browser Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"chrome\",\n                    children: \"Chrome\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"firefox\",\n                    children: \"Firefox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"edge\",\n                    children: \"Edge\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 785,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 778,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Screen Resolution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.screen_resolution,\n                  onChange: e => handleFormChange('screen_resolution', e.target.value),\n                  label: \"Screen Resolution\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1920x1080\",\n                    children: \"1920x1080\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1366x768\",\n                    children: \"1366x768\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1440x900\",\n                    children: \"1440x900\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1280x720\",\n                    children: \"1280x720\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Timezone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.timezone,\n                  onChange: e => handleFormChange('timezone', e.target.value),\n                  label: \"Timezone\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"UTC\",\n                    children: \"UTC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Asia/Ho_Chi_Minh\",\n                    children: \"Asia/Ho_Chi_Minh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 815,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"America/New_York\",\n                    children: \"America/New_York\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Europe/London\",\n                    children: \"Europe/London\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.language,\n                  onChange: e => handleFormChange('language', e.target.value),\n                  label: \"Language\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"en-US\",\n                    children: \"English (US)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"vi-VN\",\n                    children: \"Vietnamese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"zh-CN\",\n                    children: \"Chinese (Simplified)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ja-JP\",\n                    children: \"Japanese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"User Agent (Optional)\",\n                value: formData.user_agent,\n                onChange: e => handleFormChange('user_agent', e.target.value),\n                placeholder: \"Leave empty for automatic generation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 600\n                },\n                children: \"Proxy Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Proxy Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.proxy_type,\n                  onChange: e => {\n                    const proxyType = e.target.value;\n                    handleFormChange('proxy_type', proxyType);\n                    handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                  },\n                  label: \"Proxy Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"no_proxy\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"No Proxy (Local Network)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 868,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Direct connection without proxy\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 869,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"http\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTP\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 876,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Standard HTTP proxy (ports: 8080, 3128, 8888)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 877,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 875,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 874,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"https\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTPS\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 884,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Encrypted HTTPS proxy (ports: 8080, 3128, 443)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 885,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 883,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 882,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"socks5\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SOCKS5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 892,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"High anonymity proxy (ports: 1080, 1081, 9050)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 893,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 891,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ssh\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SSH\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 900,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Secure SSH tunnel (ports: 22, 2222)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 901,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 899,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 898,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 857,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: [formData.proxy_type === 'no_proxy' && 'Using direct connection', formData.proxy_type === 'http' && 'Fast, suitable for web browsing', formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data', formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols', formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 15\n            }, this), formData.proxy_type !== 'no_proxy' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Host\",\n                  value: formData.proxy_host,\n                  onChange: e => handleFormChange('proxy_host', e.target.value),\n                  required: true,\n                  placeholder: formData.proxy_type === 'ssh' ? \"e.g., ssh.example.com or *************\" : \"e.g., proxy.example.com or *************\",\n                  helperText: \"IP address or domain name of the proxy server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Port\",\n                  value: formData.proxy_port,\n                  onChange: e => handleFormChange('proxy_port', e.target.value),\n                  type: \"number\",\n                  required: true,\n                  placeholder: formData.proxy_type === 'http' ? \"8080, 3128, 8888\" : formData.proxy_type === 'https' ? \"8080, 3128, 443\" : formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" : formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\",\n                  helperText: `Common ${formData.proxy_type.toUpperCase()} ports`,\n                  inputProps: {\n                    min: 1,\n                    max: 65535\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 935,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\",\n                  value: formData.proxy_username,\n                  onChange: e => handleFormChange('proxy_username', e.target.value),\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH username for authentication\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Required for SSH connections\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\",\n                  value: formData.proxy_password,\n                  onChange: e => handleFormChange('proxy_password', e.target.value),\n                  type: \"password\",\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH password or leave empty for key-based auth\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Password or private key authentication\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2,\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    onClick: () => {\n                      if (selectedProfile) {\n                        testProxyMutation.mutate(selectedProfile.id);\n                      } else {\n                        // Test proxy configuration without saving\n                        const proxyInfo = `${formData.proxy_type.toUpperCase()} proxy: ${formData.proxy_host}:${formData.proxy_port}`;\n                        const authInfo = formData.proxy_username ? ` (Auth: ${formData.proxy_username})` : ' (No Auth)';\n                        toast.info(`Proxy Configuration:\\n${proxyInfo}${authInfo}\\n\\nSave profile first to test connection.`, {\n                          duration: 4000\n                        });\n                      }\n                    },\n                    disabled: !formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading,\n                    sx: {\n                      textTransform: 'none'\n                    },\n                    children: testProxyMutation.isLoading ? 'Testing...' : selectedProfile ? 'Test Connection' : 'Preview Config'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 992,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Test proxy connection to ensure it works with antidetect browser\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1010,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 764,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 762,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setCreateDialogOpen(false);\n            setEditDialogOpen(false);\n            resetForm();\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1021,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleSubmit,\n          disabled: !formData.name || createMutation.isLoading || updateMutation.isLoading,\n          children: selectedProfile ? 'Update' : 'Create'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1030,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1020,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 749,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: loginInstructionsOpen,\n      onClose: () => setLoginInstructionsOpen(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(FacebookIcon, {\n          sx: {\n            color: '#1877f2'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1048,\n          columnNumber: 11\n        }, this), \"Facebook Login Instructions\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1047,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"An antidetect browser window will open. Follow these steps to complete Facebook login:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1052,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"ol\",\n          sx: {\n            pl: 2\n          },\n          children: loginInstructions.map((instruction, index) => /*#__PURE__*/_jsxDEV(Box, {\n            component: \"li\",\n            sx: {\n              mb: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: instruction\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1059,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1058,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1056,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Important:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 15\n            }, this), \" After successfully logging in to Facebook, click the \\\"Complete Login\\\" button in the profile menu to update the profile status to \\\"Ready\\\".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1065,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1064,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1051,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setLoginInstructionsOpen(false),\n          children: \"Got it\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1072,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1071,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1041,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 682,\n    columnNumber: 5\n  }, this);\n}\n_s2(Profiles, \"grwBbtnLiMUn1AWcD7bDkPPR//k=\", false, function () {\n  return [useQueryClient, useApp, useQuery, useMutation, useMutation, useMutation, useMutation, useMutation, useMutation, useMutation];\n});\n_c2 = Profiles;\nexport default Profiles;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProfileCard\");\n$RefreshReg$(_c2, \"Profiles\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "Avatar", "<PERSON><PERSON>", "ListItemIcon", "ListItemText", "<PERSON><PERSON>", "LinearProgress", "FormHelperText", "Add", "AddIcon", "Person", "PersonIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "PlayArrow", "PlayIcon", "Refresh", "RefreshIcon", "Computer", "ComputerIcon", "Security", "SecurityIcon", "Language", "LanguageIcon", "LocationOn", "LocationIcon", "Facebook", "FacebookIcon", "<PERSON><PERSON>", "LoginIcon", "CheckCircle", "CheckCircleIcon", "useQuery", "useMutation", "useQueryClient", "toast", "profilesAPI", "useApp", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfileCard", "profile", "onEdit", "onDelete", "onTest", "onFacebookLogin", "onFacebookLoginComplete", "_s", "anchorEl", "setAnchorEl", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "isLoggedIn", "facebook_email", "facebook_user_id", "isReady", "status", "getStatusColor", "sx", "height", "children", "display", "justifyContent", "alignItems", "mb", "bgcolor", "width", "mr", "name", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "label", "size", "color", "onClick", "my", "container", "spacing", "item", "xs", "fontSize", "timezone", "proxy_type", "language", "mt", "gap", "startIcon", "textTransform", "open", "Boolean", "onClose", "_c", "Profiles", "_s2", "createDialogOpen", "setCreateDialogOpen", "editDialogOpen", "setEditDialogOpen", "selectedProfile", "setSelectedProfile", "loginInstructionsOpen", "setLoginInstructionsOpen", "loginInstructions", "setLoginInstructions", "loginStatusPolling", "setLoginStatusPolling", "Map", "formData", "setFormData", "browser_type", "user_agent", "screen_resolution", "proxy_enabled", "proxy_host", "proxy_port", "proxy_username", "proxy_password", "queryClient", "setProfiles", "data", "profilesData", "isLoading", "error", "getAll", "then", "response", "onSuccess", "profilesList", "profiles", "onError", "createMutation", "create", "invalidateQueries", "resetForm", "success", "_error$response", "_error$response$data", "console", "errorMessage", "detail", "message", "updateMutation", "id", "update", "_error$response2", "_error$response2$data", "deleteMutation", "delete", "testProxyMutation", "profileId", "testProxy", "result", "response_time", "info", "_error$response3", "_error$response4", "_error$response4$data", "warning", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data", "facebookLoginMutation", "facebookLogin", "instructions", "startLoginStatusPolling", "profile_id", "_error$response7", "_error$response7$data", "facebookLoginCompleteMutation", "facebookData", "facebookLoginComplete", "profile_name", "_error$response8", "_error$response8$data", "facebookLoginTerminateMutation", "facebookLoginTerminate", "stopLoginStatusPolling", "_error$response9", "_error$response9$data", "Array", "isArray", "handleCreate", "handleEdit", "toString", "handleDelete", "window", "confirm", "mutate", "handleTest", "proxyInfo", "profileInfo", "Date", "created_at", "toLocaleDateString", "join", "duration", "setTimeout", "handleSubmit", "submitData", "port", "parseInt", "isNaN", "finalData", "browser_config", "proxy_config", "host", "username", "password", "handleFormChange", "field", "value", "prev", "handleFacebookLogin", "intervalId", "setInterval", "facebookLoginStatus", "login_status", "set", "get", "clearInterval", "newMap", "useEffect", "for<PERSON>ach", "handleFacebookLoginComplete", "email", "user_id", "className", "severity", "length", "textAlign", "py", "map", "sm", "md", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "onChange", "e", "target", "required", "placeholder", "proxyType", "helperText", "type", "inputProps", "min", "max", "authInfo", "disabled", "component", "pl", "instruction", "index", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  Button,\n  Grid,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Avatar,\n  Menu,\n  ListItemIcon,\n  ListItemText,\n  Alert,\n  LinearProgress,\n  FormHelperText,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Person as PersonIcon,\n  MoreVert as MoreVertIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  PlayArrow as PlayIcon,\n  Refresh as RefreshIcon,\n  Computer as ComputerIcon,\n  Security as SecurityIcon,\n  Language as LanguageIcon,\n  LocationOn as LocationIcon,\n  Facebook as FacebookIcon,\n  Login as LoginIcon,\n  CheckCircle as CheckCircleIcon,\n} from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\n\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\n\nfunction ProfileCard({ profile, onEdit, onDelete, onTest, onFacebookLogin, onFacebookLoginComplete }) {\n  const [anchorEl, setAnchorEl] = useState(null);\n\n  const handleMenuClick = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const isLoggedIn = profile.facebook_email || profile.facebook_user_id;\n  const isReady = profile.status === 'active' && isLoggedIn;\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Avatar\n              sx={{\n                bgcolor: 'primary.main',\n                width: 48,\n                height: 48,\n                mr: 2,\n              }}\n            >\n              {profile.name.charAt(0).toUpperCase()}\n            </Avatar>\n            <Box>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                {profile.name}\n              </Typography>\n              <Chip\n                label={profile.status || 'inactive'}\n                size=\"small\"\n                color={getStatusColor(profile.status)}\n              />\n            </Box>\n          </Box>\n          <IconButton onClick={handleMenuClick}>\n            <MoreVertIcon />\n          </IconButton>\n        </Box>\n\n        <Divider sx={{ my: 2 }} />\n\n        <Grid container spacing={2}>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <ComputerIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Browser\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              Chrome\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LocationIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Location\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.timezone || 'UTC'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <SecurityIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Proxy\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.proxy_type && profile.proxy_type !== 'no_proxy'\n                ? profile.proxy_type.toUpperCase()\n                : 'No Proxy'}\n            </Typography>\n          </Grid>\n          <Box item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LanguageIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Language\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.language || 'en-US'}\n            </Typography>\n          </Box>\n\n        </Grid>\n\n        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <FacebookIcon fontSize=\"small\" color=\"action\" />\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ fontSize: '0.75rem' }}>\n              Facebook:\n            </Typography>\n            {isLoggedIn ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <CheckCircleIcon fontSize=\"small\" sx={{ color: '#4caf50' }} />\n                <Typography variant=\"body2\" sx={{ fontWeight: 500, color: '#4caf50' }}>\n                  Logged In\n                </Typography>\n              </Box>\n            ) : (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <LoginIcon fontSize=\"small\" sx={{ color: '#ff9800' }} />\n                <Typography variant=\"body2\" sx={{ fontWeight: 500, color: '#ff9800' }}>\n                  Not Logged In\n                </Typography>\n              </Box>\n            )}\n          </Box>\n        </Box>\n\n        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>\n          <Button\n            size=\"small\"\n            variant=\"outlined\"\n            startIcon={<PlayIcon />}\n            onClick={() => onTest(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Test\n          </Button>\n          <Button\n            size=\"small\"\n            variant=\"text\"\n            startIcon={<EditIcon />}\n            onClick={() => onEdit(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Edit\n          </Button>\n        </Box>\n\n        <Menu\n          anchorEl={anchorEl}\n          open={Boolean(anchorEl)}\n          onClose={handleMenuClose}\n        >\n          <MenuItem onClick={() => { onEdit(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <EditIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Edit Profile</ListItemText>\n          </MenuItem>\n          <MenuItem onClick={() => { onTest(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <PlayIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Test Browser</ListItemText>\n          </MenuItem>\n\n          <Divider />\n\n          {!isLoggedIn ? (\n            <MenuItem onClick={() => { onFacebookLogin(profile); handleMenuClose(); }}>\n              <ListItemIcon>\n                <FacebookIcon fontSize=\"small\" sx={{ color: '#1877f2' }} />\n              </ListItemIcon>\n              <ListItemText>Login to Facebook</ListItemText>\n            </MenuItem>\n          ) : (\n            <MenuItem onClick={() => { onFacebookLoginComplete(profile); handleMenuClose(); }}>\n              <ListItemIcon>\n                <CheckCircleIcon fontSize=\"small\" sx={{ color: '#4caf50' }} />\n              </ListItemIcon>\n              <ListItemText>Complete Login</ListItemText>\n            </MenuItem>\n          )}\n\n          <Divider />\n\n          <MenuItem onClick={() => { onDelete(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <DeleteIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Delete Profile</ListItemText>\n          </MenuItem>\n        </Menu>\n      </CardContent>\n    </Card>\n  );\n}\n\nfunction Profiles() {\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [loginInstructionsOpen, setLoginInstructionsOpen] = useState(false);\n  const [loginInstructions, setLoginInstructions] = useState([]);\n  const [loginStatusPolling, setLoginStatusPolling] = useState(new Map()); // profileId -> intervalId\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: '',\n  });\n\n  const queryClient = useQueryClient();\n  const { setProfiles } = useApp();\n\n  // Fetch profiles\n  const { data: profilesData, isLoading, error } = useQuery(\n    'profiles',\n    () => profilesAPI.getAll().then(response => response.data),\n    {\n      onSuccess: (data) => {\n        // Handle both array and object with profiles property\n        const profilesList = data?.profiles || data || [];\n        setProfiles(profilesList);\n      },\n      onError: (error) => {\n        toast.error('Failed to load profiles');\n      },\n    }\n  );\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: (error) => {\n      console.error('Create profile error:', error);\n      const errorMessage = error?.response?.data?.detail ||\n                          error?.message ||\n                          'Failed to create profile';\n      toast.error(errorMessage);\n    },\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(\n    ({ id, data }) => profilesAPI.update(id, data),\n    {\n      onSuccess: () => {\n        queryClient.invalidateQueries('profiles');\n        setEditDialogOpen(false);\n        resetForm();\n        toast.success('Profile updated successfully');\n      },\n      onError: (error) => {\n        console.error('Update profile error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to update profile';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: (error) => {\n      toast.error('Failed to delete profile');\n    },\n  });\n\n\n\n  // Test proxy mutation\n  const testProxyMutation = useMutation(\n    (profileId) => profilesAPI.testProxy(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'success') {\n          toast.success(`Proxy test successful! Response time: ${result.response_time}s`);\n        } else if (result.status === 'no_proxy') {\n          toast.info('No proxy configured for this profile');\n        } else {\n          toast.error(`Proxy test failed: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Proxy test error:', error);\n\n        // Handle specific error cases\n        if (error?.response?.status === 404) {\n          toast.warning('Proxy test endpoint not available. This feature may not be implemented yet.');\n        } else if (error?.response?.data?.message === \"Endpoint not found\") {\n          toast.warning('Test feature not available in current backend version.');\n        } else {\n          const errorMessage = error?.response?.data?.detail ||\n                              error?.response?.data?.message ||\n                              error?.message ||\n                              'Proxy test failed';\n          toast.error(errorMessage);\n        }\n      },\n    }\n  );\n\n  // Facebook login mutations\n  const facebookLoginMutation = useMutation(\n    (profileId) => profilesAPI.facebookLogin(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'login_initiated' || result.status === 'browser_launched') {\n          toast.success('Antidetect browser launched! Complete Facebook login manually in the browser window.');\n          // Show instructions dialog\n          setLoginInstructions(result.instructions || []);\n          setLoginInstructionsOpen(true);\n\n          // Start polling for login status\n          startLoginStatusPolling(result.profile_id);\n        } else if (result.status === 'session_exists') {\n          toast.info('Browser session already active. Complete login in the existing browser window.');\n          setLoginInstructions([\n            \"Browser session is already running\",\n            \"Complete Facebook login in the existing browser window\",\n            \"Click 'Complete Login' button when done\"\n          ]);\n          setLoginInstructionsOpen(true);\n        } else {\n          toast.error(`Failed to start login: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Facebook login error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to start Facebook login';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const facebookLoginCompleteMutation = useMutation(\n    ({ profileId, facebookData }) => profilesAPI.facebookLoginComplete(profileId, facebookData),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'login_complete') {\n          toast.success(`Facebook login completed! Profile \"${result.profile_name}\" is now ready.`);\n          queryClient.invalidateQueries('profiles');\n        } else {\n          toast.error(`Failed to complete login: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Facebook login complete error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to complete Facebook login';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const facebookLoginTerminateMutation = useMutation(\n    (profileId) => profilesAPI.facebookLoginTerminate(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        toast.success('Browser session terminated successfully.');\n        stopLoginStatusPolling(result.profile_id);\n        setLoginInstructionsOpen(false);\n      },\n      onError: (error) => {\n        console.error('Facebook login terminate error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to terminate browser session';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const profiles = Array.isArray(profilesData?.profiles)\n    ? profilesData.profiles\n    : Array.isArray(profilesData)\n    ? profilesData\n    : [];\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: '',\n    });\n    setSelectedProfile(null);\n  };\n\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n\n  const handleEdit = (profile) => {\n    setSelectedProfile(profile);\n\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome', // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '', // Not in current API response\n      proxy_password: '', // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n\n  const handleDelete = (profile) => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n\n  const handleTest = (profile) => {\n    // Show profile information\n    const proxyInfo = profile.proxy_type && profile.proxy_type !== 'no_proxy'\n      ? `${profile.proxy_type.toUpperCase()}${profile.proxy_host ? ` (${profile.proxy_host}:${profile.proxy_port})` : ''}`\n      : 'No Proxy';\n\n    // Create a detailed info message\n    const profileInfo = [\n      `Profile: ${profile.name}`,\n      `Status: ${profile.status}`,\n      `Proxy: ${proxyInfo}`,\n      `Language: ${profile.language || 'en-US'}`,\n      `Timezone: ${profile.timezone || 'UTC'}`,\n      `Created: ${new Date(profile.created_at).toLocaleDateString()}`\n    ].join('\\n');\n\n    // Show info first\n    toast.success(`Profile Information:\\n${profileInfo}`, { duration: 5000 });\n\n    // Try to test proxy if configured\n    if (profile.proxy_type && profile.proxy_type !== 'no_proxy' && profile.proxy_host) {\n      setTimeout(() => {\n        testProxyMutation.mutate(profile.id);\n      }, 1000);\n    }\n  };\n\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = { ...formData };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n\n    if (selectedProfile) {\n      updateMutation.mutate({ id: selectedProfile.id, data: finalData });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleFacebookLogin = (profile) => {\n    facebookLoginMutation.mutate(profile.id);\n  };\n\n  // Login status polling functions\n  const startLoginStatusPolling = (profileId) => {\n    // Clear any existing polling for this profile\n    stopLoginStatusPolling(profileId);\n\n    const intervalId = setInterval(async () => {\n      try {\n        const response = await profilesAPI.facebookLoginStatus(profileId);\n        const status = response.data.login_status;\n\n        if (status.status === 'browser_closed' || status.status === 'expired') {\n          toast.warning('Browser session ended. Please restart login process.');\n          stopLoginStatusPolling(profileId);\n        } else if (status.status === 'no_session') {\n          stopLoginStatusPolling(profileId);\n        }\n        // Continue polling if status is 'in_progress' or 'active'\n      } catch (error) {\n        console.error('Error polling login status:', error);\n        // Don't stop polling on error, might be temporary\n      }\n    }, 10000); // Poll every 10 seconds\n\n    setLoginStatusPolling(prev => new Map(prev.set(profileId, intervalId)));\n  };\n\n  const stopLoginStatusPolling = (profileId) => {\n    const intervalId = loginStatusPolling.get(profileId);\n    if (intervalId) {\n      clearInterval(intervalId);\n      setLoginStatusPolling(prev => {\n        const newMap = new Map(prev);\n        newMap.delete(profileId);\n        return newMap;\n      });\n    }\n  };\n\n  // Cleanup polling on component unmount\n  React.useEffect(() => {\n    return () => {\n      loginStatusPolling.forEach((intervalId) => {\n        clearInterval(intervalId);\n      });\n    };\n  }, [loginStatusPolling]);\n\n  const handleFacebookLoginComplete = (profile) => {\n    // Stop polling for this profile\n    stopLoginStatusPolling(profile.id);\n\n    // For now, complete without additional data\n    // In a real implementation, you might collect Facebook user data\n    facebookLoginCompleteMutation.mutate({\n      profileId: profile.id,\n      facebookData: {\n        email: \"<EMAIL>\", // This would be extracted from browser\n        username: \"facebook_user\",\n        user_id: \"123456789\"\n      }\n    });\n  };\n\n  if (error) {\n    return (\n      <Box className=\"fade-in\">\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          Failed to load profiles. Please check your connection to the backend.\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box className=\"fade-in\">\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n          Profile Management\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => queryClient.invalidateQueries('profiles')}\n            sx={{ textTransform: 'none' }}\n          >\n            Refresh\n          </Button>\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={handleCreate}\n            sx={{ textTransform: 'none' }}\n          >\n            New Profile\n          </Button>\n        </Box>\n      </Box>\n\n      {isLoading && <LinearProgress sx={{ mb: 3 }} />}\n\n\n\n      {profiles.length === 0 && !isLoading ? (\n        <Card>\n          <CardContent sx={{ textAlign: 'center', py: 8 }}>\n            <PersonIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n            <Typography variant=\"h6\" sx={{ mb: 1 }}>\n              No Profiles Found\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              Create your first browser profile to get started\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={handleCreate}\n              sx={{ textTransform: 'none' }}\n            >\n              Create Profile\n            </Button>\n          </CardContent>\n        </Card>\n      ) : (\n        <Grid container spacing={3}>\n          {profiles.map((profile) => (\n            <Grid item xs={12} sm={6} md={4} key={profile.id}>\n              <ProfileCard\n                profile={profile}\n                onEdit={handleEdit}\n                onDelete={handleDelete}\n                onTest={handleTest}\n                onFacebookLogin={handleFacebookLogin}\n                onFacebookLoginComplete={handleFacebookLoginComplete}\n              />\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Create/Edit Profile Dialog */}\n      <Dialog\n        open={createDialogOpen || editDialogOpen}\n        onClose={() => {\n          setCreateDialogOpen(false);\n          setEditDialogOpen(false);\n          resetForm();\n        }}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          {selectedProfile ? 'Edit Profile' : 'Create New Profile'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Grid container spacing={3}>\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Profile Name\"\n                  value={formData.name}\n                  onChange={(e) => handleFormChange('name', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Browser Type</InputLabel>\n                  <Select\n                    value={formData.browser_type}\n                    onChange={(e) => handleFormChange('browser_type', e.target.value)}\n                    label=\"Browser Type\"\n                  >\n                    <MenuItem value=\"chrome\">Chrome</MenuItem>\n                    <MenuItem value=\"firefox\">Firefox</MenuItem>\n                    <MenuItem value=\"edge\">Edge</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Screen Resolution</InputLabel>\n                  <Select\n                    value={formData.screen_resolution}\n                    onChange={(e) => handleFormChange('screen_resolution', e.target.value)}\n                    label=\"Screen Resolution\"\n                  >\n                    <MenuItem value=\"1920x1080\">1920x1080</MenuItem>\n                    <MenuItem value=\"1366x768\">1366x768</MenuItem>\n                    <MenuItem value=\"1440x900\">1440x900</MenuItem>\n                    <MenuItem value=\"1280x720\">1280x720</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Timezone</InputLabel>\n                  <Select\n                    value={formData.timezone}\n                    onChange={(e) => handleFormChange('timezone', e.target.value)}\n                    label=\"Timezone\"\n                  >\n                    <MenuItem value=\"UTC\">UTC</MenuItem>\n                    <MenuItem value=\"Asia/Ho_Chi_Minh\">Asia/Ho_Chi_Minh</MenuItem>\n                    <MenuItem value=\"America/New_York\">America/New_York</MenuItem>\n                    <MenuItem value=\"Europe/London\">Europe/London</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Language</InputLabel>\n                  <Select\n                    value={formData.language}\n                    onChange={(e) => handleFormChange('language', e.target.value)}\n                    label=\"Language\"\n                  >\n                    <MenuItem value=\"en-US\">English (US)</MenuItem>\n                    <MenuItem value=\"vi-VN\">Vietnamese</MenuItem>\n                    <MenuItem value=\"zh-CN\">Chinese (Simplified)</MenuItem>\n                    <MenuItem value=\"ja-JP\">Japanese</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"User Agent (Optional)\"\n                  value={formData.user_agent}\n                  onChange={(e) => handleFormChange('user_agent', e.target.value)}\n                  placeholder=\"Leave empty for automatic generation\"\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n                  Proxy Configuration\n                </Typography>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Proxy Type</InputLabel>\n                  <Select\n                    value={formData.proxy_type}\n                    onChange={(e) => {\n                      const proxyType = e.target.value;\n                      handleFormChange('proxy_type', proxyType);\n                      handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                    }}\n                    label=\"Proxy Type\"\n                  >\n                    <MenuItem value=\"no_proxy\">\n                      <Box>\n                        <Typography variant=\"body2\">No Proxy (Local Network)</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Direct connection without proxy\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"http\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTP</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Standard HTTP proxy (ports: 8080, 3128, 8888)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"https\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTPS</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Encrypted HTTPS proxy (ports: 8080, 3128, 443)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"socks5\">\n                      <Box>\n                        <Typography variant=\"body2\">SOCKS5</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          High anonymity proxy (ports: 1080, 1081, 9050)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"ssh\">\n                      <Box>\n                        <Typography variant=\"body2\">SSH</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Secure SSH tunnel (ports: 22, 2222)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  </Select>\n                  <FormHelperText>\n                    {formData.proxy_type === 'no_proxy' && 'Using direct connection'}\n                    {formData.proxy_type === 'http' && 'Fast, suitable for web browsing'}\n                    {formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data'}\n                    {formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols'}\n                    {formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials'}\n                  </FormHelperText>\n                </FormControl>\n              </Grid>\n\n              {formData.proxy_type !== 'no_proxy' && (\n                <>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Host\"\n                      value={formData.proxy_host}\n                      onChange={(e) => handleFormChange('proxy_host', e.target.value)}\n                      required\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"e.g., ssh.example.com or *************\"\n                          : \"e.g., proxy.example.com or *************\"\n                      }\n                      helperText=\"IP address or domain name of the proxy server\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Port\"\n                      value={formData.proxy_port}\n                      onChange={(e) => handleFormChange('proxy_port', e.target.value)}\n                      type=\"number\"\n                      required\n                      placeholder={\n                        formData.proxy_type === 'http' ? \"8080, 3128, 8888\" :\n                        formData.proxy_type === 'https' ? \"8080, 3128, 443\" :\n                        formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" :\n                        formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\"\n                      }\n                      helperText={`Common ${formData.proxy_type.toUpperCase()} ports`}\n                      inputProps={{ min: 1, max: 65535 }}\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\"}\n                      value={formData.proxy_username}\n                      onChange={(e) => handleFormChange('proxy_username', e.target.value)}\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH username for authentication\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Required for SSH connections\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\"}\n                      value={formData.proxy_password}\n                      onChange={(e) => handleFormChange('proxy_password', e.target.value)}\n                      type=\"password\"\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH password or leave empty for key-based auth\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Password or private key authentication\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n\n                  <Grid item xs={12}>\n                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>\n                      <Button\n                        variant=\"outlined\"\n                        size=\"small\"\n                        onClick={() => {\n                          if (selectedProfile) {\n                            testProxyMutation.mutate(selectedProfile.id);\n                          } else {\n                            // Test proxy configuration without saving\n                            const proxyInfo = `${formData.proxy_type.toUpperCase()} proxy: ${formData.proxy_host}:${formData.proxy_port}`;\n                            const authInfo = formData.proxy_username ? ` (Auth: ${formData.proxy_username})` : ' (No Auth)';\n                            toast.info(`Proxy Configuration:\\n${proxyInfo}${authInfo}\\n\\nSave profile first to test connection.`, { duration: 4000 });\n                          }\n                        }}\n                        disabled={!formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading}\n                        sx={{ textTransform: 'none' }}\n                      >\n                        {testProxyMutation.isLoading ? 'Testing...' : selectedProfile ? 'Test Connection' : 'Preview Config'}\n                      </Button>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Test proxy connection to ensure it works with antidetect browser\n                      </Typography>\n                    </Box>\n                  </Grid>\n                </>\n              )}\n            </Grid>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => {\n              setCreateDialogOpen(false);\n              setEditDialogOpen(false);\n              resetForm();\n            }}\n          >\n            Cancel\n          </Button>\n          <Button\n            variant=\"contained\"\n            onClick={handleSubmit}\n            disabled={!formData.name || createMutation.isLoading || updateMutation.isLoading}\n          >\n            {selectedProfile ? 'Update' : 'Create'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Facebook Login Instructions Dialog */}\n      <Dialog\n        open={loginInstructionsOpen}\n        onClose={() => setLoginInstructionsOpen(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <FacebookIcon sx={{ color: '#1877f2' }} />\n          Facebook Login Instructions\n        </DialogTitle>\n        <DialogContent>\n          <Alert severity=\"info\" sx={{ mb: 2 }}>\n            An antidetect browser window will open. Follow these steps to complete Facebook login:\n          </Alert>\n\n          <Box component=\"ol\" sx={{ pl: 2 }}>\n            {loginInstructions.map((instruction, index) => (\n              <Box component=\"li\" key={index} sx={{ mb: 1 }}>\n                <Typography variant=\"body2\">{instruction}</Typography>\n              </Box>\n            ))}\n          </Box>\n\n          <Alert severity=\"warning\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              <strong>Important:</strong> After successfully logging in to Facebook,\n              click the \"Complete Login\" button in the profile menu to update the profile status to \"Ready\".\n            </Typography>\n          </Alert>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setLoginInstructionsOpen(false)}>\n            Got it\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n}\n\nexport default Profiles;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,cAAc,EACdC,cAAc,QACT,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,QAAQ,EACrBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,YAAY,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACnE,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,MAAM,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,SAASC,WAAWA,CAAC;EAAEC,OAAO;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,MAAM;EAAEC,eAAe;EAAEC;AAAwB,CAAC,EAAE;EAAAC,EAAA;EACpG,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAM0E,eAAe,GAAIC,KAAK,IAAK;IACjCF,WAAW,CAACE,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BJ,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMK,UAAU,GAAGb,OAAO,CAACc,cAAc,IAAId,OAAO,CAACe,gBAAgB;EACrE,MAAMC,OAAO,GAAGhB,OAAO,CAACiB,MAAM,KAAK,QAAQ,IAAIJ,UAAU;EAEzD,MAAMK,cAAc,GAAID,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACErB,OAAA,CAAC1D,IAAI;IAACiF,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC3BzB,OAAA,CAACzD,WAAW;MAAAkF,QAAA,gBACVzB,OAAA,CAAC5D,GAAG;QAACmF,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,YAAY;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAC7FzB,OAAA,CAAC5D,GAAG;UAACmF,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjDzB,OAAA,CAAC1C,MAAM;YACLiE,EAAE,EAAE;cACFO,OAAO,EAAE,cAAc;cACvBC,KAAK,EAAE,EAAE;cACTP,MAAM,EAAE,EAAE;cACVQ,EAAE,EAAE;YACN,CAAE;YAAAP,QAAA,EAEDrB,OAAO,CAAC6B,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACTvC,OAAA,CAAC5D,GAAG;YAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,IAAI;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEZ,EAAE,EAAE;cAAI,CAAE;cAAAJ,QAAA,EACvDrB,OAAO,CAAC6B;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACbvC,OAAA,CAACtD,IAAI;cACHgG,KAAK,EAAEtC,OAAO,CAACiB,MAAM,IAAI,UAAW;cACpCsB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEtB,cAAc,CAAClB,OAAO,CAACiB,MAAM;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvC,OAAA,CAACrD,UAAU;UAACkG,OAAO,EAAEhC,eAAgB;UAAAY,QAAA,eACnCzB,OAAA,CAAC9B,YAAY;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENvC,OAAA,CAAC3C,OAAO;QAACkE,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BvC,OAAA,CAACvD,IAAI;QAACsG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAvB,QAAA,gBACzBzB,OAAA,CAACvD,IAAI;UAACwG,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfzB,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDzB,OAAA,CAACpB,YAAY;cAAC2C,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNvC,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EAAC;UAErD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPvC,OAAA,CAACvD,IAAI;UAACwG,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfzB,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDzB,OAAA,CAACd,YAAY;cAACqC,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNvC,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDrB,OAAO,CAACgD,QAAQ,IAAI;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPvC,OAAA,CAACvD,IAAI;UAACwG,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfzB,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDzB,OAAA,CAAClB,YAAY;cAACyC,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNvC,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDrB,OAAO,CAACiD,UAAU,IAAIjD,OAAO,CAACiD,UAAU,KAAK,UAAU,GACpDjD,OAAO,CAACiD,UAAU,CAAClB,WAAW,CAAC,CAAC,GAChC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPvC,OAAA,CAAC5D,GAAG;UAAC6G,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACdzB,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDzB,OAAA,CAAChB,YAAY;cAACuC,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNvC,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDrB,OAAO,CAACkD,QAAQ,IAAI;UAAO;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEF,CAAC,eAEPvC,OAAA,CAAC5D,GAAG;QAACmF,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE7B,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,cAAc,EAAE;QAAgB,CAAE;QAAAF,QAAA,eACzFzB,OAAA,CAAC5D,GAAG;UAACmF,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE4B,GAAG,EAAE;UAAE,CAAE;UAAA/B,QAAA,gBACzDzB,OAAA,CAACZ,YAAY;YAAC+D,QAAQ,EAAC,OAAO;YAACP,KAAK,EAAC;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDvC,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAACI,KAAK,EAAC,gBAAgB;YAACrB,EAAE,EAAE;cAAE4B,QAAQ,EAAE;YAAU,CAAE;YAAA1B,QAAA,EAAC;UAEhF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZtB,UAAU,gBACTjB,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE4B,GAAG,EAAE;YAAI,CAAE;YAAA/B,QAAA,gBAC3DzB,OAAA,CAACR,eAAe;cAAC2D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAEvE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAENvC,OAAA,CAAC5D,GAAG;YAACmF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE4B,GAAG,EAAE;YAAI,CAAE;YAAA/B,QAAA,gBAC3DzB,OAAA,CAACV,SAAS;cAAC6D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDvC,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAEvE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvC,OAAA,CAAC5D,GAAG;QAACmF,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE7B,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBAC1CzB,OAAA,CAACxD,MAAM;UACLmG,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,UAAU;UAClBiB,SAAS,eAAEzD,OAAA,CAACxB,QAAQ;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAMtC,MAAM,CAACH,OAAO,CAAE;UAC/BmB,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA,CAACxD,MAAM;UACLmG,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,MAAM;UACdiB,SAAS,eAAEzD,OAAA,CAAC5B,QAAQ;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAMxC,MAAM,CAACD,OAAO,CAAE;UAC/BmB,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENvC,OAAA,CAACzC,IAAI;QACHoD,QAAQ,EAAEA,QAAS;QACnBgD,IAAI,EAAEC,OAAO,CAACjD,QAAQ,CAAE;QACxBkD,OAAO,EAAE7C,eAAgB;QAAAS,QAAA,gBAEzBzB,OAAA,CAAC5C,QAAQ;UAACyF,OAAO,EAAEA,CAAA,KAAM;YAAExC,MAAM,CAACD,OAAO,CAAC;YAAEY,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBAC/DzB,OAAA,CAACxC,YAAY;YAAAiE,QAAA,eACXzB,OAAA,CAAC5B,QAAQ;cAAC+E,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfvC,OAAA,CAACvC,YAAY;YAAAgE,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACXvC,OAAA,CAAC5C,QAAQ;UAACyF,OAAO,EAAEA,CAAA,KAAM;YAAEtC,MAAM,CAACH,OAAO,CAAC;YAAEY,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBAC/DzB,OAAA,CAACxC,YAAY;YAAAiE,QAAA,eACXzB,OAAA,CAACxB,QAAQ;cAAC2E,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfvC,OAAA,CAACvC,YAAY;YAAAgE,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEXvC,OAAA,CAAC3C,OAAO;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAEV,CAACtB,UAAU,gBACVjB,OAAA,CAAC5C,QAAQ;UAACyF,OAAO,EAAEA,CAAA,KAAM;YAAErC,eAAe,CAACJ,OAAO,CAAC;YAAEY,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBACxEzB,OAAA,CAACxC,YAAY;YAAAiE,QAAA,eACXzB,OAAA,CAACZ,YAAY;cAAC+D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACfvC,OAAA,CAACvC,YAAY;YAAAgE,QAAA,EAAC;UAAiB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,gBAEXvC,OAAA,CAAC5C,QAAQ;UAACyF,OAAO,EAAEA,CAAA,KAAM;YAAEpC,uBAAuB,CAACL,OAAO,CAAC;YAAEY,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBAChFzB,OAAA,CAACxC,YAAY;YAAAiE,QAAA,eACXzB,OAAA,CAACR,eAAe;cAAC2D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACfvC,OAAA,CAACvC,YAAY;YAAAgE,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACX,eAEDvC,OAAA,CAAC3C,OAAO;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEXvC,OAAA,CAAC5C,QAAQ;UAACyF,OAAO,EAAEA,CAAA,KAAM;YAAEvC,QAAQ,CAACF,OAAO,CAAC;YAAEY,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBACjEzB,OAAA,CAACxC,YAAY;YAAAiE,QAAA,eACXzB,OAAA,CAAC1B,UAAU;cAAC6E,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACfvC,OAAA,CAACvC,YAAY;YAAAgE,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX;AAAC7B,EAAA,CA3MQP,WAAW;AAAA2D,EAAA,GAAX3D,WAAW;AA6MpB,SAAS4D,QAAQA,CAAA,EAAG;EAAAC,GAAA;EAClB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/H,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgI,cAAc,EAAEC,iBAAiB,CAAC,GAAGjI,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkI,eAAe,EAAEC,kBAAkB,CAAC,GAAGnI,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoI,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrI,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACsI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACwI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzI,QAAQ,CAAC,IAAI0I,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5I,QAAQ,CAAC;IACvC8F,IAAI,EAAE,EAAE;IACR+C,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,WAAW;IAC9B9B,QAAQ,EAAE,KAAK;IACfE,QAAQ,EAAE,OAAO;IACjB6B,aAAa,EAAE,KAAK;IACpB9B,UAAU,EAAE,UAAU;IACtB+B,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG7F,cAAc,CAAC,CAAC;EACpC,MAAM;IAAE8F;EAAY,CAAC,GAAG3F,MAAM,CAAC,CAAC;;EAEhC;EACA,MAAM;IAAE4F,IAAI,EAAEC,YAAY;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAGpG,QAAQ,CACvD,UAAU,EACV,MAAMI,WAAW,CAACiG,MAAM,CAAC,CAAC,CAACC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACN,IAAI,CAAC,EAC1D;IACEO,SAAS,EAAGP,IAAI,IAAK;MACnB;MACA,MAAMQ,YAAY,GAAG,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,QAAQ,KAAIT,IAAI,IAAI,EAAE;MACjDD,WAAW,CAACS,YAAY,CAAC;IAC3B,CAAC;IACDE,OAAO,EAAGP,KAAK,IAAK;MAClBjG,KAAK,CAACiG,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CACF,CAAC;;EAED;EACA,MAAMQ,cAAc,GAAG3G,WAAW,CAACG,WAAW,CAACyG,MAAM,EAAE;IACrDL,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzCrC,mBAAmB,CAAC,KAAK,CAAC;MAC1BsC,SAAS,CAAC,CAAC;MACX5G,KAAK,CAAC6G,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAa,eAAA,EAAAC,oBAAA;MAClBC,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAa,eAAA,GAALb,KAAK,CAAEG,QAAQ,cAAAU,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAiBhB,IAAI,cAAAiB,oBAAA,uBAArBA,oBAAA,CAAuBG,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,0BAA0B;MAC9CnH,KAAK,CAACiG,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CAAC,CAAC;;EAEF;EACA,MAAMG,cAAc,GAAGtH,WAAW,CAChC,CAAC;IAAEuH,EAAE;IAAEvB;EAAK,CAAC,KAAK7F,WAAW,CAACqH,MAAM,CAACD,EAAE,EAAEvB,IAAI,CAAC,EAC9C;IACEO,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzCnC,iBAAiB,CAAC,KAAK,CAAC;MACxBoC,SAAS,CAAC,CAAC;MACX5G,KAAK,CAAC6G,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAsB,gBAAA,EAAAC,qBAAA;MAClBR,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAsB,gBAAA,GAALtB,KAAK,CAAEG,QAAQ,cAAAmB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBzB,IAAI,cAAA0B,qBAAA,uBAArBA,qBAAA,CAAuBN,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,0BAA0B;MAC9CnH,KAAK,CAACiG,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;;EAED;EACA,MAAMQ,cAAc,GAAG3H,WAAW,CAACG,WAAW,CAACyH,MAAM,EAAE;IACrDrB,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzC3G,KAAK,CAAC6G,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAClBjG,KAAK,CAACiG,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,CAAC;;EAIF;EACA,MAAM0B,iBAAiB,GAAG7H,WAAW,CAClC8H,SAAS,IAAK3H,WAAW,CAAC4H,SAAS,CAACD,SAAS,CAAC,EAC/C;IACEvB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B,IAAIgC,MAAM,CAACrG,MAAM,KAAK,SAAS,EAAE;QAC/BzB,KAAK,CAAC6G,OAAO,CAAC,yCAAyCiB,MAAM,CAACC,aAAa,GAAG,CAAC;MACjF,CAAC,MAAM,IAAID,MAAM,CAACrG,MAAM,KAAK,UAAU,EAAE;QACvCzB,KAAK,CAACgI,IAAI,CAAC,sCAAsC,CAAC;MACpD,CAAC,MAAM;QACLhI,KAAK,CAACiG,KAAK,CAAC,sBAAsB6B,MAAM,CAACX,OAAO,EAAE,CAAC;MACrD;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAgC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAClBnB,OAAO,CAACf,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;;MAEzC;MACA,IAAI,CAAAA,KAAK,aAALA,KAAK,wBAAAgC,gBAAA,GAALhC,KAAK,CAAEG,QAAQ,cAAA6B,gBAAA,uBAAfA,gBAAA,CAAiBxG,MAAM,MAAK,GAAG,EAAE;QACnCzB,KAAK,CAACoI,OAAO,CAAC,6EAA6E,CAAC;MAC9F,CAAC,MAAM,IAAI,CAAAnC,KAAK,aAALA,KAAK,wBAAAiC,gBAAA,GAALjC,KAAK,CAAEG,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBpC,IAAI,cAAAqC,qBAAA,uBAArBA,qBAAA,CAAuBhB,OAAO,MAAK,oBAAoB,EAAE;QAClEnH,KAAK,CAACoI,OAAO,CAAC,wDAAwD,CAAC;MACzE,CAAC,MAAM;QAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACL,MAAMvB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAoC,gBAAA,GAALpC,KAAK,CAAEG,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBvC,IAAI,cAAAwC,qBAAA,uBAArBA,qBAAA,CAAuBpB,MAAM,MAC9BjB,KAAK,aAALA,KAAK,wBAAAsC,gBAAA,GAALtC,KAAK,CAAEG,QAAQ,cAAAmC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBzC,IAAI,cAAA0C,qBAAA,uBAArBA,qBAAA,CAAuBrB,OAAO,MAC9BlB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,mBAAmB;QACvCnH,KAAK,CAACiG,KAAK,CAACgB,YAAY,CAAC;MAC3B;IACF;EACF,CACF,CAAC;;EAED;EACA,MAAMwB,qBAAqB,GAAG3I,WAAW,CACtC8H,SAAS,IAAK3H,WAAW,CAACyI,aAAa,CAACd,SAAS,CAAC,EACnD;IACEvB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B,IAAIgC,MAAM,CAACrG,MAAM,KAAK,iBAAiB,IAAIqG,MAAM,CAACrG,MAAM,KAAK,kBAAkB,EAAE;QAC/EzB,KAAK,CAAC6G,OAAO,CAAC,sFAAsF,CAAC;QACrG;QACA/B,oBAAoB,CAACgD,MAAM,CAACa,YAAY,IAAI,EAAE,CAAC;QAC/C/D,wBAAwB,CAAC,IAAI,CAAC;;QAE9B;QACAgE,uBAAuB,CAACd,MAAM,CAACe,UAAU,CAAC;MAC5C,CAAC,MAAM,IAAIf,MAAM,CAACrG,MAAM,KAAK,gBAAgB,EAAE;QAC7CzB,KAAK,CAACgI,IAAI,CAAC,gFAAgF,CAAC;QAC5FlD,oBAAoB,CAAC,CACnB,oCAAoC,EACpC,wDAAwD,EACxD,yCAAyC,CAC1C,CAAC;QACFF,wBAAwB,CAAC,IAAI,CAAC;MAChC,CAAC,MAAM;QACL5E,KAAK,CAACiG,KAAK,CAAC,0BAA0B6B,MAAM,CAACX,OAAO,EAAE,CAAC;MACzD;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAA6C,gBAAA,EAAAC,qBAAA;MAClB/B,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAA6C,gBAAA,GAAL7C,KAAK,CAAEG,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBhD,IAAI,cAAAiD,qBAAA,uBAArBA,qBAAA,CAAuB7B,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,gCAAgC;MACpDnH,KAAK,CAACiG,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAM+B,6BAA6B,GAAGlJ,WAAW,CAC/C,CAAC;IAAE8H,SAAS;IAAEqB;EAAa,CAAC,KAAKhJ,WAAW,CAACiJ,qBAAqB,CAACtB,SAAS,EAAEqB,YAAY,CAAC,EAC3F;IACE5C,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B,IAAIgC,MAAM,CAACrG,MAAM,KAAK,gBAAgB,EAAE;QACtCzB,KAAK,CAAC6G,OAAO,CAAC,sCAAsCiB,MAAM,CAACqB,YAAY,iBAAiB,CAAC;QACzFvD,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MAC3C,CAAC,MAAM;QACL3G,KAAK,CAACiG,KAAK,CAAC,6BAA6B6B,MAAM,CAACX,OAAO,EAAE,CAAC;MAC5D;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAmD,gBAAA,EAAAC,qBAAA;MAClBrC,OAAO,CAACf,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAmD,gBAAA,GAALnD,KAAK,CAAEG,QAAQ,cAAAgD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBtD,IAAI,cAAAuD,qBAAA,uBAArBA,qBAAA,CAAuBnC,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,mCAAmC;MACvDnH,KAAK,CAACiG,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAMqC,8BAA8B,GAAGxJ,WAAW,CAC/C8H,SAAS,IAAK3H,WAAW,CAACsJ,sBAAsB,CAAC3B,SAAS,CAAC,EAC5D;IACEvB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B9F,KAAK,CAAC6G,OAAO,CAAC,0CAA0C,CAAC;MACzD2C,sBAAsB,CAAC1B,MAAM,CAACe,UAAU,CAAC;MACzCjE,wBAAwB,CAAC,KAAK,CAAC;IACjC,CAAC;IACD4B,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAwD,gBAAA,EAAAC,qBAAA;MAClB1C,OAAO,CAACf,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAwD,gBAAA,GAALxD,KAAK,CAAEG,QAAQ,cAAAqD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB3D,IAAI,cAAA4D,qBAAA,uBAArBA,qBAAA,CAAuBxC,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,qCAAqC;MACzDnH,KAAK,CAACiG,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAMV,QAAQ,GAAGoD,KAAK,CAACC,OAAO,CAAC7D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEQ,QAAQ,CAAC,GAClDR,YAAY,CAACQ,QAAQ,GACrBoD,KAAK,CAACC,OAAO,CAAC7D,YAAY,CAAC,GAC3BA,YAAY,GACZ,EAAE;EAEN,MAAMa,SAAS,GAAGA,CAAA,KAAM;IACtBzB,WAAW,CAAC;MACV9C,IAAI,EAAE,EAAE;MACR+C,YAAY,EAAE,QAAQ;MACtBC,UAAU,EAAE,EAAE;MACdC,iBAAiB,EAAE,WAAW;MAC9B9B,QAAQ,EAAE,KAAK;MACfE,QAAQ,EAAE,OAAO;MACjB6B,aAAa,EAAE,KAAK;MACpB9B,UAAU,EAAE,UAAU;MACtB+B,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFjB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMmF,YAAY,GAAGA,CAAA,KAAM;IACzBvF,mBAAmB,CAAC,IAAI,CAAC;IACzBsC,SAAS,CAAC,CAAC;EACb,CAAC;EAED,MAAMkD,UAAU,GAAItJ,OAAO,IAAK;IAC9BkE,kBAAkB,CAAClE,OAAO,CAAC;IAE3B2E,WAAW,CAAC;MACV9C,IAAI,EAAE7B,OAAO,CAAC6B,IAAI,IAAI,EAAE;MACxB+C,YAAY,EAAE,QAAQ;MAAE;MACxBC,UAAU,EAAE7E,OAAO,CAAC6E,UAAU,IAAI,EAAE;MACpCC,iBAAiB,EAAE9E,OAAO,CAAC8E,iBAAiB,IAAI,WAAW;MAC3D9B,QAAQ,EAAEhD,OAAO,CAACgD,QAAQ,IAAI,KAAK;MACnCE,QAAQ,EAAElD,OAAO,CAACkD,QAAQ,IAAI,OAAO;MACrC6B,aAAa,EAAE/E,OAAO,CAACiD,UAAU,IAAIjD,OAAO,CAACiD,UAAU,KAAK,UAAU;MACtEA,UAAU,EAAEjD,OAAO,CAACiD,UAAU,IAAI,UAAU;MAC5C+B,UAAU,EAAEhF,OAAO,CAACgF,UAAU,IAAI,EAAE;MACpCC,UAAU,EAAEjF,OAAO,CAACiF,UAAU,GAAGjF,OAAO,CAACiF,UAAU,CAACsE,QAAQ,CAAC,CAAC,GAAG,EAAE;MACnErE,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE,CAAE;IACtB,CAAC,CAAC;IACFnB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMwF,YAAY,GAAIxJ,OAAO,IAAK;IAChC,IAAIyJ,MAAM,CAACC,OAAO,CAAC,4CAA4C1J,OAAO,CAAC6B,IAAI,IAAI,CAAC,EAAE;MAChFoF,cAAc,CAAC0C,MAAM,CAAC3J,OAAO,CAAC6G,EAAE,CAAC;IACnC;EACF,CAAC;EAED,MAAM+C,UAAU,GAAI5J,OAAO,IAAK;IAC9B;IACA,MAAM6J,SAAS,GAAG7J,OAAO,CAACiD,UAAU,IAAIjD,OAAO,CAACiD,UAAU,KAAK,UAAU,GACrE,GAAGjD,OAAO,CAACiD,UAAU,CAAClB,WAAW,CAAC,CAAC,GAAG/B,OAAO,CAACgF,UAAU,GAAG,KAAKhF,OAAO,CAACgF,UAAU,IAAIhF,OAAO,CAACiF,UAAU,GAAG,GAAG,EAAE,EAAE,GAClH,UAAU;;IAEd;IACA,MAAM6E,WAAW,GAAG,CAClB,YAAY9J,OAAO,CAAC6B,IAAI,EAAE,EAC1B,WAAW7B,OAAO,CAACiB,MAAM,EAAE,EAC3B,UAAU4I,SAAS,EAAE,EACrB,aAAa7J,OAAO,CAACkD,QAAQ,IAAI,OAAO,EAAE,EAC1C,aAAalD,OAAO,CAACgD,QAAQ,IAAI,KAAK,EAAE,EACxC,YAAY,IAAI+G,IAAI,CAAC/J,OAAO,CAACgK,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,CAChE,CAACC,IAAI,CAAC,IAAI,CAAC;;IAEZ;IACA1K,KAAK,CAAC6G,OAAO,CAAC,yBAAyByD,WAAW,EAAE,EAAE;MAAEK,QAAQ,EAAE;IAAK,CAAC,CAAC;;IAEzE;IACA,IAAInK,OAAO,CAACiD,UAAU,IAAIjD,OAAO,CAACiD,UAAU,KAAK,UAAU,IAAIjD,OAAO,CAACgF,UAAU,EAAE;MACjFoF,UAAU,CAAC,MAAM;QACfjD,iBAAiB,CAACwC,MAAM,CAAC3J,OAAO,CAAC6G,EAAE,CAAC;MACtC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMwD,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAMC,UAAU,GAAG;MAAE,GAAG5F;IAAS,CAAC;;IAElC;IACA,IAAI4F,UAAU,CAACtF,UAAU,KAAK,EAAE,EAAEsF,UAAU,CAACtF,UAAU,GAAG,IAAI;IAC9D,IAAIsF,UAAU,CAACrF,UAAU,KAAK,EAAE,EAAEqF,UAAU,CAACrF,UAAU,GAAG,IAAI;IAC9D,IAAIqF,UAAU,CAACpF,cAAc,KAAK,EAAE,EAAEoF,UAAU,CAACpF,cAAc,GAAG,IAAI;IACtE,IAAIoF,UAAU,CAACnF,cAAc,KAAK,EAAE,EAAEmF,UAAU,CAACnF,cAAc,GAAG,IAAI;IACtE,IAAImF,UAAU,CAACzF,UAAU,KAAK,EAAE,EAAEyF,UAAU,CAACzF,UAAU,GAAG,IAAI;;IAE9D;IACA,IAAIyF,UAAU,CAACrF,UAAU,EAAE;MACzB,MAAMsF,IAAI,GAAGC,QAAQ,CAACF,UAAU,CAACrF,UAAU,CAAC;MAC5C,IAAIwF,KAAK,CAACF,IAAI,CAAC,EAAE;QACf/K,KAAK,CAACiG,KAAK,CAAC,mCAAmC,CAAC;QAChD;MACF;MACA6E,UAAU,CAACrF,UAAU,GAAGsF,IAAI;IAC9B;;IAEA;IACA,IAAI7F,QAAQ,CAACzB,UAAU,KAAK,UAAU,EAAE;MACtC,IAAI,CAACyB,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,EAAE;QAChDzF,KAAK,CAACiG,KAAK,CAAC,wDAAwD,CAAC;QACrE;MACF;MAEA,MAAM8E,IAAI,GAAGC,QAAQ,CAAC9F,QAAQ,CAACO,UAAU,CAAC;MAC1C,IAAIwF,KAAK,CAACF,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,KAAK,EAAE;QAC3C/K,KAAK,CAACiG,KAAK,CAAC,uDAAuD,CAAC;QACpE;MACF;IACF;;IAEA;IACA,MAAMiF,SAAS,GAAG;MAChB7I,IAAI,EAAEyI,UAAU,CAACzI,IAAI;MACrBgD,UAAU,EAAEyF,UAAU,CAACzF,UAAU;MACjC8F,cAAc,EAAE;QACd/F,YAAY,EAAE0F,UAAU,CAAC1F,YAAY,IAAI,QAAQ;QACjDE,iBAAiB,EAAEwF,UAAU,CAACxF,iBAAiB,IAAI,WAAW;QAC9D9B,QAAQ,EAAEsH,UAAU,CAACtH,QAAQ,IAAI,KAAK;QACtCE,QAAQ,EAAEoH,UAAU,CAACpH,QAAQ,IAAI;MACnC,CAAC;MACD0H,YAAY,EAAEN,UAAU,CAACrH,UAAU,KAAK,UAAU,GAAG;QACnDA,UAAU,EAAEqH,UAAU,CAACrH,UAAU;QACjC4H,IAAI,EAAEP,UAAU,CAACtF,UAAU;QAC3BuF,IAAI,EAAED,UAAU,CAACrF,UAAU;QAC3B6F,QAAQ,EAAER,UAAU,CAACpF,cAAc;QACnC6F,QAAQ,EAAET,UAAU,CAACnF;MACvB,CAAC,GAAG;IACN,CAAC;IAED,IAAIlB,eAAe,EAAE;MACnB2C,cAAc,CAAC+C,MAAM,CAAC;QAAE9C,EAAE,EAAE5C,eAAe,CAAC4C,EAAE;QAAEvB,IAAI,EAAEoF;MAAU,CAAC,CAAC;IACpE,CAAC,MAAM;MACLzE,cAAc,CAAC0D,MAAM,CAACe,SAAS,CAAC;IAClC;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzCvG,WAAW,CAACwG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAME,mBAAmB,GAAIpL,OAAO,IAAK;IACvCiI,qBAAqB,CAAC0B,MAAM,CAAC3J,OAAO,CAAC6G,EAAE,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMuB,uBAAuB,GAAIhB,SAAS,IAAK;IAC7C;IACA4B,sBAAsB,CAAC5B,SAAS,CAAC;IAEjC,MAAMiE,UAAU,GAAGC,WAAW,CAAC,YAAY;MACzC,IAAI;QACF,MAAM1F,QAAQ,GAAG,MAAMnG,WAAW,CAAC8L,mBAAmB,CAACnE,SAAS,CAAC;QACjE,MAAMnG,MAAM,GAAG2E,QAAQ,CAACN,IAAI,CAACkG,YAAY;QAEzC,IAAIvK,MAAM,CAACA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,CAACA,MAAM,KAAK,SAAS,EAAE;UACrEzB,KAAK,CAACoI,OAAO,CAAC,sDAAsD,CAAC;UACrEoB,sBAAsB,CAAC5B,SAAS,CAAC;QACnC,CAAC,MAAM,IAAInG,MAAM,CAACA,MAAM,KAAK,YAAY,EAAE;UACzC+H,sBAAsB,CAAC5B,SAAS,CAAC;QACnC;QACA;MACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;QACde,OAAO,CAACf,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;MACF;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEXjB,qBAAqB,CAAC2G,IAAI,IAAI,IAAI1G,GAAG,CAAC0G,IAAI,CAACM,GAAG,CAACrE,SAAS,EAAEiE,UAAU,CAAC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMrC,sBAAsB,GAAI5B,SAAS,IAAK;IAC5C,MAAMiE,UAAU,GAAG9G,kBAAkB,CAACmH,GAAG,CAACtE,SAAS,CAAC;IACpD,IAAIiE,UAAU,EAAE;MACdM,aAAa,CAACN,UAAU,CAAC;MACzB7G,qBAAqB,CAAC2G,IAAI,IAAI;QAC5B,MAAMS,MAAM,GAAG,IAAInH,GAAG,CAAC0G,IAAI,CAAC;QAC5BS,MAAM,CAAC1E,MAAM,CAACE,SAAS,CAAC;QACxB,OAAOwE,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA9P,KAAK,CAAC+P,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXtH,kBAAkB,CAACuH,OAAO,CAAET,UAAU,IAAK;QACzCM,aAAa,CAACN,UAAU,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAAC9G,kBAAkB,CAAC,CAAC;EAExB,MAAMwH,2BAA2B,GAAI/L,OAAO,IAAK;IAC/C;IACAgJ,sBAAsB,CAAChJ,OAAO,CAAC6G,EAAE,CAAC;;IAElC;IACA;IACA2B,6BAA6B,CAACmB,MAAM,CAAC;MACnCvC,SAAS,EAAEpH,OAAO,CAAC6G,EAAE;MACrB4B,YAAY,EAAE;QACZuD,KAAK,EAAE,kBAAkB;QAAE;QAC3BlB,QAAQ,EAAE,eAAe;QACzBmB,OAAO,EAAE;MACX;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIxG,KAAK,EAAE;IACT,oBACE7F,OAAA,CAAC5D,GAAG;MAACkQ,SAAS,EAAC,SAAS;MAAA7K,QAAA,eACtBzB,OAAA,CAACtC,KAAK;QAAC6O,QAAQ,EAAC,OAAO;QAAChL,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAEvC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACEvC,OAAA,CAAC5D,GAAG;IAACkQ,SAAS,EAAC,SAAS;IAAA7K,QAAA,gBACtBzB,OAAA,CAAC5D,GAAG;MAACmF,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFzB,OAAA,CAAC3D,UAAU;QAACmG,OAAO,EAAC,IAAI;QAACjB,EAAE,EAAE;UAAEkB,UAAU,EAAE;QAAI,CAAE;QAAAhB,QAAA,EAAC;MAElD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvC,OAAA,CAAC5D,GAAG;QAACmF,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBACnCzB,OAAA,CAACxD,MAAM;UACLgG,OAAO,EAAC,UAAU;UAClBiB,SAAS,eAAEzD,OAAA,CAACtB,WAAW;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BM,OAAO,EAAEA,CAAA,KAAM2C,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAE;UACzDhF,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA,CAACxD,MAAM;UACLgG,OAAO,EAAC,WAAW;UACnBiB,SAAS,eAAEzD,OAAA,CAAClC,OAAO;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAE4G,YAAa;UACtBlI,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELqD,SAAS,iBAAI5F,OAAA,CAACrC,cAAc;MAAC4D,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE;IAAE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAI9C4D,QAAQ,CAACqG,MAAM,KAAK,CAAC,IAAI,CAAC5G,SAAS,gBAClC5F,OAAA,CAAC1D,IAAI;MAAAmF,QAAA,eACHzB,OAAA,CAACzD,WAAW;QAACgF,EAAE,EAAE;UAAEkL,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAjL,QAAA,gBAC9CzB,OAAA,CAAChC,UAAU;UAACuD,EAAE,EAAE;YAAE4B,QAAQ,EAAE,EAAE;YAAEP,KAAK,EAAE,gBAAgB;YAAEf,EAAE,EAAE;UAAE;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpEvC,OAAA,CAAC3D,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACjB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAExC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvC,OAAA,CAAC3D,UAAU;UAACmG,OAAO,EAAC,OAAO;UAACI,KAAK,EAAC,gBAAgB;UAACrB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvC,OAAA,CAACxD,MAAM;UACLgG,OAAO,EAAC,WAAW;UACnBiB,SAAS,eAAEzD,OAAA,CAAClC,OAAO;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAE4G,YAAa;UACtBlI,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEPvC,OAAA,CAACvD,IAAI;MAACsG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvB,QAAA,EACxB0E,QAAQ,CAACwG,GAAG,CAAEvM,OAAO,iBACpBJ,OAAA,CAACvD,IAAI;QAACwG,IAAI;QAACC,EAAE,EAAE,EAAG;QAAC0J,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApL,QAAA,eAC9BzB,OAAA,CAACG,WAAW;UACVC,OAAO,EAAEA,OAAQ;UACjBC,MAAM,EAAEqJ,UAAW;UACnBpJ,QAAQ,EAAEsJ,YAAa;UACvBrJ,MAAM,EAAEyJ,UAAW;UACnBxJ,eAAe,EAAEgL,mBAAoB;UACrC/K,uBAAuB,EAAE0L;QAA4B;UAAA/J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC,GARkCnC,OAAO,CAAC6G,EAAE;QAAA7E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAS1C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDvC,OAAA,CAACpD,MAAM;MACL+G,IAAI,EAAEM,gBAAgB,IAAIE,cAAe;MACzCN,OAAO,EAAEA,CAAA,KAAM;QACbK,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,iBAAiB,CAAC,KAAK,CAAC;QACxBoC,SAAS,CAAC,CAAC;MACb,CAAE;MACFsG,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAtL,QAAA,gBAETzB,OAAA,CAACnD,WAAW;QAAA4E,QAAA,EACT4C,eAAe,GAAG,cAAc,GAAG;MAAoB;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACdvC,OAAA,CAAClD,aAAa;QAAA2E,QAAA,eACZzB,OAAA,CAAC5D,GAAG;UAACmF,EAAE,EAAE;YAAEyL,EAAE,EAAE;UAAE,CAAE;UAAAvL,QAAA,eACjBzB,OAAA,CAACvD,IAAI;YAACsG,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAvB,QAAA,gBACzBzB,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBzB,OAAA,CAAChD,SAAS;gBACR+P,SAAS;gBACTrK,KAAK,EAAC,cAAc;gBACpB4I,KAAK,EAAExG,QAAQ,CAAC7C,IAAK;gBACrBgL,QAAQ,EAAGC,CAAC,IAAK9B,gBAAgB,CAAC,MAAM,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;gBAC1D8B,QAAQ;cAAA;gBAAAhL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC0J,EAAE,EAAE,CAAE;cAAAnL,QAAA,eACvBzB,OAAA,CAAC/C,WAAW;gBAAC8P,SAAS;gBAAAtL,QAAA,gBACpBzB,OAAA,CAAC9C,UAAU;kBAAAuE,QAAA,EAAC;gBAAY;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCvC,OAAA,CAAC7C,MAAM;kBACLmO,KAAK,EAAExG,QAAQ,CAACE,YAAa;kBAC7BiI,QAAQ,EAAGC,CAAC,IAAK9B,gBAAgB,CAAC,cAAc,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;kBAClE5I,KAAK,EAAC,cAAc;kBAAAjB,QAAA,gBAEpBzB,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,QAAQ;oBAAA7J,QAAA,EAAC;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,SAAS;oBAAA7J,QAAA,EAAC;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5CvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,MAAM;oBAAA7J,QAAA,EAAC;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC0J,EAAE,EAAE,CAAE;cAAAnL,QAAA,eACvBzB,OAAA,CAAC/C,WAAW;gBAAC8P,SAAS;gBAAAtL,QAAA,gBACpBzB,OAAA,CAAC9C,UAAU;kBAAAuE,QAAA,EAAC;gBAAiB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CvC,OAAA,CAAC7C,MAAM;kBACLmO,KAAK,EAAExG,QAAQ,CAACI,iBAAkB;kBAClC+H,QAAQ,EAAGC,CAAC,IAAK9B,gBAAgB,CAAC,mBAAmB,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;kBACvE5I,KAAK,EAAC,mBAAmB;kBAAAjB,QAAA,gBAEzBzB,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,WAAW;oBAAA7J,QAAA,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,UAAU;oBAAA7J,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,UAAU;oBAAA7J,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,UAAU;oBAAA7J,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC0J,EAAE,EAAE,CAAE;cAAAnL,QAAA,eACvBzB,OAAA,CAAC/C,WAAW;gBAAC8P,SAAS;gBAAAtL,QAAA,gBACpBzB,OAAA,CAAC9C,UAAU;kBAAAuE,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCvC,OAAA,CAAC7C,MAAM;kBACLmO,KAAK,EAAExG,QAAQ,CAAC1B,QAAS;kBACzB6J,QAAQ,EAAGC,CAAC,IAAK9B,gBAAgB,CAAC,UAAU,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;kBAC9D5I,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhBzB,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,KAAK;oBAAA7J,QAAA,EAAC;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpCvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,kBAAkB;oBAAA7J,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9DvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,kBAAkB;oBAAA7J,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9DvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,eAAe;oBAAA7J,QAAA,EAAC;kBAAa;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC0J,EAAE,EAAE,CAAE;cAAAnL,QAAA,eACvBzB,OAAA,CAAC/C,WAAW;gBAAC8P,SAAS;gBAAAtL,QAAA,gBACpBzB,OAAA,CAAC9C,UAAU;kBAAAuE,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCvC,OAAA,CAAC7C,MAAM;kBACLmO,KAAK,EAAExG,QAAQ,CAACxB,QAAS;kBACzB2J,QAAQ,EAAGC,CAAC,IAAK9B,gBAAgB,CAAC,UAAU,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;kBAC9D5I,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhBzB,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,OAAO;oBAAA7J,QAAA,EAAC;kBAAY;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC/CvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,OAAO;oBAAA7J,QAAA,EAAC;kBAAU;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC7CvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,OAAO;oBAAA7J,QAAA,EAAC;kBAAoB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACvDvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,OAAO;oBAAA7J,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBzB,OAAA,CAAChD,SAAS;gBACR+P,SAAS;gBACTrK,KAAK,EAAC,uBAAuB;gBAC7B4I,KAAK,EAAExG,QAAQ,CAACG,UAAW;gBAC3BgI,QAAQ,EAAGC,CAAC,IAAK9B,gBAAgB,CAAC,YAAY,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;gBAChE+B,WAAW,EAAC;cAAsC;gBAAAjL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBzB,OAAA,CAAC3D,UAAU;gBAACmG,OAAO,EAAC,IAAI;gBAACjB,EAAE,EAAE;kBAAEM,EAAE,EAAE,CAAC;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAAC;cAEzD;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEPvC,OAAA,CAACvD,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC0J,EAAE,EAAE,CAAE;cAAAnL,QAAA,eACvBzB,OAAA,CAAC/C,WAAW;gBAAC8P,SAAS;gBAAAtL,QAAA,gBACpBzB,OAAA,CAAC9C,UAAU;kBAAAuE,QAAA,EAAC;gBAAU;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCvC,OAAA,CAAC7C,MAAM;kBACLmO,KAAK,EAAExG,QAAQ,CAACzB,UAAW;kBAC3B4J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMI,SAAS,GAAGJ,CAAC,CAACC,MAAM,CAAC7B,KAAK;oBAChCF,gBAAgB,CAAC,YAAY,EAAEkC,SAAS,CAAC;oBACzClC,gBAAgB,CAAC,eAAe,EAAEkC,SAAS,KAAK,UAAU,CAAC;kBAC7D,CAAE;kBACF5K,KAAK,EAAC,YAAY;kBAAAjB,QAAA,gBAElBzB,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,UAAU;oBAAA7J,QAAA,eACxBzB,OAAA,CAAC5D,GAAG;sBAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAwB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjEvC,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,MAAM;oBAAA7J,QAAA,eACpBzB,OAAA,CAAC5D,GAAG;sBAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC7CvC,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,OAAO;oBAAA7J,QAAA,eACrBzB,OAAA,CAAC5D,GAAG;sBAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAK;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC9CvC,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,QAAQ;oBAAA7J,QAAA,eACtBzB,OAAA,CAAC5D,GAAG;sBAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAM;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC/CvC,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXvC,OAAA,CAAC5C,QAAQ;oBAACkO,KAAK,EAAC,KAAK;oBAAA7J,QAAA,eACnBzB,OAAA,CAAC5D,GAAG;sBAAAqF,QAAA,gBACFzB,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAG;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC5CvC,OAAA,CAAC3D,UAAU;wBAACmG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACTvC,OAAA,CAACpC,cAAc;kBAAA6D,QAAA,GACZqD,QAAQ,CAACzB,UAAU,KAAK,UAAU,IAAI,yBAAyB,EAC/DyB,QAAQ,CAACzB,UAAU,KAAK,MAAM,IAAI,iCAAiC,EACnEyB,QAAQ,CAACzB,UAAU,KAAK,OAAO,IAAI,sCAAsC,EACzEyB,QAAQ,CAACzB,UAAU,KAAK,QAAQ,IAAI,wCAAwC,EAC5EyB,QAAQ,CAACzB,UAAU,KAAK,KAAK,IAAI,4CAA4C;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAENuC,QAAQ,CAACzB,UAAU,KAAK,UAAU,iBACjCrD,OAAA,CAAAE,SAAA;cAAAuB,QAAA,gBACEzB,OAAA,CAACvD,IAAI;gBAACwG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC0J,EAAE,EAAE,CAAE;gBAAAnL,QAAA,eACvBzB,OAAA,CAAChD,SAAS;kBACR+P,SAAS;kBACTrK,KAAK,EAAC,YAAY;kBAClB4I,KAAK,EAAExG,QAAQ,CAACM,UAAW;kBAC3B6H,QAAQ,EAAGC,CAAC,IAAK9B,gBAAgB,CAAC,YAAY,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;kBAChE8B,QAAQ;kBACRC,WAAW,EACTvI,QAAQ,CAACzB,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC,0CACL;kBACDkK,UAAU,EAAC;gBAA+C;kBAAAnL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPvC,OAAA,CAACvD,IAAI;gBAACwG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC0J,EAAE,EAAE,CAAE;gBAAAnL,QAAA,eACvBzB,OAAA,CAAChD,SAAS;kBACR+P,SAAS;kBACTrK,KAAK,EAAC,YAAY;kBAClB4I,KAAK,EAAExG,QAAQ,CAACO,UAAW;kBAC3B4H,QAAQ,EAAGC,CAAC,IAAK9B,gBAAgB,CAAC,YAAY,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;kBAChEkC,IAAI,EAAC,QAAQ;kBACbJ,QAAQ;kBACRC,WAAW,EACTvI,QAAQ,CAACzB,UAAU,KAAK,MAAM,GAAG,kBAAkB,GACnDyB,QAAQ,CAACzB,UAAU,KAAK,OAAO,GAAG,iBAAiB,GACnDyB,QAAQ,CAACzB,UAAU,KAAK,QAAQ,GAAG,kBAAkB,GACrDyB,QAAQ,CAACzB,UAAU,KAAK,KAAK,GAAG,UAAU,GAAG,aAC9C;kBACDkK,UAAU,EAAE,UAAUzI,QAAQ,CAACzB,UAAU,CAAClB,WAAW,CAAC,CAAC,QAAS;kBAChEsL,UAAU,EAAE;oBAAEC,GAAG,EAAE,CAAC;oBAAEC,GAAG,EAAE;kBAAM;gBAAE;kBAAAvL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPvC,OAAA,CAACvD,IAAI;gBAACwG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC0J,EAAE,EAAE,CAAE;gBAAAnL,QAAA,eACvBzB,OAAA,CAAChD,SAAS;kBACR+P,SAAS;kBACTrK,KAAK,EAAEoC,QAAQ,CAACzB,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEiI,KAAK,EAAExG,QAAQ,CAACQ,cAAe;kBAC/B2H,QAAQ,EAAGC,CAAC,IAAK9B,gBAAgB,CAAC,gBAAgB,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;kBACpE+B,WAAW,EACTvI,QAAQ,CAACzB,UAAU,KAAK,KAAK,GACzB,iCAAiC,GACjC,2CACL;kBACDkK,UAAU,EACRzI,QAAQ,CAACzB,UAAU,KAAK,KAAK,GACzB,8BAA8B,GAC9B;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPvC,OAAA,CAACvD,IAAI;gBAACwG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC0J,EAAE,EAAE,CAAE;gBAAAnL,QAAA,eACvBzB,OAAA,CAAChD,SAAS;kBACR+P,SAAS;kBACTrK,KAAK,EAAEoC,QAAQ,CAACzB,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEiI,KAAK,EAAExG,QAAQ,CAACS,cAAe;kBAC/B0H,QAAQ,EAAGC,CAAC,IAAK9B,gBAAgB,CAAC,gBAAgB,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;kBACpEkC,IAAI,EAAC,UAAU;kBACfH,WAAW,EACTvI,QAAQ,CAACzB,UAAU,KAAK,KAAK,GACzB,gDAAgD,GAChD,2CACL;kBACDkK,UAAU,EACRzI,QAAQ,CAACzB,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPvC,OAAA,CAACvD,IAAI;gBAACwG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAzB,QAAA,eAChBzB,OAAA,CAAC5D,GAAG;kBAACmF,EAAE,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAE8B,GAAG,EAAE,CAAC;oBAAE5B,UAAU,EAAE;kBAAS,CAAE;kBAAAH,QAAA,gBACzDzB,OAAA,CAACxD,MAAM;oBACLgG,OAAO,EAAC,UAAU;oBAClBG,IAAI,EAAC,OAAO;oBACZE,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAIwB,eAAe,EAAE;wBACnBkD,iBAAiB,CAACwC,MAAM,CAAC1F,eAAe,CAAC4C,EAAE,CAAC;sBAC9C,CAAC,MAAM;wBACL;wBACA,MAAMgD,SAAS,GAAG,GAAGnF,QAAQ,CAACzB,UAAU,CAAClB,WAAW,CAAC,CAAC,WAAW2C,QAAQ,CAACM,UAAU,IAAIN,QAAQ,CAACO,UAAU,EAAE;wBAC7G,MAAMuI,QAAQ,GAAG9I,QAAQ,CAACQ,cAAc,GAAG,WAAWR,QAAQ,CAACQ,cAAc,GAAG,GAAG,YAAY;wBAC/F1F,KAAK,CAACgI,IAAI,CAAC,yBAAyBqC,SAAS,GAAG2D,QAAQ,4CAA4C,EAAE;0BAAErD,QAAQ,EAAE;wBAAK,CAAC,CAAC;sBAC3H;oBACF,CAAE;oBACFsD,QAAQ,EAAE,CAAC/I,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,IAAIkC,iBAAiB,CAAC3B,SAAU;oBACtFrE,EAAE,EAAE;sBAAEmC,aAAa,EAAE;oBAAO,CAAE;oBAAAjC,QAAA,EAE7B8F,iBAAiB,CAAC3B,SAAS,GAAG,YAAY,GAAGvB,eAAe,GAAG,iBAAiB,GAAG;kBAAgB;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC,eACTvC,OAAA,CAAC3D,UAAU;oBAACmG,OAAO,EAAC,SAAS;oBAACI,KAAK,EAAC,gBAAgB;oBAAAnB,QAAA,EAAC;kBAErD;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,eACP,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBvC,OAAA,CAACjD,aAAa;QAAA0E,QAAA,gBACZzB,OAAA,CAACxD,MAAM;UACLqG,OAAO,EAAEA,CAAA,KAAM;YACbqB,mBAAmB,CAAC,KAAK,CAAC;YAC1BE,iBAAiB,CAAC,KAAK,CAAC;YACxBoC,SAAS,CAAC,CAAC;UACb,CAAE;UAAA/E,QAAA,EACH;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvC,OAAA,CAACxD,MAAM;UACLgG,OAAO,EAAC,WAAW;UACnBK,OAAO,EAAE4H,YAAa;UACtBoD,QAAQ,EAAE,CAAC/I,QAAQ,CAAC7C,IAAI,IAAIoE,cAAc,CAACT,SAAS,IAAIoB,cAAc,CAACpB,SAAU;UAAAnE,QAAA,EAEhF4C,eAAe,GAAG,QAAQ,GAAG;QAAQ;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTvC,OAAA,CAACpD,MAAM;MACL+G,IAAI,EAAEY,qBAAsB;MAC5BV,OAAO,EAAEA,CAAA,KAAMW,wBAAwB,CAAC,KAAK,CAAE;MAC/CsI,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAtL,QAAA,gBAETzB,OAAA,CAACnD,WAAW;QAAC0E,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAE4B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBACjEzB,OAAA,CAACZ,YAAY;UAACmC,EAAE,EAAE;YAAEqB,KAAK,EAAE;UAAU;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+BAE5C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACdvC,OAAA,CAAClD,aAAa;QAAA2E,QAAA,gBACZzB,OAAA,CAACtC,KAAK;UAAC6O,QAAQ,EAAC,MAAM;UAAChL,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAEtC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAERvC,OAAA,CAAC5D,GAAG;UAAC0R,SAAS,EAAC,IAAI;UAACvM,EAAE,EAAE;YAAEwM,EAAE,EAAE;UAAE,CAAE;UAAAtM,QAAA,EAC/BgD,iBAAiB,CAACkI,GAAG,CAAC,CAACqB,WAAW,EAAEC,KAAK,kBACxCjO,OAAA,CAAC5D,GAAG;YAAC0R,SAAS,EAAC,IAAI;YAAavM,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,eAC5CzB,OAAA,CAAC3D,UAAU;cAACmG,OAAO,EAAC,OAAO;cAAAf,QAAA,EAAEuM;YAAW;cAAA5L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC,GAD/B0L,KAAK;YAAA7L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEzB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvC,OAAA,CAACtC,KAAK;UAAC6O,QAAQ,EAAC,SAAS;UAAChL,EAAE,EAAE;YAAEgC,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,eACtCzB,OAAA,CAAC3D,UAAU;YAACmG,OAAO,EAAC,OAAO;YAAAf,QAAA,gBACzBzB,OAAA;cAAAyB,QAAA,EAAQ;YAAU;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,kJAE7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAChBvC,OAAA,CAACjD,aAAa;QAAA0E,QAAA,eACZzB,OAAA,CAACxD,MAAM;UAACqG,OAAO,EAAEA,CAAA,KAAM2B,wBAAwB,CAAC,KAAK,CAAE;UAAA/C,QAAA,EAAC;QAExD;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACyB,GAAA,CAvzBQD,QAAQ;EAAA,QAsBKpE,cAAc,EACVG,MAAM,EAGmBL,QAAQ,EAgBlCC,WAAW,EAiBXA,WAAW,EAoBXA,WAAW,EAaRA,WAAW,EAiCPA,WAAW,EAmCHA,WAAW,EAsBVA,WAAW;AAAA;AAAAwO,GAAA,GAtL3CnK,QAAQ;AAyzBjB,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAAoK,GAAA;AAAAC,YAAA,CAAArK,EAAA;AAAAqK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}