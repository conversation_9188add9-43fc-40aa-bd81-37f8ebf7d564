// Test script for Facebook login functionality
const axios = require('axios');

const BASE_URL = 'http://localhost:8000';

async function testFacebookLogin() {
  console.log('🔍 Testing Facebook Login Functionality');
  console.log('=' * 50);

  try {
    // Step 1: Get profiles
    console.log('\n1. Getting profiles...');
    const profilesResponse = await axios.get(`${BASE_URL}/api/profiles`);
    
    let profiles = [];
    if (Array.isArray(profilesResponse.data)) {
      profiles = profilesResponse.data;
    } else if (profilesResponse.data.profiles) {
      profiles = profilesResponse.data.profiles;
    }
    
    console.log(`✅ Found ${profiles.length} profiles`);
    
    if (profiles.length === 0) {
      console.log('❌ No profiles found. Creating a test profile...');
      
      // Create a test profile
      const createData = {
        name: `Test FB Profile ${Date.now()}`,
        user_agent: "Mozilla/5.0 (Test Browser)",
        browser_config: {
          browser_type: "chrome",
          screen_resolution: "1920x1080",
          timezone: "UTC",
          language: "en-US"
        },
        proxy_config: {
          proxy_type: "no_proxy",
          host: null,
          port: null,
          username: null,
          password: null
        }
      };
      
      const createResponse = await axios.post(`${BASE_URL}/api/profiles`, createData);
      console.log(`✅ Created test profile: ${createResponse.data.name} (ID: ${createResponse.data.id})`);
      profiles = [createResponse.data];
    }
    
    const testProfile = profiles[0];
    const profileId = testProfile.id;
    
    console.log(`\nUsing profile: ${testProfile.name} (ID: ${profileId})`);
    
    // Step 2: Check initial Facebook status
    console.log('\n2. Checking initial Facebook status...');
    try {
      const statusResponse = await axios.get(`${BASE_URL}/api/profiles/${profileId}/facebook-status`);
      console.log(`✅ Facebook status retrieved:`);
      console.log(`   - Logged in: ${statusResponse.data.is_logged_in}`);
      console.log(`   - Email: ${statusResponse.data.facebook_email || 'None'}`);
      console.log(`   - Username: ${statusResponse.data.facebook_username || 'None'}`);
      console.log(`   - Login count: ${statusResponse.data.login_count}`);
    } catch (error) {
      console.log(`❌ Facebook status check failed: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Step 3: Initiate Facebook login
    console.log('\n3. Initiating Facebook login...');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/profiles/${profileId}/facebook-login`);
      console.log(`✅ Facebook login initiated:`);
      console.log(`   - Status: ${loginResponse.data.status}`);
      console.log(`   - Message: ${loginResponse.data.message}`);
      console.log(`   - Login URL: ${loginResponse.data.login_url}`);
      
      if (loginResponse.data.instructions) {
        console.log(`   - Instructions:`);
        loginResponse.data.instructions.forEach((instruction, index) => {
          console.log(`     ${index + 1}. ${instruction}`);
        });
      }
      
      if (loginResponse.data.zendriver_config) {
        console.log(`   - Browser config ready: ✅`);
        console.log(`   - Profile path: ${loginResponse.data.browser_config?.profile_path || 'N/A'}`);
      }
      
    } catch (error) {
      console.log(`❌ Facebook login initiation failed: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Step 4: Simulate login completion
    console.log('\n4. Simulating Facebook login completion...');
    try {
      const mockFacebookData = {
        email: "<EMAIL>",
        username: "test_user",
        user_id: "123456789",
        name: "Test User"
      };
      
      const completeResponse = await axios.post(
        `${BASE_URL}/api/profiles/${profileId}/facebook-login-complete`,
        mockFacebookData
      );
      
      console.log(`✅ Facebook login completed:`);
      console.log(`   - Status: ${completeResponse.data.status}`);
      console.log(`   - Message: ${completeResponse.data.message}`);
      console.log(`   - Profile status: ${completeResponse.data.profile_status}`);
      console.log(`   - Login count: ${completeResponse.data.login_count}`);
      console.log(`   - Facebook email: ${completeResponse.data.facebook_email}`);
      
    } catch (error) {
      console.log(`❌ Facebook login completion failed: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Step 5: Check final Facebook status
    console.log('\n5. Checking final Facebook status...');
    try {
      const finalStatusResponse = await axios.get(`${BASE_URL}/api/profiles/${profileId}/facebook-status`);
      console.log(`✅ Final Facebook status:`);
      console.log(`   - Logged in: ${finalStatusResponse.data.is_logged_in}`);
      console.log(`   - Email: ${finalStatusResponse.data.facebook_email || 'None'}`);
      console.log(`   - Username: ${finalStatusResponse.data.facebook_username || 'None'}`);
      console.log(`   - User ID: ${finalStatusResponse.data.facebook_user_id || 'None'}`);
      console.log(`   - Login count: ${finalStatusResponse.data.login_count}`);
      console.log(`   - Profile status: ${finalStatusResponse.data.status}`);
      console.log(`   - Last used: ${finalStatusResponse.data.last_used || 'Never'}`);
    } catch (error) {
      console.log(`❌ Final status check failed: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Step 6: Check updated profile in list
    console.log('\n6. Checking updated profile in list...');
    const updatedProfilesResponse = await axios.get(`${BASE_URL}/api/profiles`);
    let updatedProfiles = [];
    if (Array.isArray(updatedProfilesResponse.data)) {
      updatedProfiles = updatedProfilesResponse.data;
    } else if (updatedProfilesResponse.data.profiles) {
      updatedProfiles = updatedProfilesResponse.data.profiles;
    }
    
    const updatedProfile = updatedProfiles.find(p => p.id === profileId);
    if (updatedProfile) {
      console.log(`✅ Profile updated in list:`);
      console.log(`   - Name: ${updatedProfile.name}`);
      console.log(`   - Status: ${updatedProfile.status}`);
      console.log(`   - Facebook email: ${updatedProfile.facebook_email || 'None'}`);
      console.log(`   - Facebook username: ${updatedProfile.facebook_username || 'None'}`);
    } else {
      console.log(`❌ Profile not found in updated list`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
  
  console.log('\n' + '=' * 50);
  console.log('🎉 Facebook login testing completed!');
  console.log('\nNext steps:');
  console.log('1. Start the backend server');
  console.log('2. Open the frontend application');
  console.log('3. Click "Login to Facebook" on a profile');
  console.log('4. Follow the instructions in the dialog');
  console.log('5. Click "Complete Login" after manual login');
  console.log('6. Profile status should change to "Ready"');
}

// Run the test
testFacebookLogin();
