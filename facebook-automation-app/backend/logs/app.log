2025-07-06 09:16:16 | INFO | __main__:<module>:177 | Starting server on 127.0.0.1:8000
2025-07-06 09:16:17 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:16:17 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:16:17 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:16:17 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:18:11 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:18:11 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:18:31 | INFO | __main__:<module>:177 | Starting server on 127.0.0.1:8000
2025-07-06 09:18:31 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:18:31 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:18:31 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:18:31 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:10 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:10 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:10 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:21 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:21 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:21 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:21 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:51 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:19:51 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:19:51 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:05 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:05 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:05 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:startup_event:55 | Starting Facebook Automation Backend Server...
2025-07-06 09:20:20 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:20 | INFO | main:startup_event:60 | Backend server started successfully
2025-07-06 09:20:33 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:20:33 | INFO | main:shutdown_event:65 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:41:05 | INFO | __main__:<module>:227 | Starting server on 127.0.0.1:8000
2025-07-06 09:41:06 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:06 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:06 | ERROR | database.connection:check_database_connection:76 | Database connection failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-06 09:41:06 | ERROR | database.connection:check_database_connection:76 | Database connection failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-06 09:41:06 | ERROR | main:startup_event:82 | Database connection failed
2025-07-06 09:41:06 | ERROR | main:startup_event:82 | Database connection failed
2025-07-06 09:41:06 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:06 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751769668791-4413108288: GET /api/analytics/dashboard
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751769668791-4413108288: GET /api/analytics/dashboard
2025-07-06 09:41:08 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:08 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:08 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:08 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751769668791-4413108288: 500
2025-07-06 09:41:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751769668791-4413108288: 500
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751769669809-4416528832: GET /api/analytics/dashboard
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751769669809-4416528832: GET /api/analytics/dashboard
2025-07-06 09:41:09 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:09 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:09 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:09 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751769669809-4416528832: 500
2025-07-06 09:41:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751769669809-4416528832: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671916-4416567376: GET /api/analytics/dashboard
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671916-4416567376: GET /api/analytics/dashboard
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671926-4417687712: GET /api/profiles
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671926-4417687712: GET /api/profiles
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671927-4417710880: GET /api/campaigns
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671927-4417710880: GET /api/campaigns
2025-07-06 09:41:11 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671929-4417779264: GET /api/scraping/sessions
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671929-4417779264: GET /api/scraping/sessions
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671926-4417687712: 200
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671926-4417687712: 200
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671927-4417710880: 307
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671927-4417710880: 307
2025-07-06 09:41:11 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671916-4416567376: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671916-4416567376: 500
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671929-4417779264: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671929-4417779264: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671953-4416419680: GET /api/campaigns/
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751769671953-4416419680: GET /api/campaigns/
2025-07-06 09:41:11 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671953-4416419680: 500
2025-07-06 09:41:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751769671953-4416419680: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418204336: GET /api/analytics/dashboard
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418204336: GET /api/analytics/dashboard
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418221440: GET /api/scraping/sessions
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751769672959-4418221440: GET /api/scraping/sessions
2025-07-06 09:41:12 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(*) AS count_1 
FROM (SELECT profiles.id AS profiles_id, profiles.name AS profiles_name, profiles.description AS profiles_description, profiles.user_agent AS profiles_user_agent, profiles.screen_resolution AS profiles_screen_resolution, profiles.timezone AS profiles_timezone, profiles.language AS profiles_language, profiles.proxy_host AS profiles_proxy_host, profiles.proxy_port AS profiles_proxy_port, profiles.proxy_username AS profiles_proxy_username, profiles.proxy_password AS profiles_proxy_password, profiles.proxy_type AS profiles_proxy_type, profiles.facebook_email AS profiles_facebook_email, profiles.facebook_password AS profiles_facebook_password, profiles.facebook_user_id AS profiles_facebook_user_id, profiles.facebook_username AS profiles_facebook_username, profiles.status AS profiles_status, profiles.last_used AS profiles_last_used, profiles.created_at AS profiles_created_at, profiles.updated_at AS profiles_updated_at, profiles.profile_path AS profiles_profile_path 
FROM profiles) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: (sqlite3.OperationalError) no such table: scraping_sessions
[SQL: SELECT count(*) AS count_1 
FROM (SELECT scraping_sessions.id AS scraping_sessions_id, scraping_sessions.name AS scraping_sessions_name, scraping_sessions.target_url AS scraping_sessions_target_url, scraping_sessions.target_type AS scraping_sessions_target_type, scraping_sessions.scraping_method AS scraping_sessions_scraping_method, scraping_sessions.max_results AS scraping_sessions_max_results, scraping_sessions.filters AS scraping_sessions_filters, scraping_sessions.status AS scraping_sessions_status, scraping_sessions.progress AS scraping_sessions_progress, scraping_sessions.total_found AS scraping_sessions_total_found, scraping_sessions.total_scraped AS scraping_sessions_total_scraped, scraping_sessions.unique_uids AS scraping_sessions_unique_uids, scraping_sessions.created_at AS scraping_sessions_created_at, scraping_sessions.updated_at AS scraping_sessions_updated_at, scraping_sessions.started_at AS scraping_sessions_started_at, scraping_sessions.completed_at AS scraping_sessions_completed_at, scraping_sessions.error_message AS scraping_sessions_error_message, scraping_sessions.retry_count AS scraping_sessions_retry_count, scraping_sessions.profile_id AS scraping_sessions_profile_id, scraping_sessions.campaign_id AS scraping_sessions_campaign_id 
FROM scraping_sessions) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418204336: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418204336: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418221440: 500
2025-07-06 09:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751769672959-4418221440: 500
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673019-4418222208: GET /api/campaigns
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673019-4418222208: GET /api/campaigns
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673019-4418222208: 307
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673019-4418222208: 307
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673023-4418081792: GET /api/campaigns/
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769673023-4418081792: GET /api/campaigns/
2025-07-06 09:41:13 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:13 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: (sqlite3.OperationalError) no such table: campaigns
[SQL: SELECT count(*) AS count_1 
FROM (SELECT campaigns.id AS campaigns_id, campaigns.name AS campaigns_name, campaigns.description AS campaigns_description, campaigns.campaign_type AS campaigns_campaign_type, campaigns.target_audience AS campaigns_target_audience, campaigns.message_template AS campaigns_message_template, campaigns.scheduled_start AS campaigns_scheduled_start, campaigns.scheduled_end AS campaigns_scheduled_end, campaigns.status AS campaigns_status, campaigns.progress AS campaigns_progress, campaigns.total_targets AS campaigns_total_targets, campaigns.completed_targets AS campaigns_completed_targets, campaigns.successful_actions AS campaigns_successful_actions, campaigns.failed_actions AS campaigns_failed_actions, campaigns.created_at AS campaigns_created_at, campaigns.updated_at AS campaigns_updated_at, campaigns.started_at AS campaigns_started_at, campaigns.completed_at AS campaigns_completed_at, campaigns.profile_id AS campaigns_profile_id 
FROM campaigns) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 09:41:13 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:13 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673023-4418081792: 500
2025-07-06 09:41:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769673023-4418081792: 500
2025-07-06 09:41:42 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:41:42 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:41:43 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:43 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:41:43 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:41:43 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:41:43 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:41:43 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:41:43 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:41:43 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:41:43 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:43 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751769703325-**********: GET /api/analytics/dashboard
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751769703325-**********: GET /api/analytics/dashboard
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751769703325-**********: 200
2025-07-06 09:41:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751769703325-**********: 200
2025-07-06 09:41:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751769713783-**********: GET /health
2025-07-06 09:41:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751769713783-**********: GET /health
2025-07-06 09:41:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751769713783-**********: 200
2025-07-06 09:41:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751769713783-**********: 200
2025-07-06 09:42:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769733352-**********: GET /api/analytics/dashboard
2025-07-06 09:42:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751769733352-**********: GET /api/analytics/dashboard
2025-07-06 09:42:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769733352-**********: 200
2025-07-06 09:42:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751769733352-**********: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734721-**********: GET /api/analytics/dashboard
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734721-**********: GET /api/analytics/dashboard
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419901376: GET /api/profiles
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419901376: GET /api/profiles
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419903344: GET /api/campaigns
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419903344: GET /api/campaigns
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419950432: GET /api/scraping/sessions
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734722-4419950432: GET /api/scraping/sessions
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419901376: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419901376: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419903344: 307
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419903344: 307
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734721-**********: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734721-**********: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419950432: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734722-4419950432: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734745-4412821408: GET /api/campaigns/
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751769734745-4412821408: GET /api/campaigns/
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734745-4412821408: 200
2025-07-06 09:42:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751769734745-4412821408: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748557-4412820928: GET /api/analytics/dashboard
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748557-4412820928: GET /api/analytics/dashboard
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748557-4419769200: GET /api/profiles
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748557-4419769200: GET /api/profiles
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748558-4419867888: GET /api/campaigns
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748558-4419867888: GET /api/campaigns
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748558-4419903056: GET /api/scraping/sessions
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748558-4419903056: GET /api/scraping/sessions
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748557-4419769200: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748557-4419769200: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748558-4419867888: 307
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748558-4419867888: 307
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748557-4412820928: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748557-4412820928: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748558-4419903056: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748558-4419903056: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748575-4419899456: GET /api/campaigns/
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751769748575-4419899456: GET /api/campaigns/
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748575-4419899456: 200
2025-07-06 09:42:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751769748575-4419899456: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753708-4419771120: GET /api/analytics/dashboard
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753708-4419771120: GET /api/analytics/dashboard
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419912704: GET /api/profiles
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419912704: GET /api/profiles
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419951104: GET /api/campaigns
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419951104: GET /api/campaigns
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419984496: GET /api/scraping/sessions
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753709-4419984496: GET /api/scraping/sessions
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419912704: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419912704: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419951104: 307
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419951104: 307
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753708-4419771120: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753708-4419771120: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419984496: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753709-4419984496: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753721-4419866928: GET /api/campaigns/
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751769753721-4419866928: GET /api/campaigns/
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753721-4419866928: 200
2025-07-06 09:42:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751769753721-4419866928: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757432-4419982576: GET /api/profiles
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757432-4419982576: GET /api/profiles
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757434-4419982528: GET /api/profiles
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757434-4419982528: GET /api/profiles
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757434-4420524352: GET /api/scraping/sessions
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757434-4420524352: GET /api/scraping/sessions
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757435-4420549072: GET /api/scraping/sessions
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757435-4420549072: GET /api/scraping/sessions
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757436-4420575584: GET /api/campaigns
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757436-4420575584: GET /api/campaigns
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757432-4419982576: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757432-4419982576: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757437-4420611520: GET /api/campaigns
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757437-4420611520: GET /api/campaigns
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757434-4419982528: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757434-4419982528: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757436-4420575584: 307
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757436-4420575584: 307
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757437-4420611520: 307
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757437-4420611520: 307
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757434-4420524352: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757434-4420524352: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757435-4420549072: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757435-4420549072: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757466-4420466816: GET /api/campaigns/
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757466-4420466816: GET /api/campaigns/
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757468-4420527296: GET /api/campaigns/
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751769757468-4420527296: GET /api/campaigns/
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757466-4420466816: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757466-4420466816: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757468-4420527296: 200
2025-07-06 09:42:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751769757468-4420527296: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760789-4420427840: GET /api/analytics/dashboard
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760789-4420427840: GET /api/analytics/dashboard
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420428272: GET /api/profiles
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420428272: GET /api/profiles
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420526144: GET /api/scraping/sessions
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420526144: GET /api/scraping/sessions
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420510672: GET /api/campaigns
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760790-4420510672: GET /api/campaigns
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420428272: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420428272: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420510672: 307
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420510672: 307
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760789-4420427840: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760789-4420427840: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420526144: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760790-4420526144: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760801-4420431392: GET /api/campaigns/
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751769760801-4420431392: GET /api/campaigns/
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760801-4420431392: 200
2025-07-06 09:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751769760801-4420431392: 200
2025-07-06 09:45:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751769921189-4420512736: GET /api/profiles
2025-07-06 09:45:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751769921189-4420512736: GET /api/profiles
2025-07-06 09:45:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751769921189-4420512736: 200
2025-07-06 09:45:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751769921189-4420512736: 200
2025-07-06 09:45:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751769927369-4419901040: GET /api/profiles/stats
2025-07-06 09:45:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751769927369-4419901040: GET /api/profiles/stats
2025-07-06 09:45:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751769927369-4419901040: 200
2025-07-06 09:45:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751769927369-4419901040: 200
2025-07-06 09:45:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751769935127-4420511008: POST /api/profiles
2025-07-06 09:45:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751769935127-4420511008: POST /api/profiles
2025-07-06 09:45:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751769935127-4420511008: 200
2025-07-06 09:45:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751769935127-4420511008: 200
2025-07-06 09:45:57 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:45:57 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:45:58 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:45:58 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:45:58 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:45:58 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:45:58 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:45:58 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:45:58 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:45:58 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:45:58 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:45:58 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:46:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751769968358-4360631872: POST /api/profiles
2025-07-06 09:46:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751769968358-4360631872: POST /api/profiles
2025-07-06 09:46:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751769968358-4360631872: 307
2025-07-06 09:46:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751769968358-4360631872: 307
2025-07-06 09:46:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751769978929-**********: POST /api/profiles
2025-07-06 09:46:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751769978929-**********: POST /api/profiles
2025-07-06 09:46:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751769978929-**********: 307
2025-07-06 09:46:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751769978929-**********: 307
2025-07-06 09:46:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751769987532-**********: GET /health
2025-07-06 09:46:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751769987532-**********: GET /health
2025-07-06 09:46:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751769987532-**********: 200
2025-07-06 09:46:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751769987532-**********: 200
2025-07-06 09:46:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751769994111-**********: POST /api/profiles
2025-07-06 09:46:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751769994111-**********: POST /api/profiles
2025-07-06 09:46:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751769994111-**********: 307
2025-07-06 09:46:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751769994111-**********: 307
2025-07-06 09:46:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751770001207-**********: POST /api/profiles/
2025-07-06 09:46:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751770001207-**********: POST /api/profiles/
2025-07-06 09:46:41 | INFO | api.profiles:create_profile:93 | Created new profile: Test Profile (3d0762db-4480-45c3-abea-f4f6b9cb88b8)
2025-07-06 09:46:41 | INFO | api.profiles:create_profile:93 | Created new profile: Test Profile (3d0762db-4480-45c3-abea-f4f6b9cb88b8)
2025-07-06 09:46:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751770001207-**********: 200
2025-07-06 09:46:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751770001207-**********: 200
2025-07-06 09:46:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751770007557-4363761552: GET /api/profiles/
2025-07-06 09:46:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751770007557-4363761552: GET /api/profiles/
2025-07-06 09:46:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751770007557-4363761552: 200
2025-07-06 09:46:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751770007557-4363761552: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014256-4364132752: GET /api/analytics/dashboard
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014256-4364132752: GET /api/analytics/dashboard
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014257-4360663680: GET /api/profiles
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014257-4360663680: GET /api/profiles
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014258-4364174432: GET /api/campaigns
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014258-4364174432: GET /api/campaigns
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014259-4364195776: GET /api/scraping/sessions
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014259-4364195776: GET /api/scraping/sessions
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014257-4360663680: 307
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014257-4360663680: 307
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014258-4364174432: 307
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014258-4364174432: 307
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014256-4364132752: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014256-4364132752: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014259-4364195776: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014259-4364195776: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014299-4362963936: GET /api/profiles/
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014299-4362963936: GET /api/profiles/
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014300-4364173712: GET /api/campaigns/
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770014300-4364173712: GET /api/campaigns/
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014299-4362963936: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014299-4362963936: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014300-4364173712: 200
2025-07-06 09:46:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770014300-4364173712: 200
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021203-4364132464: GET /api/profiles
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021203-4364132464: GET /api/profiles
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021203-4364132464: 307
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021203-4364132464: 307
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021205-4364197408: GET /api/profiles
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021205-4364197408: GET /api/profiles
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021205-4364197408: 307
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021205-4364197408: 307
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021223-4363845792: GET /api/profiles/
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021223-4363845792: GET /api/profiles/
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021223-4363845792: 200
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021223-4363845792: 200
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021229-4364116704: GET /api/profiles/
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770021229-4364116704: GET /api/profiles/
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021229-4364116704: 200
2025-07-06 09:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770021229-4364116704: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025609-4365288256: GET /api/analytics/dashboard
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025609-4365288256: GET /api/analytics/dashboard
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025610-4365429392: GET /api/profiles
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025610-4365429392: GET /api/profiles
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025611-4365430544: GET /api/campaigns
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025611-4365430544: GET /api/campaigns
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025611-4365087072: GET /api/scraping/sessions
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025611-4365087072: GET /api/scraping/sessions
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025610-4365429392: 307
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025610-4365429392: 307
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025611-4365430544: 307
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025611-4365430544: 307
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025609-4365288256: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025609-4365288256: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025611-4365087072: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025611-4365087072: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025623-4365290272: GET /api/profiles/
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025623-4365290272: GET /api/profiles/
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025624-4365528560: GET /api/campaigns/
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770025624-4365528560: GET /api/campaigns/
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025623-4365290272: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025623-4365290272: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025624-4365528560: 200
2025-07-06 09:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770025624-4365528560: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028873-4363760400: GET /api/profiles
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028873-4363760400: GET /api/profiles
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028874-4365569136: GET /api/scraping/sessions
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028874-4365569136: GET /api/scraping/sessions
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028873-4363760400: 307
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028873-4363760400: 307
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028874-4365569136: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028874-4365569136: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028880-4365596560: GET /api/profiles
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028880-4365596560: GET /api/profiles
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028880-4365596560: 307
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028880-4365596560: 307
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028881-4365598576: GET /api/scraping/sessions
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028881-4365598576: GET /api/scraping/sessions
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028881-4365598576: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028881-4365598576: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028888-4364132704: GET /api/profiles/
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028888-4364132704: GET /api/profiles/
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028888-4364132704: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028888-4364132704: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028892-4365633328: GET /api/profiles/
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751770028892-4365633328: GET /api/profiles/
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028892-4365633328: 200
2025-07-06 09:47:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751770028892-4365633328: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047404-4365670240: GET /api/profiles
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047404-4365670240: GET /api/profiles
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047406-4365443376: GET /api/scraping/sessions
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047406-4365443376: GET /api/scraping/sessions
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047404-4365670240: 307
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047404-4365670240: 307
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047406-4365443376: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047406-4365443376: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047412-4365598672: GET /api/scraping/sessions
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047412-4365598672: GET /api/scraping/sessions
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047412-4365598672: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047412-4365598672: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047420-4360663200: GET /api/profiles
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047420-4360663200: GET /api/profiles
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047420-4360663200: 307
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047420-4360663200: 307
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047425-4365360816: GET /api/profiles/
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047425-4365360816: GET /api/profiles/
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047425-4365360816: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047425-4365360816: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047439-4364117088: GET /api/profiles/
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770047439-4364117088: GET /api/profiles/
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047439-4364117088: 200
2025-07-06 09:47:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770047439-4364117088: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064524-4365446208: GET /api/analytics/dashboard
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064524-4365446208: GET /api/analytics/dashboard
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064527-4365595312: GET /api/profiles
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064527-4365595312: GET /api/profiles
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064527-4365428144: GET /api/campaigns
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064527-4365428144: GET /api/campaigns
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064528-4364175296: GET /api/scraping/sessions
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064528-4364175296: GET /api/scraping/sessions
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064527-4365595312: 307
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064527-4365595312: 307
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064527-4365428144: 307
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064527-4365428144: 307
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064524-4365446208: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064524-4365446208: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064528-4364175296: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064528-4364175296: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064547-4362961056: GET /api/campaigns/
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064547-4362961056: GET /api/campaigns/
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064547-4365430400: GET /api/profiles/
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770064547-4365430400: GET /api/profiles/
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064547-4362961056: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064547-4362961056: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064547-4365430400: 200
2025-07-06 09:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770064547-4365430400: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075050-4364135296: GET /api/analytics/dashboard
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075050-4364135296: GET /api/analytics/dashboard
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075051-4365666960: GET /api/profiles
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075051-4365666960: GET /api/profiles
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075052-4365689376: GET /api/campaigns
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075052-4365689376: GET /api/campaigns
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075052-4365691248: GET /api/scraping/sessions
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075052-4365691248: GET /api/scraping/sessions
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075051-4365666960: 307
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075051-4365666960: 307
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075052-4365689376: 307
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075052-4365689376: 307
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075050-4364135296: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075050-4364135296: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075052-4365691248: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075052-4365691248: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075071-4365666336: GET /api/campaigns/
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075071-4365666336: GET /api/campaigns/
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075071-4365691536: GET /api/profiles/
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770075071-4365691536: GET /api/profiles/
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075071-4365666336: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075071-4365666336: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075071-4365691536: 200
2025-07-06 09:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770075071-4365691536: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083695-4364136112: GET /api/analytics/dashboard
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083695-4364136112: GET /api/analytics/dashboard
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083697-4365778368: GET /api/profiles
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083697-4365778368: GET /api/profiles
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083697-4365903184: GET /api/campaigns
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083697-4365903184: GET /api/campaigns
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083698-4365905056: GET /api/scraping/sessions
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083698-4365905056: GET /api/scraping/sessions
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083697-4365778368: 307
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083697-4365778368: 307
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083697-4365903184: 307
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083697-4365903184: 307
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083695-4364136112: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083695-4364136112: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083698-4365905056: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083698-4365905056: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083733-4365889200: GET /api/profiles/
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083733-4365889200: GET /api/profiles/
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083733-4365843664: GET /api/campaigns/
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751770083733-4365843664: GET /api/campaigns/
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083733-4365889200: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083733-4365889200: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083733-4365843664: 200
2025-07-06 09:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751770083733-4365843664: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090648-4365739440: GET /api/analytics/dashboard
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090648-4365739440: GET /api/analytics/dashboard
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090649-4365905488: GET /api/profiles
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090649-4365905488: GET /api/profiles
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090650-4365887136: GET /api/campaigns
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090650-4365887136: GET /api/campaigns
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090650-4365843040: GET /api/scraping/sessions
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090650-4365843040: GET /api/scraping/sessions
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090649-4365905488: 307
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090649-4365905488: 307
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090650-4365887136: 307
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090650-4365887136: 307
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090648-4365739440: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090648-4365739440: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090650-4365843040: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090650-4365843040: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090687-4365886752: GET /api/profiles/
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090687-4365886752: GET /api/profiles/
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090688-4365782704: GET /api/campaigns/
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751770090688-4365782704: GET /api/campaigns/
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090687-4365886752: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090687-4365886752: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090688-4365782704: 200
2025-07-06 09:48:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751770090688-4365782704: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134946-4365666960: GET /api/profiles
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134946-4365666960: GET /api/profiles
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134948-4364232736: GET /api/profiles
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134948-4364232736: GET /api/profiles
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134948-4365842800: GET /api/scraping/sessions
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134948-4365842800: GET /api/scraping/sessions
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134949-4365741744: GET /api/scraping/sessions
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134949-4365741744: GET /api/scraping/sessions
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134946-4365666960: 307
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134946-4365666960: 307
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134948-4364232736: 307
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134948-4364232736: 307
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134948-4365842800: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134948-4365842800: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134949-4365741744: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134949-4365741744: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134978-4365778800: GET /api/profiles/
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134978-4365778800: GET /api/profiles/
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134978-4364231056: GET /api/profiles/
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751770134978-4364231056: GET /api/profiles/
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134978-4365778800: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134978-4365778800: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134978-4364231056: 200
2025-07-06 09:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751770134978-4364231056: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182417-4363846320: GET /api/profiles
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182417-4363846320: GET /api/profiles
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182418-4365444000: GET /api/scraping/sessions
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182418-4365444000: GET /api/scraping/sessions
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182417-4363846320: 307
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182417-4363846320: 307
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182418-4365444000: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182418-4365444000: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182425-4365012608: GET /api/scraping/sessions
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182425-4365012608: GET /api/scraping/sessions
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182425-4365012608: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182425-4365012608: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182437-4365691008: GET /api/profiles
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182437-4365691008: GET /api/profiles
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182437-4365691008: 307
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182437-4365691008: 307
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182441-4365710336: GET /api/profiles/
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182441-4365710336: GET /api/profiles/
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182441-4365710336: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182441-4365710336: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182450-4363847808: GET /api/profiles/
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751770182450-4363847808: GET /api/profiles/
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182450-4363847808: 200
2025-07-06 09:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751770182450-4363847808: 200
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184777-4365904624: GET /api/profiles
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184777-4365904624: GET /api/profiles
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184777-4365904624: 307
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184777-4365904624: 307
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184778-4364132752: GET /api/profiles
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184778-4364132752: GET /api/profiles
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184778-4364132752: 307
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184778-4364132752: 307
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184797-4365009968: GET /api/profiles/
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184797-4365009968: GET /api/profiles/
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184797-4365009968: 200
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184797-4365009968: 200
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184803-4365664656: GET /api/profiles/
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751770184803-4365664656: GET /api/profiles/
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184803-4365664656: 200
2025-07-06 09:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751770184803-4365664656: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201514-4365445632: GET /api/profiles
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201514-4365445632: GET /api/profiles
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201515-4365289264: GET /api/scraping/sessions
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201515-4365289264: GET /api/scraping/sessions
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201515-4360319712: GET /api/campaigns
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201515-4360319712: GET /api/campaigns
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201514-4365445632: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201514-4365445632: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201515-4360319712: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201515-4360319712: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201515-4365289264: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201515-4365289264: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201519-4365923856: GET /api/campaigns
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201519-4365923856: GET /api/campaigns
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201519-4365923856: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201519-4365923856: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201521-4365926016: GET /api/profiles
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201521-4365926016: GET /api/profiles
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201521-4365710240: GET /api/scraping/sessions
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201521-4365710240: GET /api/scraping/sessions
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201521-4365926016: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201521-4365926016: 307
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201521-4365710240: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201521-4365710240: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201530-4364134912: GET /api/profiles/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201530-4364134912: GET /api/profiles/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201530-4364134912: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201530-4364134912: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201534-4364177072: GET /api/campaigns/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201534-4364177072: GET /api/campaigns/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201534-4364177072: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201534-4364177072: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201538-4365971808: GET /api/profiles/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201538-4365971808: GET /api/profiles/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201538-4365971808: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201538-4365971808: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201543-4365778224: GET /api/campaigns/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751770201543-4365778224: GET /api/campaigns/
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201543-4365778224: 200
2025-07-06 09:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751770201543-4365778224: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205298-4360664208: GET /api/analytics/dashboard
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205298-4360664208: GET /api/analytics/dashboard
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205300-4365782464: GET /api/profiles
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205300-4365782464: GET /api/profiles
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205301-4365902080: GET /api/campaigns
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205301-4365902080: GET /api/campaigns
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205301-4365844192: GET /api/scraping/sessions
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205301-4365844192: GET /api/scraping/sessions
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205300-4365782464: 307
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205300-4365782464: 307
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205301-4365902080: 307
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205301-4365902080: 307
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205298-4360664208: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205298-4360664208: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205301-4365844192: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205301-4365844192: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205321-4365902512: GET /api/campaigns/
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205321-4365902512: GET /api/campaigns/
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205321-4365569616: GET /api/profiles/
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751770205321-4365569616: GET /api/profiles/
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205321-4365902512: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205321-4365902512: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205321-4365569616: 200
2025-07-06 09:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751770205321-4365569616: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209668-4365569280: GET /api/profiles
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209668-4365569280: GET /api/profiles
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209669-4362853248: GET /api/scraping/sessions
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209669-4362853248: GET /api/scraping/sessions
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209668-4365569280: 307
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209668-4365569280: 307
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209669-4362853248: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209669-4362853248: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209674-4365777648: GET /api/profiles
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209674-4365777648: GET /api/profiles
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209674-4365777648: 307
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209674-4365777648: 307
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209678-4365973680: GET /api/scraping/sessions
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209678-4365973680: GET /api/scraping/sessions
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209678-4365973680: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209678-4365973680: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209687-4365944432: GET /api/profiles/
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209687-4365944432: GET /api/profiles/
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209687-4365944432: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209687-4365944432: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209695-4365568560: GET /api/profiles/
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770209695-4365568560: GET /api/profiles/
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209695-4365568560: 200
2025-07-06 09:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770209695-4365568560: 200
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215170-4365843280: GET /api/profiles
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215170-4365843280: GET /api/profiles
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215170-4365843280: 307
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215170-4365843280: 307
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215172-4365670240: GET /api/profiles
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215172-4365670240: GET /api/profiles
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215172-4365670240: 307
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215172-4365670240: 307
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215188-4365946592: GET /api/profiles/
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215188-4365946592: GET /api/profiles/
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215188-4365946592: 200
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215188-4365946592: 200
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215196-4365634768: GET /api/profiles/
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751770215196-4365634768: GET /api/profiles/
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215196-4365634768: 200
2025-07-06 09:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751770215196-4365634768: 200
2025-07-06 09:51:16 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 09:51:16 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276277-4365089952: GET /api/profiles
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276277-4365089952: GET /api/profiles
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276277-4365089952: 307
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276277-4365089952: 307
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276279-4365528560: GET /api/profiles
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276279-4365528560: GET /api/profiles
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276279-4365528560: 307
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276279-4365528560: 307
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276296-4365842464: GET /api/profiles/
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276296-4365842464: GET /api/profiles/
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276296-4365842464: 200
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276296-4365842464: 200
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276303-4366035168: GET /api/profiles/
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770276303-4366035168: GET /api/profiles/
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276303-4366035168: 200
2025-07-06 09:51:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770276303-4366035168: 200
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282057-4365634624: GET /api/profiles
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282057-4365634624: GET /api/profiles
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282057-4365634624: 307
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282057-4365634624: 307
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282061-4365426800: GET /api/profiles
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282061-4365426800: GET /api/profiles
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282061-4365426800: 307
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282061-4365426800: 307
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282067-4360252192: GET /api/profiles/
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282067-4360252192: GET /api/profiles/
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282067-4360252192: 200
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282067-4360252192: 200
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282076-4365672304: GET /api/profiles/
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751770282076-4365672304: GET /api/profiles/
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282076-4365672304: 200
2025-07-06 09:51:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751770282076-4365672304: 200
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283276-4364194048: GET /api/campaigns
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283276-4364194048: GET /api/campaigns
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283276-4364194048: 307
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283276-4364194048: 307
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283279-4365567792: GET /api/campaigns
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283279-4365567792: GET /api/campaigns
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283279-4365567792: 307
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283279-4365567792: 307
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283289-4364197600: GET /api/campaigns/
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283289-4364197600: GET /api/campaigns/
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283289-4364197600: 200
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283289-4364197600: 200
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283297-4362963456: GET /api/campaigns/
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751770283297-4362963456: GET /api/campaigns/
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283297-4362963456: 200
2025-07-06 09:51:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751770283297-4362963456: 200
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751770288851-4365089952: GET /api/analytics/dashboard
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751770288851-4365089952: GET /api/analytics/dashboard
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751770288852-4365778512: GET /api/scraping/sessions
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751770288852-4365778512: GET /api/scraping/sessions
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751770288851-4365089952: 200
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751770288851-4365089952: 200
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751770288852-4365778512: 200
2025-07-06 09:51:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751770288852-4365778512: 200
2025-07-06 09:51:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751770318879-4365886512: GET /api/analytics/dashboard
2025-07-06 09:51:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751770318879-4365886512: GET /api/analytics/dashboard
2025-07-06 09:51:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751770318879-4365886512: 200
2025-07-06 09:51:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751770318879-4365886512: 200
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333355-4365777168: GET /api/profiles
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333355-4365777168: GET /api/profiles
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333355-4365777168: 307
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333355-4365777168: 307
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333357-4365944432: GET /api/profiles
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333357-4365944432: GET /api/profiles
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333357-4365944432: 307
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333357-4365944432: 307
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333374-4365971760: GET /api/profiles/
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333374-4365971760: GET /api/profiles/
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333374-4365971760: 200
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333374-4365971760: 200
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333382-4364194480: GET /api/profiles/
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751770333382-4364194480: GET /api/profiles/
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333382-4364194480: 200
2025-07-06 09:52:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751770333382-4364194480: 200
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336716-4365568272: GET /api/profiles
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336716-4365568272: GET /api/profiles
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336716-4365568272: 307
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336716-4365568272: 307
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336719-4365528368: GET /api/profiles
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336719-4365528368: GET /api/profiles
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336719-4365528368: 307
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336719-4365528368: 307
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336726-4365974160: GET /api/profiles/
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336726-4365974160: GET /api/profiles/
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336726-4365974160: 200
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336726-4365974160: 200
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336736-4365481392: GET /api/profiles/
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770336736-4365481392: GET /api/profiles/
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336736-4365481392: 200
2025-07-06 09:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770336736-4365481392: 200
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338556-4365481872: GET /api/campaigns
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338556-4365481872: GET /api/campaigns
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338556-4365481872: 307
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338556-4365481872: 307
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338557-4365781408: GET /api/campaigns
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338557-4365781408: GET /api/campaigns
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338557-4365781408: 307
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338557-4365781408: 307
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338567-4365904720: GET /api/campaigns/
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338567-4365904720: GET /api/campaigns/
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338567-4365904720: 200
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338567-4365904720: 200
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338571-4365291328: GET /api/campaigns/
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751770338571-4365291328: GET /api/campaigns/
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338571-4365291328: 200
2025-07-06 09:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751770338571-4365291328: 200
2025-07-06 09:52:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751770353381-4365887232: POST /api/profiles/
2025-07-06 09:52:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751770353381-4365887232: POST /api/profiles/
2025-07-06 09:52:33 | INFO | api.profiles:create_profile:93 | Created new profile: Demo Profile (4fd6972b-b736-40c3-80d1-815c898bc788)
2025-07-06 09:52:33 | INFO | api.profiles:create_profile:93 | Created new profile: Demo Profile (4fd6972b-b736-40c3-80d1-815c898bc788)
2025-07-06 09:52:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751770353381-4365887232: 200
2025-07-06 09:52:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751770353381-4365887232: 200
2025-07-06 09:52:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751770360853-4365358656: GET /api/profiles/
2025-07-06 09:52:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751770360853-4365358656: GET /api/profiles/
2025-07-06 09:52:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751770360853-4365358656: 200
2025-07-06 09:52:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751770360853-4365358656: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369030-4365739584: GET /api/analytics/dashboard
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369030-4365739584: GET /api/analytics/dashboard
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365711536: GET /api/profiles
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365711536: GET /api/profiles
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365779392: GET /api/campaigns
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365779392: GET /api/campaigns
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365482256: GET /api/scraping/sessions
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369031-4365482256: GET /api/scraping/sessions
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365711536: 307
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365711536: 307
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365779392: 307
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365779392: 307
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369030-4365739584: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369030-4365739584: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365482256: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369031-4365482256: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369081-4365357264: GET /api/campaigns/
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369081-4365357264: GET /api/campaigns/
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369081-4365291136: GET /api/profiles/
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751770369081-4365291136: GET /api/profiles/
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369081-4365357264: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369081-4365357264: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369081-4365291136: 200
2025-07-06 09:52:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751770369081-4365291136: 200
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375551-4365483312: GET /api/profiles
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375551-4365483312: GET /api/profiles
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375551-4365483312: 307
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375551-4365483312: 307
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375554-4365780160: GET /api/profiles
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375554-4365780160: GET /api/profiles
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375554-4365780160: 307
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375554-4365780160: 307
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375574-4364232352: GET /api/profiles/
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375574-4364232352: GET /api/profiles/
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375574-4364232352: 200
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375574-4364232352: 200
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375582-4365740112: GET /api/profiles/
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751770375582-4365740112: GET /api/profiles/
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375582-4365740112: 200
2025-07-06 09:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751770375582-4365740112: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377413-4365289024: GET /api/profiles
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377413-4365289024: GET /api/profiles
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377414-4365776304: GET /api/scraping/sessions
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377414-4365776304: GET /api/scraping/sessions
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377414-4363962496: GET /api/campaigns
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377414-4363962496: GET /api/campaigns
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377413-4365289024: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377413-4365289024: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377414-4363962496: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377414-4363962496: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377414-4365776304: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377414-4365776304: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377418-4363438064: GET /api/scraping/sessions
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377418-4363438064: GET /api/scraping/sessions
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377418-4363438064: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377418-4363438064: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377421-4365739008: GET /api/profiles
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377421-4365739008: GET /api/profiles
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377421-4360665840: GET /api/campaigns
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377421-4360665840: GET /api/campaigns
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377421-4365739008: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377421-4365739008: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377421-4360665840: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377421-4360665840: 307
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377426-4365120896: GET /api/campaigns/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377426-4365120896: GET /api/campaigns/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377426-4365120896: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377426-4365120896: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377433-4365713264: GET /api/profiles/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377433-4365713264: GET /api/profiles/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377438-4365527744: GET /api/campaigns/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377438-4365527744: GET /api/campaigns/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377433-4365713264: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377433-4365713264: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377438-4365527744: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377438-4365527744: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377443-4365569856: GET /api/profiles/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770377443-4365569856: GET /api/profiles/
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377443-4365569856: 200
2025-07-06 09:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770377443-4365569856: 200
2025-07-06 09:53:18 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:53:18 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 09:53:20 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 09:53:20 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:53:20 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 09:53:20 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:53:20 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 09:53:20 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:53:20 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 09:53:20 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:53:20 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 09:53:20 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:53:20 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406619-4369656944: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406619-4369656944: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406622-4369867632: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406622-4369867632: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406624-4369917984: GET /api/campaigns
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406624-4369917984: GET /api/campaigns
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406619-4369656944: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406619-4369656944: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406624-4369917984: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406624-4369917984: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406622-4369867632: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406622-4369867632: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406693-4373192320: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406693-4373192320: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406695-4373263984: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406695-4373263984: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406695-4373293424: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406695-4373293424: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406693-4373192320: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406693-4373192320: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406695-4373293424: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406695-4373293424: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406695-4373263984: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406695-4373263984: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406719-4369678544: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406719-4369678544: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406721-4373345664: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406721-4373345664: GET /api/profiles
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406721-4373345664: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406721-4373345664: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406719-4369678544: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406719-4369678544: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406724-4373498320: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406724-4373498320: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406725-4373496064: GET /api/campaigns
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406725-4373496064: GET /api/campaigns
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406725-4373533024: GET /api/campaigns/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406725-4373533024: GET /api/campaigns/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406725-4373496064: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406725-4373496064: 307
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406724-4373498320: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406724-4373498320: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406740-4405513424: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406740-4405513424: GET /api/scraping/sessions
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406725-4373533024: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406725-4373533024: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406740-4405513424: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406740-4405513424: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406752-4405511552: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406752-4405511552: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406752-4405511552: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406752-4405511552: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406755-4373460112: GET /api/campaigns/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406755-4373460112: GET /api/campaigns/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406755-4373460112: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406755-4373460112: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406764-4373532976: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406764-4373532976: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406764-4373532976: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406764-4373532976: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406768-4405635344: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751770406768-4405635344: GET /api/profiles/
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406768-4405635344: 200
2025-07-06 09:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751770406768-4405635344: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449308-4405467312: GET /api/analytics/dashboard
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449308-4405467312: GET /api/analytics/dashboard
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449312-4373536240: GET /api/profiles
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449312-4373536240: GET /api/profiles
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449314-4405513808: GET /api/campaigns
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449314-4405513808: GET /api/campaigns
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449381-4369794048: GET /api/scraping/sessions
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449381-4369794048: GET /api/scraping/sessions
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449312-4373536240: 307
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449312-4373536240: 307
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449308-4405467312: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449308-4405467312: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449314-4405513808: 307
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449314-4405513808: 307
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449381-4369794048: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449381-4369794048: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449434-4373396016: GET /api/profiles/
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449434-4373396016: GET /api/profiles/
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449434-4373168032: GET /api/campaigns/
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770449434-4373168032: GET /api/campaigns/
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449434-4373396016: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449434-4373396016: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449434-4373168032: 200
2025-07-06 09:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770449434-4373168032: 200
2025-07-06 09:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751770660372-4369867344: GET /api/profiles/
2025-07-06 09:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751770660372-4369867344: GET /api/profiles/
2025-07-06 09:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751770660372-4369867344: 200
2025-07-06 09:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751770660372-4369867344: 200
2025-07-06 09:58:46 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 09:58:46 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726107-4405733264: GET /api/analytics/dashboard
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726107-4405733264: GET /api/analytics/dashboard
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726107-4405263520: GET /api/profiles
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726107-4405263520: GET /api/profiles
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726108-4369974368: GET /api/campaigns
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726108-4369974368: GET /api/campaigns
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726108-4373535616: GET /api/scraping/sessions
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726108-4373535616: GET /api/scraping/sessions
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726107-4405263520: 307
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726107-4405263520: 307
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726108-4369974368: 307
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726108-4369974368: 307
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726107-4405733264: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726107-4405733264: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726108-4373535616: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726108-4373535616: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726137-4406075008: GET /api/campaigns/
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726137-4406075008: GET /api/campaigns/
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726138-4406104176: GET /api/profiles/
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770726138-4406104176: GET /api/profiles/
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726137-4406075008: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726137-4406075008: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726138-4406104176: 200
2025-07-06 09:58:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770726138-4406104176: 200
2025-07-06 09:59:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770756142-4405733168: GET /api/analytics/dashboard
2025-07-06 09:59:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751770756142-4405733168: GET /api/analytics/dashboard
2025-07-06 09:59:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770756142-4405733168: 200
2025-07-06 09:59:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751770756142-4405733168: 200
2025-07-06 09:59:30 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 09:59:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770786171-4406071504: GET /api/analytics/dashboard
2025-07-06 09:59:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751770786171-4406071504: GET /api/analytics/dashboard
2025-07-06 09:59:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770786171-4406071504: 200
2025-07-06 09:59:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751770786171-4406071504: 200
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809137-4373263840: GET /api/analytics/dashboard
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809137-4373263840: GET /api/analytics/dashboard
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809141-4406073520: GET /api/profiles
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809141-4406073520: GET /api/profiles
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809141-4373235456: GET /api/campaigns
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809141-4373235456: GET /api/campaigns
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809142-4406156016: GET /api/scraping/sessions
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809142-4406156016: GET /api/scraping/sessions
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809141-4406073520: 307
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809141-4406073520: 307
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809141-4373235456: 307
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809141-4373235456: 307
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809137-4373263840: 200
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809137-4373263840: 200
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809142-4406156016: 200
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809142-4406156016: 200
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809161-4369917504: GET /api/profiles/
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809161-4369917504: GET /api/profiles/
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809162-4406155584: GET /api/campaigns/
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751770809162-4406155584: GET /api/campaigns/
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809161-4369917504: 200
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809161-4369917504: 200
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809162-4406155584: 200
2025-07-06 10:00:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751770809162-4406155584: 200
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827057-4369794048: GET /api/analytics/dashboard
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827057-4369794048: GET /api/analytics/dashboard
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827058-4406154528: GET /api/profiles
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827058-4406154528: GET /api/profiles
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827058-4405467648: GET /api/campaigns
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827058-4405467648: GET /api/campaigns
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827058-4373427536: GET /api/scraping/sessions
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827058-4373427536: GET /api/scraping/sessions
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827058-4406154528: 307
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827058-4406154528: 307
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827058-4405467648: 307
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827058-4405467648: 307
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827057-4369794048: 200
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827057-4369794048: 200
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827058-4373427536: 200
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827058-4373427536: 200
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827072-4406107920: GET /api/profiles/
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827072-4406107920: GET /api/profiles/
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827075-4406153424: GET /api/campaigns/
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751770827075-4406153424: GET /api/campaigns/
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827072-4406107920: 200
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827072-4406107920: 200
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827075-4406153424: 200
2025-07-06 10:00:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751770827075-4406153424: 200
2025-07-06 10:00:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770857085-4406073904: GET /api/analytics/dashboard
2025-07-06 10:00:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751770857085-4406073904: GET /api/analytics/dashboard
2025-07-06 10:00:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770857085-4406073904: 200
2025-07-06 10:00:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751770857085-4406073904: 200
2025-07-06 10:01:03 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 10:01:03 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 10:03:58 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 10:03:58 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 10:03:58 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 10:03:58 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 10:03:58 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 10:03:58 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 10:03:58 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 10:03:58 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 10:03:58 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 10:03:58 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 10:03:58 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 10:04:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751771043618-4344791776: GET /api/analytics/dashboard
2025-07-06 10:04:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751771043618-4344791776: GET /api/analytics/dashboard
2025-07-06 10:04:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751771043618-4344791776: 200
2025-07-06 10:04:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751771043618-4344791776: 200
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751771049284-4348748272: GET /api/profiles
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751771049284-4348748272: GET /api/profiles
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751771049285-4348785904: GET /api/profiles
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751771049285-4348785904: GET /api/profiles
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751771049284-4348748272: 307
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751771049284-4348748272: 307
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751771049285-4348785904: 307
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751771049285-4348785904: 307
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751771049292-4348858816: GET /api/profiles/
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751771049292-4348858816: GET /api/profiles/
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751771049296-4348888256: GET /api/profiles/
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751771049296-4348888256: GET /api/profiles/
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751771049292-4348858816: 200
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751771049292-4348858816: 200
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751771049296-4348888256: 200
2025-07-06 10:04:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751771049296-4348888256: 200
2025-07-06 10:04:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751771050937-4348827248: GET /api/scraping/sessions
2025-07-06 10:04:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751771050937-4348827248: GET /api/scraping/sessions
2025-07-06 10:04:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751771050937-4348678000: GET /api/scraping/sessions
2025-07-06 10:04:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751771050937-4348678000: GET /api/scraping/sessions
2025-07-06 10:04:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751771050937-4348827248: 200
2025-07-06 10:04:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751771050937-4348827248: 200
2025-07-06 10:04:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751771050937-4348678000: 200
2025-07-06 10:04:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751771050937-4348678000: 200
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751771051617-4348677088: GET /api/campaigns
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751771051617-4348677088: GET /api/campaigns
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751771051618-4349325120: GET /api/campaigns
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751771051618-4349325120: GET /api/campaigns
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751771051617-4348677088: 307
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751771051617-4348677088: 307
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751771051618-4349325120: 307
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751771051618-4349325120: 307
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751771051624-4349274336: GET /api/campaigns/
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751771051624-4349274336: GET /api/campaigns/
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751771051625-4349351152: GET /api/campaigns/
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751771051625-4349351152: GET /api/campaigns/
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751771051624-4349274336: 200
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751771051624-4349274336: 200
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751771051625-4349351152: 200
2025-07-06 10:04:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751771051625-4349351152: 200
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068269-4344906128: GET /api/analytics/dashboard
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068269-4344906128: GET /api/analytics/dashboard
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068270-4349322816: GET /api/profiles
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068270-4349322816: GET /api/profiles
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068270-4349352736: GET /api/campaigns
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068270-4349352736: GET /api/campaigns
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068270-4349380832: GET /api/scraping/sessions
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068270-4349380832: GET /api/scraping/sessions
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068270-4349322816: 307
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068270-4349322816: 307
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068270-4349352736: 307
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068270-4349352736: 307
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068269-4344906128: 200
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068269-4344906128: 200
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068270-4349380832: 200
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068270-4349380832: 200
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068284-4349381504: GET /api/campaigns/
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068284-4349381504: GET /api/campaigns/
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068285-4349351344: GET /api/profiles/
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751771068285-4349351344: GET /api/profiles/
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068284-4349381504: 200
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068284-4349381504: 200
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068285-4349351344: 200
2025-07-06 10:04:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751771068285-4349351344: 200
2025-07-06 10:04:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751771078158-4349350096: GET /api/profiles/
2025-07-06 10:04:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751771078158-4349350096: GET /api/profiles/
2025-07-06 10:04:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751771078158-4349350096: 200
2025-07-06 10:04:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751771078158-4349350096: 200
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092252-4349476000: GET /api/analytics/dashboard
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092252-4349476000: GET /api/analytics/dashboard
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092253-4349434320: GET /api/profiles
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092253-4349434320: GET /api/profiles
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092253-4349568528: GET /api/campaigns
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092253-4349568528: GET /api/campaigns
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092253-4349585440: GET /api/scraping/sessions
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092253-4349585440: GET /api/scraping/sessions
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092253-4349434320: 307
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092253-4349434320: 307
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092253-4349568528: 307
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092253-4349568528: 307
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092252-4349476000: 200
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092252-4349476000: 200
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092253-4349585440: 200
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092253-4349585440: 200
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092267-4348442992: GET /api/campaigns/
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092267-4348442992: GET /api/campaigns/
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092268-4344740928: GET /api/profiles/
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751771092268-4344740928: GET /api/profiles/
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092267-4348442992: 200
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092267-4348442992: 200
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092268-4344740928: 200
2025-07-06 10:04:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751771092268-4344740928: 200
2025-07-06 10:05:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751771122271-4349586736: GET /api/analytics/dashboard
2025-07-06 10:05:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751771122271-4349586736: GET /api/analytics/dashboard
2025-07-06 10:05:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751771122271-4349586736: 200
2025-07-06 10:05:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751771122271-4349586736: 200
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129102-4349472672: GET /api/analytics/dashboard
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129102-4349472672: GET /api/analytics/dashboard
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129102-4349570544: GET /api/profiles
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129102-4349570544: GET /api/profiles
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129103-4348786048: GET /api/campaigns
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129103-4348786048: GET /api/campaigns
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129104-4348440928: GET /api/scraping/sessions
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129104-4348440928: GET /api/scraping/sessions
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129102-4349570544: 307
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129102-4349570544: 307
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129103-4348786048: 307
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129103-4348786048: 307
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129102-4349472672: 200
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129102-4349472672: 200
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129104-4348440928: 200
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129104-4348440928: 200
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129125-4349570736: GET /api/profiles/
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129125-4349570736: GET /api/profiles/
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129125-4349585872: GET /api/campaigns/
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751771129125-4349585872: GET /api/campaigns/
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129125-4349570736: 200
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129125-4349570736: 200
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129125-4349585872: 200
2025-07-06 10:05:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751771129125-4349585872: 200
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133936-4349567520: GET /api/analytics/dashboard
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133936-4349567520: GET /api/analytics/dashboard
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133936-4344741744: GET /api/profiles
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133936-4344741744: GET /api/profiles
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133937-4348441888: GET /api/campaigns
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133937-4348441888: GET /api/campaigns
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133937-4349272992: GET /api/scraping/sessions
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133937-4349272992: GET /api/scraping/sessions
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133936-4344741744: 307
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133936-4344741744: 307
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133937-4348441888: 307
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133937-4348441888: 307
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133936-4349567520: 200
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133936-4349567520: 200
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133937-4349272992: 200
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133937-4349272992: 200
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133957-4349472672: GET /api/campaigns/
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133957-4349472672: GET /api/campaigns/
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133957-4348677088: GET /api/profiles/
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751771133957-4348677088: GET /api/profiles/
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133957-4349472672: 200
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133957-4349472672: 200
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133957-4348677088: 200
2025-07-06 10:05:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751771133957-4348677088: 200
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751771142136-4348751344: OPTIONS /api/profiles
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751771142136-4348751344: OPTIONS /api/profiles
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751771142136-4348751344: 200
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751771142136-4348751344: 200
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751771142140-4348676464: POST /api/profiles
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751771142140-4348676464: POST /api/profiles
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751771142140-4348676464: 307
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751771142140-4348676464: 307
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751771142144-4349350000: OPTIONS /api/profiles/
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751771142144-4349350000: OPTIONS /api/profiles/
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751771142144-4349350000: 200
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751771142144-4349350000: 200
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751771142148-4349654784: POST /api/profiles/
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751771142148-4349654784: POST /api/profiles/
2025-07-06 10:05:42 | ERROR | database.connection:get_db:43 | Database session error: [{'type': 'int_parsing', 'loc': ('body', 'proxy_port'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': '', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-07-06 10:05:42 | ERROR | database.connection:get_db:43 | Database session error: [{'type': 'int_parsing', 'loc': ('body', 'proxy_port'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': '', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751771142148-4349654784: 422
2025-07-06 10:05:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751771142148-4349654784: 422
2025-07-06 10:06:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751771176453-4349321568: POST /api/profiles
2025-07-06 10:06:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751771176453-4349321568: POST /api/profiles
2025-07-06 10:06:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751771176453-4349321568: 307
2025-07-06 10:06:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751771176453-4349321568: 307
2025-07-06 10:06:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751771176457-4349558448: POST /api/profiles/
2025-07-06 10:06:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751771176457-4349558448: POST /api/profiles/
2025-07-06 10:06:16 | ERROR | database.connection:get_db:43 | Database session error: [{'type': 'int_parsing', 'loc': ('body', 'proxy_port'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': '', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-07-06 10:06:16 | ERROR | database.connection:get_db:43 | Database session error: [{'type': 'int_parsing', 'loc': ('body', 'proxy_port'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': '', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-07-06 10:06:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751771176457-4349558448: 422
2025-07-06 10:06:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751771176457-4349558448: 422
2025-07-06 10:36:45 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 10:36:45 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 10:36:45 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 10:36:45 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 10:36:45 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 10:36:45 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 10:36:45 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 10:36:45 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 10:36:45 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 10:36:45 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 10:36:45 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751773013250-4352906384: GET /api/profiles
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751773013250-4352906384: GET /api/profiles
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751773013253-4352940352: GET /api/profiles
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751773013253-4352940352: GET /api/profiles
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751773013250-4352906384: 307
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751773013250-4352906384: 307
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751773013253-4352940352: 307
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751773013253-4352940352: 307
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751773013267-4352939008: GET /api/profiles/
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751773013267-4352939008: GET /api/profiles/
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751773013267-4352939008: 200
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751773013267-4352939008: 200
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751773013325-4353962864: GET /api/profiles/
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751773013325-4353962864: GET /api/profiles/
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751773013325-4353962864: 200
2025-07-06 10:36:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751773013325-4353962864: 200
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751773025706-4356268144: GET /api/campaigns
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751773025706-4356268144: GET /api/campaigns
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751773025708-4356278304: GET /api/analytics/dashboard
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751773025708-4356278304: GET /api/analytics/dashboard
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751773025708-4356280224: GET /api/scraping/sessions
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751773025708-4356280224: GET /api/scraping/sessions
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751773025706-4356268144: 307
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751773025706-4356268144: 307
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751773025708-4356278304: 200
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751773025708-4356278304: 200
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751773025708-4356280224: 200
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751773025708-4356280224: 200
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751773025749-4356333968: GET /api/campaigns/
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751773025749-4356333968: GET /api/campaigns/
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751773025749-4356333968: 200
2025-07-06 10:37:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751773025749-4356333968: 200
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751773034917-4356301488: OPTIONS /api/profiles
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751773034917-4356301488: OPTIONS /api/profiles
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751773034917-4356301488: 200
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751773034917-4356301488: 200
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751773034920-4356276720: POST /api/profiles
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751773034920-4356276720: POST /api/profiles
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751773034920-4356276720: 307
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751773034920-4356276720: 307
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751773034926-4357330400: OPTIONS /api/profiles/
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751773034926-4357330400: OPTIONS /api/profiles/
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751773034926-4357330400: 200
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751773034926-4357330400: 200
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751773034928-4356278736: POST /api/profiles/
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751773034928-4356278736: POST /api/profiles/
2025-07-06 10:37:14 | ERROR | database.connection:get_db:43 | Database session error: [{'type': 'int_parsing', 'loc': ('body', 'proxy_port'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': '', 'url': 'https://errors.pydantic.dev/2.5/v/int_parsing'}]
2025-07-06 10:37:14 | ERROR | database.connection:get_db:43 | Database session error: [{'type': 'int_parsing', 'loc': ('body', 'proxy_port'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': '', 'url': 'https://errors.pydantic.dev/2.5/v/int_parsing'}]
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751773034928-4356278736: 422
2025-07-06 10:37:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751773034928-4356278736: 422
2025-07-06 10:37:58 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 10:37:58 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 10:37:59 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 10:37:59 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 10:37:59 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 10:37:59 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 10:37:59 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 10:37:59 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 10:37:59 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 10:37:59 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 10:37:59 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 10:37:59 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 10:38:03 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 10:38:03 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 10:53:44 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 10:53:44 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 10:53:44 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 10:53:44 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 10:53:44 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 10:53:44 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 10:53:44 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 10:53:44 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 10:53:44 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 10:53:44 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 10:53:44 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751774031560-4353397904: GET /api/profiles
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751774031560-4353397904: GET /api/profiles
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751774031564-4353431872: GET /api/profiles
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751774031564-4353431872: GET /api/profiles
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751774031560-4353397904: 307
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751774031560-4353397904: 307
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751774031564-4353431872: 307
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751774031564-4353431872: 307
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751774031635-4353433504: GET /api/profiles/
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751774031635-4353433504: GET /api/profiles/
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751774031637-4353594272: GET /api/profiles/
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751774031637-4353594272: GET /api/profiles/
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751774031635-4353433504: 200
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751774031635-4353433504: 200
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751774031637-4353594272: 200
2025-07-06 10:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751774031637-4353594272: 200
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751774035240-4353594992: GET /api/profiles
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751774035240-4353594992: GET /api/profiles
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751774035240-4356772288: GET /api/profiles
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751774035240-4356772288: GET /api/profiles
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751774035240-4353594992: 307
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751774035240-4353594992: 307
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751774035240-4356772288: 307
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751774035240-4356772288: 307
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751774035243-4353555232: GET /api/profiles/
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751774035243-4353555232: GET /api/profiles/
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751774035244-4356784480: GET /api/profiles/
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751774035244-4356784480: GET /api/profiles/
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751774035243-4353555232: 200
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751774035243-4353555232: 200
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751774035244-4356784480: 200
2025-07-06 10:53:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751774035244-4356784480: 200
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774037718-4356775696: GET /api/analytics/dashboard
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774037718-4356775696: GET /api/analytics/dashboard
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774037719-4353644144: GET /api/campaigns
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774037719-4353644144: GET /api/campaigns
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774037720-4353399728: GET /api/scraping/sessions
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774037720-4353399728: GET /api/scraping/sessions
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774037719-4353644144: 307
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774037719-4353644144: 307
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774037718-4356775696: 200
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774037718-4356775696: 200
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774037720-4353399728: 200
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774037720-4353399728: 200
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774037764-4353400496: GET /api/campaigns/
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774037764-4353400496: GET /api/campaigns/
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774037764-4353400496: 200
2025-07-06 10:53:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774037764-4353400496: 200
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046057-4357822592: OPTIONS /api/profiles
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046057-4357822592: OPTIONS /api/profiles
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046057-4357822592: 200
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046057-4357822592: 200
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046061-4353553264: POST /api/profiles
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046061-4353553264: POST /api/profiles
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046061-4353553264: 307
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046061-4353553264: 307
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046064-4357823024: OPTIONS /api/profiles/
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046064-4357823024: OPTIONS /api/profiles/
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046064-4357823024: 200
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046064-4357823024: 200
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046067-4357575584: POST /api/profiles/
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046067-4357575584: POST /api/profiles/
2025-07-06 10:54:06 | INFO | api.profiles:create_profile:93 | Created new profile: test (e7bcdacb-cea1-478b-865a-92baf3939251)
2025-07-06 10:54:06 | INFO | api.profiles:create_profile:93 | Created new profile: test (e7bcdacb-cea1-478b-865a-92baf3939251)
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046067-4357575584: 200
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046067-4357575584: 200
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046091-4353397472: GET /api/profiles
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046091-4353397472: GET /api/profiles
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046091-4353397472: 307
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046091-4353397472: 307
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046109-4358043872: GET /api/profiles/
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774046109-4358043872: GET /api/profiles/
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046109-4358043872: 200
2025-07-06 10:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774046109-4358043872: 200
2025-07-06 10:54:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751774051663-4356787312: GET /api/profiles
2025-07-06 10:54:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751774051663-4356787312: GET /api/profiles
2025-07-06 10:54:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751774051663-4356787312: 307
2025-07-06 10:54:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751774051663-4356787312: 307
2025-07-06 10:54:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751774051674-4353275360: GET /api/profiles/
2025-07-06 10:54:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751774051674-4353275360: GET /api/profiles/
2025-07-06 10:54:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751774051674-4353275360: 200
2025-07-06 10:54:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751774051674-4353275360: 200
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751774054801-4357512304: GET /api/profiles
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751774054801-4357512304: GET /api/profiles
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751774054802-4358083152: GET /api/profiles
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751774054802-4358083152: GET /api/profiles
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751774054801-4357512304: 307
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751774054801-4357512304: 307
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751774054802-4358083152: 307
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751774054802-4358083152: 307
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751774054806-4358083008: GET /api/profiles/
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751774054806-4358083008: GET /api/profiles/
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751774054806-4358124016: GET /api/profiles/
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751774054806-4358124016: GET /api/profiles/
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751774054806-4358083008: 200
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751774054806-4358083008: 200
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751774054806-4358124016: 200
2025-07-06 10:54:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751774054806-4358124016: 200
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751774056511-4358043920: GET /api/profiles
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751774056511-4358043920: GET /api/profiles
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751774056513-4358125840: GET /api/profiles
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751774056513-4358125840: GET /api/profiles
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751774056511-4358043920: 307
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751774056511-4358043920: 307
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751774056513-4358125840: 307
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751774056513-4358125840: 307
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751774056516-4358114704: GET /api/profiles/
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751774056516-4358114704: GET /api/profiles/
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751774056516-4358144256: GET /api/profiles/
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751774056516-4358144256: GET /api/profiles/
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751774056516-4358114704: 200
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751774056516-4358114704: 200
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751774056516-4358144256: 200
2025-07-06 10:54:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751774056516-4358144256: 200
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751774069325-4358197696: GET /api/profiles
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751774069325-4358197696: GET /api/profiles
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751774069325-4358180976: GET /api/profiles
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751774069325-4358180976: GET /api/profiles
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751774069325-4358197696: 307
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751774069325-4358197696: 307
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751774069325-4358180976: 307
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751774069325-4358180976: 307
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751774069343-4358197888: GET /api/profiles/
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751774069343-4358197888: GET /api/profiles/
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751774069343-4358127472: GET /api/profiles/
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751774069343-4358127472: GET /api/profiles/
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751774069343-4358197888: 200
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751774069343-4358197888: 200
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751774069343-4358127472: 200
2025-07-06 10:54:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751774069343-4358127472: 200
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751774071115-4358113168: GET /api/analytics/dashboard
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751774071115-4358113168: GET /api/analytics/dashboard
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751774071116-4358083392: GET /api/campaigns
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751774071116-4358083392: GET /api/campaigns
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751774071118-4358183952: GET /api/scraping/sessions
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751774071118-4358183952: GET /api/scraping/sessions
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751774071116-4358083392: 307
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751774071116-4358083392: 307
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751774071115-4358113168: 200
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751774071115-4358113168: 200
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751774071118-4358183952: 200
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751774071118-4358183952: 200
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751774071146-4358184144: GET /api/campaigns/
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751774071146-4358184144: GET /api/campaigns/
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751774071146-4358184144: 200
2025-07-06 10:54:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751774071146-4358184144: 200
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751774074563-4358124208: GET /api/profiles
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751774074563-4358124208: GET /api/profiles
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751774074565-4353594464: GET /api/profiles
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751774074565-4353594464: GET /api/profiles
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751774074563-4358124208: 307
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751774074563-4358124208: 307
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751774074565-4353594464: 307
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751774074565-4353594464: 307
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751774074570-4358198224: GET /api/profiles/
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751774074570-4358198224: GET /api/profiles/
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751774074571-4356773392: GET /api/profiles/
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751774074571-4356773392: GET /api/profiles/
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751774074570-4358198224: 200
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751774074570-4358198224: 200
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751774074571-4356773392: 200
2025-07-06 10:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751774074571-4356773392: 200
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751774075426-4356772384: GET /api/profiles
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751774075426-4356772384: GET /api/profiles
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751774075428-4354182256: GET /api/profiles
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751774075428-4354182256: GET /api/profiles
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751774075426-4356772384: 307
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751774075426-4356772384: 307
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751774075428-4354182256: 307
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751774075428-4354182256: 307
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751774075434-4358083200: GET /api/profiles/
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751774075434-4358083200: GET /api/profiles/
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751774075434-4353400400: GET /api/profiles/
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751774075434-4353400400: GET /api/profiles/
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751774075434-4358083200: 200
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751774075434-4358083200: 200
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751774075434-4353400400: 200
2025-07-06 10:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751774075434-4353400400: 200
2025-07-06 10:54:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751774086971-4358084928: GET /api/profiles
2025-07-06 10:54:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751774086971-4358084928: GET /api/profiles
2025-07-06 10:54:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751774086971-4358084928: 307
2025-07-06 10:54:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751774086971-4358084928: 307
2025-07-06 10:54:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751774086981-4358267280: GET /api/profiles/
2025-07-06 10:54:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751774086981-4358267280: GET /api/profiles/
2025-07-06 10:54:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751774086981-4358267280: 200
2025-07-06 10:54:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751774086981-4358267280: 200
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092391-4357511392: OPTIONS /api/profiles
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092391-4357511392: OPTIONS /api/profiles
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092391-4357511392: 200
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092391-4357511392: 200
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092396-4356774160: POST /api/profiles
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092396-4356774160: POST /api/profiles
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092396-4356774160: 307
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092396-4356774160: 307
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092400-4357823840: OPTIONS /api/profiles/
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092400-4357823840: OPTIONS /api/profiles/
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092400-4357823840: 200
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092400-4357823840: 200
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092404-4356784288: POST /api/profiles/
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092404-4356784288: POST /api/profiles/
2025-07-06 10:54:52 | INFO | api.profiles:create_profile:93 | Created new profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 10:54:52 | INFO | api.profiles:create_profile:93 | Created new profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092404-4356784288: 200
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092404-4356784288: 200
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092438-4353595040: GET /api/profiles
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092438-4353595040: GET /api/profiles
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092438-4353595040: 307
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092438-4353595040: 307
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092457-4358268432: GET /api/profiles/
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774092457-4358268432: GET /api/profiles/
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092457-4358268432: 200
2025-07-06 10:54:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774092457-4358268432: 200
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751774145182-4357575680: GET /api/profiles
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751774145182-4357575680: GET /api/profiles
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751774145182-4358299216: GET /api/profiles
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751774145182-4358299216: GET /api/profiles
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751774145182-4357575680: 307
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751774145182-4357575680: 307
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751774145182-4358299216: 307
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751774145182-4358299216: 307
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751774145201-4358285344: GET /api/profiles/
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751774145201-4358285344: GET /api/profiles/
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751774145201-4358287216: GET /api/profiles/
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751774145201-4358287216: GET /api/profiles/
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751774145201-4358285344: 200
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751774145201-4358285344: 200
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751774145201-4358287216: 200
2025-07-06 10:55:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751774145201-4358287216: 200
2025-07-06 11:00:30 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:00:30 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:00:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751774430626-4358233296: GET /api/profiles
2025-07-06 11:00:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751774430626-4358233296: GET /api/profiles
2025-07-06 11:00:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751774430626-4358233296: 307
2025-07-06 11:00:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751774430626-4358233296: 307
2025-07-06 11:00:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751774430645-4358332624: GET /api/profiles/
2025-07-06 11:00:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751774430645-4358332624: GET /api/profiles/
2025-07-06 11:00:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751774430645-4358332624: 200
2025-07-06 11:00:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751774430645-4358332624: 200
2025-07-06 11:01:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751774480423-4358298448: GET /api/profiles
2025-07-06 11:01:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751774480423-4358298448: GET /api/profiles
2025-07-06 11:01:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751774480423-4358298448: 307
2025-07-06 11:01:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751774480423-4358298448: 307
2025-07-06 11:01:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751774480442-4358269824: GET /api/profiles/
2025-07-06 11:01:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751774480442-4358269824: GET /api/profiles/
2025-07-06 11:01:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751774480442-4358269824: 200
2025-07-06 11:01:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751774480442-4358269824: 200
2025-07-06 11:05:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751774704810-4358334400: GET /api/profiles
2025-07-06 11:05:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751774704810-4358334400: GET /api/profiles
2025-07-06 11:05:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751774704810-4358334400: 307
2025-07-06 11:05:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751774704810-4358334400: 307
2025-07-06 11:05:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751774704816-4358230896: GET /api/profiles/
2025-07-06 11:05:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751774704816-4358230896: GET /api/profiles/
2025-07-06 11:05:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751774704816-4358230896: 200
2025-07-06 11:05:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751774704816-4358230896: 200
2025-07-06 11:05:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774706207-4358334400: GET /api/profiles
2025-07-06 11:05:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774706207-4358334400: GET /api/profiles
2025-07-06 11:05:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774706207-4358334400: 307
2025-07-06 11:05:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774706207-4358334400: 307
2025-07-06 11:05:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774706211-4358297584: GET /api/profiles/
2025-07-06 11:05:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774706211-4358297584: GET /api/profiles/
2025-07-06 11:05:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774706211-4358297584: 200
2025-07-06 11:05:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774706211-4358297584: 200
2025-07-06 11:05:10 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:05:10 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:05:13 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 11:05:14 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:05:14 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:05:14 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:05:14 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:05:14 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:05:14 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:05:14 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:05:14 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:05:14 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:05:14 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:05:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751774720140-4355076160: GET /api/profiles
2025-07-06 11:05:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751774720140-4355076160: GET /api/profiles
2025-07-06 11:05:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751774720140-4355076160: 307
2025-07-06 11:05:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751774720140-4355076160: 307
2025-07-06 11:05:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751774720143-4355198928: GET /api/profiles/
2025-07-06 11:05:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751774720143-4355198928: GET /api/profiles/
2025-07-06 11:05:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751774720143-4355198928: 200
2025-07-06 11:05:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751774720143-4355198928: 200
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751774721900-4356140720: GET /api/analytics/dashboard
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751774721900-4356140720: GET /api/analytics/dashboard
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751774721906-4355078416: GET /api/campaigns
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751774721906-4355078416: GET /api/campaigns
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751774721908-4358443984: GET /api/scraping/sessions
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751774721908-4358443984: GET /api/scraping/sessions
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751774721906-4355078416: 307
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751774721906-4355078416: 307
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751774721900-4356140720: 200
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751774721900-4356140720: 200
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751774721908-4358443984: 200
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751774721908-4358443984: 200
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751774721949-4358445808: GET /api/campaigns/
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751774721949-4358445808: GET /api/campaigns/
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751774721949-4358445808: 200
2025-07-06 11:05:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751774721949-4358445808: 200
2025-07-06 11:05:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751774725053-4356267456: GET /api/profiles
2025-07-06 11:05:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751774725053-4356267456: GET /api/profiles
2025-07-06 11:05:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751774725053-4356267456: 307
2025-07-06 11:05:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751774725053-4356267456: 307
2025-07-06 11:05:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751774725063-4358496848: GET /api/profiles/
2025-07-06 11:05:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751774725063-4358496848: GET /api/profiles/
2025-07-06 11:05:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751774725063-4358496848: 200
2025-07-06 11:05:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751774725063-4358496848: 200
2025-07-06 11:05:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751774754517-4358445760: GET /api/scraping/sessions
2025-07-06 11:05:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751774754517-4358445760: GET /api/scraping/sessions
2025-07-06 11:05:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751774754518-4355233200: GET /api/scraping/sessions
2025-07-06 11:05:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751774754518-4355233200: GET /api/scraping/sessions
2025-07-06 11:05:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751774754517-4358445760: 200
2025-07-06 11:05:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751774754517-4358445760: 200
2025-07-06 11:05:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751774754518-4355233200: 200
2025-07-06 11:05:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751774754518-4355233200: 200
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751774756224-4358446048: GET /api/campaigns
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751774756224-4358446048: GET /api/campaigns
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751774756225-4359668736: GET /api/campaigns
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751774756225-4359668736: GET /api/campaigns
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751774756224-4358446048: 307
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751774756224-4358446048: 307
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751774756225-4359668736: 307
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751774756225-4359668736: 307
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751774756233-4356237680: GET /api/campaigns/
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751774756233-4356237680: GET /api/campaigns/
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751774756233-4356140912: GET /api/campaigns/
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751774756233-4356140912: GET /api/campaigns/
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751774756233-4356237680: 200
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751774756233-4356237680: 200
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751774756233-4356140912: 200
2025-07-06 11:05:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751774756233-4356140912: 200
2025-07-06 11:06:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751774760195-4359499440: GET /api/analytics/dashboard
2025-07-06 11:06:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751774760195-4359499440: GET /api/analytics/dashboard
2025-07-06 11:06:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751774760195-4359499440: 200
2025-07-06 11:06:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751774760195-4359499440: 200
2025-07-06 11:06:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751774764581-4358446816: GET /api/profiles
2025-07-06 11:06:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751774764581-4358446816: GET /api/profiles
2025-07-06 11:06:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751774764581-4358446816: 307
2025-07-06 11:06:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751774764581-4358446816: 307
2025-07-06 11:06:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751774764585-4356267840: GET /api/profiles/
2025-07-06 11:06:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751774764585-4356267840: GET /api/profiles/
2025-07-06 11:06:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751774764585-4356267840: 200
2025-07-06 11:06:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751774764585-4356267840: 200
2025-07-06 11:06:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774766275-4359567440: GET /api/profiles
2025-07-06 11:06:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774766275-4359567440: GET /api/profiles
2025-07-06 11:06:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774766275-4359567440: 307
2025-07-06 11:06:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774766275-4359567440: 307
2025-07-06 11:06:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774766286-4359498336: GET /api/profiles/
2025-07-06 11:06:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751774766286-4359498336: GET /api/profiles/
2025-07-06 11:06:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774766286-4359498336: 200
2025-07-06 11:06:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751774766286-4359498336: 200
2025-07-06 11:07:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751774858068-4359569072: GET /api/profiles
2025-07-06 11:07:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751774858068-4359569072: GET /api/profiles
2025-07-06 11:07:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751774858068-4359569072: 307
2025-07-06 11:07:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751774858068-4359569072: 307
2025-07-06 11:07:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751774858071-4355195328: GET /api/profiles/
2025-07-06 11:07:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751774858071-4355195328: GET /api/profiles/
2025-07-06 11:07:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751774858071-4355195328: 200
2025-07-06 11:07:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751774858071-4355195328: 200
2025-07-06 11:07:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774872949-4359304960: GET /api/profiles
2025-07-06 11:07:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774872949-4359304960: GET /api/profiles
2025-07-06 11:07:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774872949-4359304960: 307
2025-07-06 11:07:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774872949-4359304960: 307
2025-07-06 11:07:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774872952-4356235616: GET /api/profiles/
2025-07-06 11:07:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774872952-4356235616: GET /api/profiles/
2025-07-06 11:07:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774872952-4356235616: 200
2025-07-06 11:07:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774872952-4356235616: 200
2025-07-06 11:08:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751774881545-4359498480: GET /api/profiles
2025-07-06 11:08:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751774881545-4359498480: GET /api/profiles
2025-07-06 11:08:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751774881545-4359498480: 307
2025-07-06 11:08:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751774881545-4359498480: 307
2025-07-06 11:08:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751774898446-4355057984: GET /api/profiles
2025-07-06 11:08:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751774898446-4355057984: GET /api/profiles
2025-07-06 11:08:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751774898446-4355057984: 307
2025-07-06 11:08:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751774898446-4355057984: 307
2025-07-06 11:08:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751774898449-4359669552: GET /api/profiles/
2025-07-06 11:08:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751774898449-4359669552: GET /api/profiles/
2025-07-06 11:08:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751774898449-4359669552: 200
2025-07-06 11:08:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751774898449-4359669552: 200
2025-07-06 11:08:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751774913930-4359566480: GET /api/profiles
2025-07-06 11:08:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751774913930-4359566480: GET /api/profiles
2025-07-06 11:08:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751774913930-4359566480: 307
2025-07-06 11:08:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751774913930-4359566480: 307
2025-07-06 11:08:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751774913935-4356138608: GET /api/profiles/
2025-07-06 11:08:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751774913935-4356138608: GET /api/profiles/
2025-07-06 11:08:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751774913935-4356138608: 200
2025-07-06 11:08:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751774913935-4356138608: 200
2025-07-06 11:08:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774932153-4359499152: GET /api/profiles
2025-07-06 11:08:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774932153-4359499152: GET /api/profiles
2025-07-06 11:08:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774932153-4359499152: 307
2025-07-06 11:08:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774932153-4359499152: 307
2025-07-06 11:08:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774932155-4355057120: GET /api/profiles/
2025-07-06 11:08:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751774932155-4355057120: GET /api/profiles/
2025-07-06 11:08:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774932155-4355057120: 200
2025-07-06 11:08:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751774932155-4355057120: 200
2025-07-06 11:09:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751774947743-4359498096: GET /api/profiles
2025-07-06 11:09:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751774947743-4359498096: GET /api/profiles
2025-07-06 11:09:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751774947743-4359498096: 307
2025-07-06 11:09:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751774947743-4359498096: 307
2025-07-06 11:09:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751774947751-4356139664: GET /api/profiles/
2025-07-06 11:09:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751774947751-4356139664: GET /api/profiles/
2025-07-06 11:09:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751774947751-4356139664: 200
2025-07-06 11:09:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751774947751-4356139664: 200
2025-07-06 11:09:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751774959421-4356238400: GET /api/profiles
2025-07-06 11:09:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751774959421-4356238400: GET /api/profiles
2025-07-06 11:09:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751774959421-4356238400: 307
2025-07-06 11:09:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751774959421-4356238400: 307
2025-07-06 11:09:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751774959431-4355195280: GET /api/profiles/
2025-07-06 11:09:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751774959431-4355195280: GET /api/profiles/
2025-07-06 11:09:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751774959431-4355195280: 200
2025-07-06 11:09:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751774959431-4355195280: 200
2025-07-06 11:09:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751774975998-4359566480: GET /api/profiles
2025-07-06 11:09:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751774975998-4359566480: GET /api/profiles
2025-07-06 11:09:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751774975998-4359566480: 307
2025-07-06 11:09:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751774975998-4359566480: 307
2025-07-06 11:09:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751774976002-4356140096: GET /api/profiles/
2025-07-06 11:09:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751774976002-4356140096: GET /api/profiles/
2025-07-06 11:09:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751774976002-4356140096: 200
2025-07-06 11:09:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751774976002-4356140096: 200
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751774993839-4359566960: GET /api/profiles
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751774993839-4359566960: GET /api/profiles
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751774993840-4358444848: GET /api/profiles
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751774993840-4358444848: GET /api/profiles
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751774993839-4359566960: 307
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751774993839-4359566960: 307
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751774993840-4358444848: 307
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751774993840-4358444848: 307
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751774993843-4358497184: GET /api/profiles/
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751774993843-4358497184: GET /api/profiles/
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751774993843-4359670752: GET /api/profiles/
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751774993843-4359670752: GET /api/profiles/
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751774993843-4358497184: 200
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751774993843-4358497184: 200
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751774993843-4359670752: 200
2025-07-06 11:09:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751774993843-4359670752: 200
2025-07-06 11:09:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774997863-4358446528: GET /api/profiles
2025-07-06 11:09:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774997863-4358446528: GET /api/profiles
2025-07-06 11:09:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774997863-4358446528: 307
2025-07-06 11:09:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774997863-4358446528: 307
2025-07-06 11:09:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774997867-4355232480: GET /api/profiles/
2025-07-06 11:09:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751774997867-4355232480: GET /api/profiles/
2025-07-06 11:09:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774997867-4355232480: 200
2025-07-06 11:09:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751774997867-4355232480: 200
2025-07-06 11:10:14 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:10:14 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:10:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751775014619-4355195520: GET /api/profiles
2025-07-06 11:10:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751775014619-4355195520: GET /api/profiles
2025-07-06 11:10:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751775014619-4355195520: 307
2025-07-06 11:10:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751775014619-4355195520: 307
2025-07-06 11:10:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751775014643-4358444656: GET /api/profiles/
2025-07-06 11:10:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751775014643-4358444656: GET /api/profiles/
2025-07-06 11:10:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751775014643-4358444656: 200
2025-07-06 11:10:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751775014643-4358444656: 200
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751775015359-4359670752: GET /api/profiles
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751775015359-4359670752: GET /api/profiles
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751775015359-4359670752: 307
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751775015359-4359670752: 307
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751775015368-4356267984: GET /api/profiles/
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751775015368-4356267984: GET /api/profiles/
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751775015368-4356267984: 200
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751775015368-4356267984: 200
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751775015551-4358444800: GET /api/profiles
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751775015551-4358444800: GET /api/profiles
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751775015551-4358444800: 307
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751775015551-4358444800: 307
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751775015560-4359567008: GET /api/profiles/
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751775015560-4359567008: GET /api/profiles/
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751775015560-4359567008: 200
2025-07-06 11:10:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751775015560-4359567008: 200
2025-07-06 11:12:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751775121954-4356139376: GET /api/profiles
2025-07-06 11:12:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751775121954-4356139376: GET /api/profiles
2025-07-06 11:12:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751775121954-4356139376: 307
2025-07-06 11:12:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751775121954-4356139376: 307
2025-07-06 11:12:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751775121963-4359670752: GET /api/profiles/
2025-07-06 11:12:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751775121963-4359670752: GET /api/profiles/
2025-07-06 11:12:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751775121963-4359670752: 200
2025-07-06 11:12:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751775121963-4359670752: 200
2025-07-06 11:12:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751775128814-4356266352: POST /api/profiles/e7bcdacb-cea1-478b-865a-92baf3939251/test-browser
2025-07-06 11:12:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751775128814-4356266352: POST /api/profiles/e7bcdacb-cea1-478b-865a-92baf3939251/test-browser
2025-07-06 11:12:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751775128814-4356266352: 404
2025-07-06 11:12:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751775128814-4356266352: 404
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751775186878-4355807840: GET /api/analytics/dashboard
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751775186878-4355807840: GET /api/analytics/dashboard
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751775186880-4358446672: GET /api/campaigns
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751775186880-4358446672: GET /api/campaigns
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751775186881-4355233680: GET /api/scraping/sessions
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751775186881-4355233680: GET /api/scraping/sessions
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751775186880-4358446672: 307
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751775186880-4358446672: 307
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751775186878-4355807840: 200
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751775186878-4355807840: 200
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751775186881-4355233680: 200
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751775186881-4355233680: 200
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751775186944-4358444800: GET /api/campaigns/
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751775186944-4358444800: GET /api/campaigns/
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751775186944-4358444800: 200
2025-07-06 11:13:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751775186944-4358444800: 200
2025-07-06 11:15:29 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:15:29 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:15:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751775329570-4359693216: GET /api/profiles
2025-07-06 11:15:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751775329570-4359693216: GET /api/profiles
2025-07-06 11:15:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751775329570-4359693216: 307
2025-07-06 11:15:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751775329570-4359693216: 307
2025-07-06 11:15:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751775329580-4359705696: GET /api/profiles/
2025-07-06 11:15:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751775329580-4359705696: GET /api/profiles/
2025-07-06 11:15:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751775329580-4359705696: 200
2025-07-06 11:15:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751775329580-4359705696: 200
2025-07-06 11:15:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751775333838-4355234928: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/test-browser
2025-07-06 11:15:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751775333838-4355234928: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/test-browser
2025-07-06 11:15:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751775333838-4355234928: 404
2025-07-06 11:15:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751775333838-4355234928: 404
2025-07-06 11:15:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751775344453-4359652784: GET /api/scraping/sessions
2025-07-06 11:15:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751775344453-4359652784: GET /api/scraping/sessions
2025-07-06 11:15:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751775344454-4359303808: GET /api/scraping/sessions
2025-07-06 11:15:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751775344454-4359303808: GET /api/scraping/sessions
2025-07-06 11:15:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751775344453-4359652784: 200
2025-07-06 11:15:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751775344453-4359652784: 200
2025-07-06 11:15:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751775344454-4359303808: 200
2025-07-06 11:15:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751775344454-4359303808: 200
2025-07-06 11:15:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751775349471-4359652928: GET /api/scraping/sessions
2025-07-06 11:15:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751775349471-4359652928: GET /api/scraping/sessions
2025-07-06 11:15:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751775349471-4359652928: 200
2025-07-06 11:15:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751775349471-4359652928: 200
2025-07-06 11:15:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751775354494-4356264912: GET /api/scraping/sessions
2025-07-06 11:15:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751775354494-4356264912: GET /api/scraping/sessions
2025-07-06 11:15:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751775354494-4356264912: 200
2025-07-06 11:15:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751775354494-4356264912: 200
2025-07-06 11:15:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751775359518-4355234496: GET /api/scraping/sessions
2025-07-06 11:15:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751775359518-4355234496: GET /api/scraping/sessions
2025-07-06 11:15:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751775359518-4355234496: 200
2025-07-06 11:15:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751775359518-4355234496: 200
2025-07-06 11:16:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751775364541-4358445664: GET /api/scraping/sessions
2025-07-06 11:16:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751775364541-4358445664: GET /api/scraping/sessions
2025-07-06 11:16:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751775364541-4358445664: 200
2025-07-06 11:16:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751775364541-4358445664: 200
2025-07-06 11:16:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751775394575-4359566816: GET /api/scraping/sessions
2025-07-06 11:16:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751775394575-4359566816: GET /api/scraping/sessions
2025-07-06 11:16:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751775394575-4359566816: 200
2025-07-06 11:16:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751775394575-4359566816: 200
2025-07-06 11:16:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751775399594-4356267840: GET /api/scraping/sessions
2025-07-06 11:16:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751775399594-4356267840: GET /api/scraping/sessions
2025-07-06 11:16:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751775399594-4356267840: 200
2025-07-06 11:16:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751775399594-4356267840: 200
2025-07-06 11:16:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751775404610-4356266544: GET /api/scraping/sessions
2025-07-06 11:16:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751775404610-4356266544: GET /api/scraping/sessions
2025-07-06 11:16:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751775404610-4356266544: 200
2025-07-06 11:16:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751775404610-4356266544: 200
2025-07-06 11:16:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751775407557-4355234928: OPTIONS /api/scraping/sessions
2025-07-06 11:16:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751775407557-4355234928: OPTIONS /api/scraping/sessions
2025-07-06 11:16:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751775407557-4355234928: 200
2025-07-06 11:16:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751775407557-4355234928: 200
2025-07-06 11:16:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751775407561-4355056784: POST /api/scraping/sessions
2025-07-06 11:16:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751775407561-4355056784: POST /api/scraping/sessions
2025-07-06 11:16:47 | ERROR | database.connection:get_db:43 | Database session error: [{'type': 'missing', 'loc': ('body', 'target_url'), 'msg': 'Field required', 'input': {'name': 'test 123', 'post_url': 'https://www.facebook.com/share/p/15dEbyxZTH/', 'scraping_types': ['comments'], 'profile_id': 'e7bcdacb-cea1-478b-865a-92baf3939251', 'max_users': 1000, 'include_comments': True, 'include_replies': False, 'delay_between_requests': 2}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'target_type'), 'msg': 'Field required', 'input': {'name': 'test 123', 'post_url': 'https://www.facebook.com/share/p/15dEbyxZTH/', 'scraping_types': ['comments'], 'profile_id': 'e7bcdacb-cea1-478b-865a-92baf3939251', 'max_users': 1000, 'include_comments': True, 'include_replies': False, 'delay_between_requests': 2}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'scraping_method'), 'msg': 'Field required', 'input': {'name': 'test 123', 'post_url': 'https://www.facebook.com/share/p/15dEbyxZTH/', 'scraping_types': ['comments'], 'profile_id': 'e7bcdacb-cea1-478b-865a-92baf3939251', 'max_users': 1000, 'include_comments': True, 'include_replies': False, 'delay_between_requests': 2}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-07-06 11:16:47 | ERROR | database.connection:get_db:43 | Database session error: [{'type': 'missing', 'loc': ('body', 'target_url'), 'msg': 'Field required', 'input': {'name': 'test 123', 'post_url': 'https://www.facebook.com/share/p/15dEbyxZTH/', 'scraping_types': ['comments'], 'profile_id': 'e7bcdacb-cea1-478b-865a-92baf3939251', 'max_users': 1000, 'include_comments': True, 'include_replies': False, 'delay_between_requests': 2}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'target_type'), 'msg': 'Field required', 'input': {'name': 'test 123', 'post_url': 'https://www.facebook.com/share/p/15dEbyxZTH/', 'scraping_types': ['comments'], 'profile_id': 'e7bcdacb-cea1-478b-865a-92baf3939251', 'max_users': 1000, 'include_comments': True, 'include_replies': False, 'delay_between_requests': 2}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'scraping_method'), 'msg': 'Field required', 'input': {'name': 'test 123', 'post_url': 'https://www.facebook.com/share/p/15dEbyxZTH/', 'scraping_types': ['comments'], 'profile_id': 'e7bcdacb-cea1-478b-865a-92baf3939251', 'max_users': 1000, 'include_comments': True, 'include_replies': False, 'delay_between_requests': 2}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-07-06 11:16:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751775407561-4355056784: 422
2025-07-06 11:16:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751775407561-4355056784: 422
2025-07-06 11:16:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751775409628-4359565616: GET /api/scraping/sessions
2025-07-06 11:16:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751775409628-4359565616: GET /api/scraping/sessions
2025-07-06 11:16:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751775409628-4359565616: 200
2025-07-06 11:16:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751775409628-4359565616: 200
2025-07-06 11:16:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751775419030-4355197392: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/test-browser
2025-07-06 11:16:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751775419030-4355197392: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/test-browser
2025-07-06 11:16:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751775419030-4355197392: 404
2025-07-06 11:16:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751775419030-4355197392: 404
2025-07-06 11:22:29 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:22:29 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751775749079-4356237344: GET /api/profiles
2025-07-06 11:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751775749079-4356237344: GET /api/profiles
2025-07-06 11:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751775749079-4356237344: 307
2025-07-06 11:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751775749079-4356237344: 307
2025-07-06 11:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751775749083-4359653984: GET /api/profiles/
2025-07-06 11:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751775749083-4359653984: GET /api/profiles/
2025-07-06 11:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751775749083-4359653984: 200
2025-07-06 11:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751775749083-4359653984: 200
2025-07-06 11:22:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751775761158-4359174608: GET /api/scraping/sessions
2025-07-06 11:22:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751775761158-4359174608: GET /api/scraping/sessions
2025-07-06 11:22:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751775761159-4358444176: GET /api/scraping/sessions
2025-07-06 11:22:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751775761159-4358444176: GET /api/scraping/sessions
2025-07-06 11:22:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751775761158-4359174608: 200
2025-07-06 11:22:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751775761158-4359174608: 200
2025-07-06 11:22:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751775761159-4358444176: 200
2025-07-06 11:22:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751775761159-4358444176: 200
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751775762636-4356236624: GET /api/campaigns
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751775762636-4356236624: GET /api/campaigns
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751775762637-4359683472: GET /api/campaigns
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751775762637-4359683472: GET /api/campaigns
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751775762636-4356236624: 307
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751775762636-4356236624: 307
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751775762637-4359683472: 307
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751775762637-4359683472: 307
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751775762642-4359669936: GET /api/campaigns/
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751775762642-4359669936: GET /api/campaigns/
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751775762643-4355232528: GET /api/campaigns/
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751775762643-4355232528: GET /api/campaigns/
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751775762642-4359669936: 200
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751775762642-4359669936: 200
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751775762643-4355232528: 200
2025-07-06 11:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751775762643-4355232528: 200
2025-07-06 11:27:41 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:27:41 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061777-4359699712: GET /api/profiles
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061777-4359699712: GET /api/profiles
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061777-4359722128: GET /api/profiles
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061777-4359722128: GET /api/profiles
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061778-4359305584: GET /api/scraping/sessions
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061778-4359305584: GET /api/scraping/sessions
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061779-4359723280: GET /api/scraping/sessions
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061779-4359723280: GET /api/scraping/sessions
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061777-4359699712: 307
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061777-4359699712: 307
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061777-4359722128: 307
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061777-4359722128: 307
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061778-4359305584: 200
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061778-4359305584: 200
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061779-4359723280: 200
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061779-4359723280: 200
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061831-4359684000: GET /api/profiles/
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061831-4359684000: GET /api/profiles/
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061832-4382106240: GET /api/profiles/
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776061832-4382106240: GET /api/profiles/
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061831-4359684000: 200
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061831-4359684000: 200
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061832-4382106240: 200
2025-07-06 11:27:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776061832-4382106240: 200
2025-07-06 11:27:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751776066825-4355234928: GET /api/scraping/sessions
2025-07-06 11:27:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751776066825-4355234928: GET /api/scraping/sessions
2025-07-06 11:27:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751776066825-4355234928: 200
2025-07-06 11:27:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751776066825-4355234928: 200
2025-07-06 11:27:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751776071849-4382106336: GET /api/scraping/sessions
2025-07-06 11:27:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751776071849-4382106336: GET /api/scraping/sessions
2025-07-06 11:27:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751776071849-4382106336: 200
2025-07-06 11:27:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751776071849-4382106336: 200
2025-07-06 11:27:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751776076874-4382107392: GET /api/scraping/sessions
2025-07-06 11:27:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751776076874-4382107392: GET /api/scraping/sessions
2025-07-06 11:27:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751776076874-4382107392: 200
2025-07-06 11:27:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751776076874-4382107392: 200
2025-07-06 11:28:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751776081898-4359723184: GET /api/scraping/sessions
2025-07-06 11:28:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751776081898-4359723184: GET /api/scraping/sessions
2025-07-06 11:28:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751776081898-4359723184: 200
2025-07-06 11:28:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751776081898-4359723184: 200
2025-07-06 11:28:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751776086923-4382052704: GET /api/scraping/sessions
2025-07-06 11:28:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751776086923-4382052704: GET /api/scraping/sessions
2025-07-06 11:28:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751776086923-4382052704: 200
2025-07-06 11:28:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751776086923-4382052704: 200
2025-07-06 11:28:08 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:28:08 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:28:09 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:28:09 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:28:09 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:28:09 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:28:09 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:28:09 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:28:09 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:28:09 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:28:09 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:28:09 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:28:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751776091957-4392546464: GET /api/scraping/sessions
2025-07-06 11:28:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751776091957-4392546464: GET /api/scraping/sessions
2025-07-06 11:28:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751776091957-4392546464: 200
2025-07-06 11:28:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751776091957-4392546464: 200
2025-07-06 11:28:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751776096995-4393650832: GET /api/scraping/sessions
2025-07-06 11:28:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751776096995-4393650832: GET /api/scraping/sessions
2025-07-06 11:28:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751776096995-4393650832: 200
2025-07-06 11:28:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751776096995-4393650832: 200
2025-07-06 11:28:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751776102019-4393650016: GET /api/scraping/sessions
2025-07-06 11:28:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751776102019-4393650016: GET /api/scraping/sessions
2025-07-06 11:28:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751776102019-4393650016: 200
2025-07-06 11:28:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751776102019-4393650016: 200
2025-07-06 11:28:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751776107050-4393705632: GET /api/scraping/sessions
2025-07-06 11:28:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751776107050-4393705632: GET /api/scraping/sessions
2025-07-06 11:28:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751776107050-4393705632: 200
2025-07-06 11:28:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751776107050-4393705632: 200
2025-07-06 11:28:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751776112072-4393650544: GET /api/scraping/sessions
2025-07-06 11:28:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751776112072-4393650544: GET /api/scraping/sessions
2025-07-06 11:28:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751776112072-4393650544: 200
2025-07-06 11:28:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751776112072-4393650544: 200
2025-07-06 11:28:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751776117095-4392547904: GET /api/scraping/sessions
2025-07-06 11:28:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751776117095-4392547904: GET /api/scraping/sessions
2025-07-06 11:28:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751776117095-4392547904: 200
2025-07-06 11:28:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751776117095-4392547904: 200
2025-07-06 11:28:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751776122120-4392546704: GET /api/scraping/sessions
2025-07-06 11:28:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751776122120-4392546704: GET /api/scraping/sessions
2025-07-06 11:28:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751776122120-4392546704: 200
2025-07-06 11:28:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751776122120-4392546704: 200
2025-07-06 11:28:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751776127144-4393707744: GET /api/scraping/sessions
2025-07-06 11:28:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751776127144-4393707744: GET /api/scraping/sessions
2025-07-06 11:28:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751776127144-4393707744: 200
2025-07-06 11:28:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751776127144-4393707744: 200
2025-07-06 11:28:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751776132168-4393649488: GET /api/scraping/sessions
2025-07-06 11:28:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751776132168-4393649488: GET /api/scraping/sessions
2025-07-06 11:28:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751776132168-4393649488: 200
2025-07-06 11:28:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751776132168-4393649488: 200
2025-07-06 11:28:54 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:28:54 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:28:55 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:28:55 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:28:55 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:28:55 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:28:55 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:28:55 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:28:55 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:28:55 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:28:55 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:28:55 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:28:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751776137202-4358025376: GET /api/scraping/sessions
2025-07-06 11:28:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751776137202-4358025376: GET /api/scraping/sessions
2025-07-06 11:28:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751776137202-4358025376: 200
2025-07-06 11:28:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751776137202-4358025376: 200
2025-07-06 11:29:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751776142253-4359129744: GET /api/scraping/sessions
2025-07-06 11:29:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751776142253-4359129744: GET /api/scraping/sessions
2025-07-06 11:29:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751776142253-4359129744: 200
2025-07-06 11:29:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751776142253-4359129744: 200
2025-07-06 11:29:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751776147277-4359128928: GET /api/scraping/sessions
2025-07-06 11:29:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751776147277-4359128928: GET /api/scraping/sessions
2025-07-06 11:29:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751776147277-4359128928: 200
2025-07-06 11:29:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751776147277-4359128928: 200
2025-07-06 11:29:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751776156414-4359184544: GET /api/scraping/sessions
2025-07-06 11:29:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751776156414-4359184544: GET /api/scraping/sessions
2025-07-06 11:29:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751776156414-4359184544: 200
2025-07-06 11:29:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751776156414-4359184544: 200
2025-07-06 11:29:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751776161439-4359129456: GET /api/scraping/sessions
2025-07-06 11:29:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751776161439-4359129456: GET /api/scraping/sessions
2025-07-06 11:29:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751776161439-4359129456: 200
2025-07-06 11:29:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751776161439-4359129456: 200
2025-07-06 11:29:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751776166465-4358028160: GET /api/scraping/sessions
2025-07-06 11:29:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751776166465-4358028160: GET /api/scraping/sessions
2025-07-06 11:29:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751776166465-4358028160: 200
2025-07-06 11:29:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751776166465-4358028160: 200
2025-07-06 11:29:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751776171488-4358025472: GET /api/scraping/sessions
2025-07-06 11:29:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751776171488-4358025472: GET /api/scraping/sessions
2025-07-06 11:29:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751776171488-4358025472: 200
2025-07-06 11:29:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751776171488-4358025472: 200
2025-07-06 11:29:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751776176522-4359185024: GET /api/scraping/sessions
2025-07-06 11:29:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751776176522-4359185024: GET /api/scraping/sessions
2025-07-06 11:29:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751776176522-4359185024: 200
2025-07-06 11:29:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751776176522-4359185024: 200
2025-07-06 11:29:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776181547-4359128784: GET /api/scraping/sessions
2025-07-06 11:29:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776181547-4359128784: GET /api/scraping/sessions
2025-07-06 11:29:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776181547-4359128784: 200
2025-07-06 11:29:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776181547-4359128784: 200
2025-07-06 11:29:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751776186569-4357633696: GET /api/scraping/sessions
2025-07-06 11:29:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751776186569-4357633696: GET /api/scraping/sessions
2025-07-06 11:29:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751776186569-4357633696: 200
2025-07-06 11:29:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751776186569-4357633696: 200
2025-07-06 11:29:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751776191595-4358029168: GET /api/scraping/sessions
2025-07-06 11:29:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751776191595-4358029168: GET /api/scraping/sessions
2025-07-06 11:29:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751776191595-4358029168: 200
2025-07-06 11:29:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751776191595-4358029168: 200
2025-07-06 11:29:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751776196618-4361399024: GET /api/scraping/sessions
2025-07-06 11:29:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751776196618-4361399024: GET /api/scraping/sessions
2025-07-06 11:29:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751776196618-4361399024: 200
2025-07-06 11:29:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751776196618-4361399024: 200
2025-07-06 11:30:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751776201642-4359186416: GET /api/scraping/sessions
2025-07-06 11:30:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751776201642-4359186416: GET /api/scraping/sessions
2025-07-06 11:30:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751776201642-4359186416: 200
2025-07-06 11:30:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751776201642-4359186416: 200
2025-07-06 11:30:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751776206667-4358008208: GET /api/scraping/sessions
2025-07-06 11:30:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751776206667-4358008208: GET /api/scraping/sessions
2025-07-06 11:30:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751776206667-4358008208: 200
2025-07-06 11:30:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751776206667-4358008208: 200
2025-07-06 11:30:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751776211698-4359128592: GET /api/scraping/sessions
2025-07-06 11:30:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751776211698-4359128592: GET /api/scraping/sessions
2025-07-06 11:30:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751776211698-4359128592: 200
2025-07-06 11:30:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751776211698-4359128592: 200
2025-07-06 11:30:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751776216727-4361399360: GET /api/scraping/sessions
2025-07-06 11:30:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751776216727-4361399360: GET /api/scraping/sessions
2025-07-06 11:30:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751776216727-4361399360: 200
2025-07-06 11:30:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751776216727-4361399360: 200
2025-07-06 11:30:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751776221757-4358028016: GET /api/scraping/sessions
2025-07-06 11:30:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751776221757-4358028016: GET /api/scraping/sessions
2025-07-06 11:30:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751776221757-4358028016: 200
2025-07-06 11:30:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751776221757-4358028016: 200
2025-07-06 11:30:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751776226780-4359186656: GET /api/scraping/sessions
2025-07-06 11:30:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751776226780-4359186656: GET /api/scraping/sessions
2025-07-06 11:30:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751776226780-4359186656: 200
2025-07-06 11:30:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751776226780-4359186656: 200
2025-07-06 11:30:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751776231803-4359130176: GET /api/scraping/sessions
2025-07-06 11:30:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751776231803-4359130176: GET /api/scraping/sessions
2025-07-06 11:30:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751776231803-4359130176: 200
2025-07-06 11:30:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751776231803-4359130176: 200
2025-07-06 11:30:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751776236826-4361397536: GET /api/scraping/sessions
2025-07-06 11:30:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751776236826-4361397536: GET /api/scraping/sessions
2025-07-06 11:30:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751776236826-4361397536: 200
2025-07-06 11:30:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751776236826-4361397536: 200
2025-07-06 11:30:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776241848-4358025376: GET /api/scraping/sessions
2025-07-06 11:30:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751776241848-4358025376: GET /api/scraping/sessions
2025-07-06 11:30:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776241848-4358025376: 200
2025-07-06 11:30:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751776241848-4358025376: 200
2025-07-06 11:30:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751776246870-4359187904: GET /api/scraping/sessions
2025-07-06 11:30:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751776246870-4359187904: GET /api/scraping/sessions
2025-07-06 11:30:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751776246870-4359187904: 200
2025-07-06 11:30:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751776246870-4359187904: 200
2025-07-06 11:30:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751776251894-4359130464: GET /api/scraping/sessions
2025-07-06 11:30:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751776251894-4359130464: GET /api/scraping/sessions
2025-07-06 11:30:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751776251894-4359130464: 200
2025-07-06 11:30:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751776251894-4359130464: 200
2025-07-06 11:30:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751776256915-4361399264: GET /api/scraping/sessions
2025-07-06 11:30:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751776256915-4361399264: GET /api/scraping/sessions
2025-07-06 11:30:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751776256915-4361399264: 200
2025-07-06 11:30:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751776256915-4361399264: 200
2025-07-06 11:31:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751776261937-4361399072: GET /api/scraping/sessions
2025-07-06 11:31:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751776261937-4361399072: GET /api/scraping/sessions
2025-07-06 11:31:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751776261937-4361399072: 200
2025-07-06 11:31:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751776261937-4361399072: 200
2025-07-06 11:31:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751776266959-4358029264: GET /api/scraping/sessions
2025-07-06 11:31:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751776266959-4358029264: GET /api/scraping/sessions
2025-07-06 11:31:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751776266959-4358029264: 200
2025-07-06 11:31:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751776266959-4358029264: 200
2025-07-06 11:31:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751776271981-4359186368: GET /api/scraping/sessions
2025-07-06 11:31:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751776271981-4359186368: GET /api/scraping/sessions
2025-07-06 11:31:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751776271981-4359186368: 200
2025-07-06 11:31:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751776271981-4359186368: 200
2025-07-06 11:31:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751776277001-4358006720: GET /api/scraping/sessions
2025-07-06 11:31:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751776277001-4358006720: GET /api/scraping/sessions
2025-07-06 11:31:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751776277001-4358006720: 200
2025-07-06 11:31:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751776277001-4358006720: 200
2025-07-06 11:31:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751776282019-4361396912: GET /api/scraping/sessions
2025-07-06 11:31:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751776282019-4361396912: GET /api/scraping/sessions
2025-07-06 11:31:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751776282019-4361396912: 200
2025-07-06 11:31:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751776282019-4361396912: 200
2025-07-06 11:31:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751776287041-4358025376: GET /api/scraping/sessions
2025-07-06 11:31:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751776287041-4358025376: GET /api/scraping/sessions
2025-07-06 11:31:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751776287041-4358025376: 200
2025-07-06 11:31:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751776287041-4358025376: 200
2025-07-06 11:31:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751776292070-4359187712: GET /api/scraping/sessions
2025-07-06 11:31:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751776292070-4359187712: GET /api/scraping/sessions
2025-07-06 11:31:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751776292070-4359187712: 200
2025-07-06 11:31:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751776292070-4359187712: 200
2025-07-06 11:31:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751776297096-4359129360: GET /api/scraping/sessions
2025-07-06 11:31:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751776297096-4359129360: GET /api/scraping/sessions
2025-07-06 11:31:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751776297096-4359129360: 200
2025-07-06 11:31:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751776297096-4359129360: 200
2025-07-06 11:31:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751776302118-4358006768: GET /api/scraping/sessions
2025-07-06 11:31:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751776302118-4358006768: GET /api/scraping/sessions
2025-07-06 11:31:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751776302118-4358006768: 200
2025-07-06 11:31:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751776302118-4358006768: 200
2025-07-06 11:31:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751776307139-4361396576: GET /api/scraping/sessions
2025-07-06 11:31:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751776307139-4361396576: GET /api/scraping/sessions
2025-07-06 11:31:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751776307139-4361396576: 200
2025-07-06 11:31:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751776307139-4361396576: 200
2025-07-06 11:31:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751776312163-4361397200: GET /api/scraping/sessions
2025-07-06 11:31:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751776312163-4361397200: GET /api/scraping/sessions
2025-07-06 11:31:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751776312163-4361397200: 200
2025-07-06 11:31:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751776312163-4361397200: 200
2025-07-06 11:31:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751776317187-4358005808: GET /api/scraping/sessions
2025-07-06 11:31:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751776317187-4358005808: GET /api/scraping/sessions
2025-07-06 11:31:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751776317187-4358005808: 200
2025-07-06 11:31:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751776317187-4358005808: 200
2025-07-06 11:32:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751776322212-4359128544: GET /api/scraping/sessions
2025-07-06 11:32:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751776322212-4359128544: GET /api/scraping/sessions
2025-07-06 11:32:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751776322212-4359128544: 200
2025-07-06 11:32:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751776322212-4359128544: 200
2025-07-06 11:32:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751776327232-4357692816: GET /api/scraping/sessions
2025-07-06 11:32:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751776327232-4357692816: GET /api/scraping/sessions
2025-07-06 11:32:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751776327232-4357692816: 200
2025-07-06 11:32:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751776327232-4357692816: 200
2025-07-06 11:32:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751776332253-4357633024: GET /api/scraping/sessions
2025-07-06 11:32:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751776332253-4357633024: GET /api/scraping/sessions
2025-07-06 11:32:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751776332253-4357633024: 200
2025-07-06 11:32:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751776332253-4357633024: 200
2025-07-06 11:32:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751776337271-4359186320: GET /api/scraping/sessions
2025-07-06 11:32:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751776337271-4359186320: GET /api/scraping/sessions
2025-07-06 11:32:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751776337271-4359186320: 200
2025-07-06 11:32:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751776337271-4359186320: 200
2025-07-06 11:32:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751776342288-4361398640: GET /api/scraping/sessions
2025-07-06 11:32:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751776342288-4361398640: GET /api/scraping/sessions
2025-07-06 11:32:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751776342288-4361398640: 200
2025-07-06 11:32:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751776342288-4361398640: 200
2025-07-06 11:32:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751776347308-4358152736: GET /api/scraping/sessions
2025-07-06 11:32:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751776347308-4358152736: GET /api/scraping/sessions
2025-07-06 11:32:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751776347308-4358152736: 200
2025-07-06 11:32:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751776347308-4358152736: 200
2025-07-06 11:32:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751776352330-4358027008: GET /api/scraping/sessions
2025-07-06 11:32:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751776352330-4358027008: GET /api/scraping/sessions
2025-07-06 11:32:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751776352330-4358027008: 200
2025-07-06 11:32:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751776352330-4358027008: 200
2025-07-06 11:32:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751776357352-4359186608: GET /api/scraping/sessions
2025-07-06 11:32:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751776357352-4359186608: GET /api/scraping/sessions
2025-07-06 11:32:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751776357352-4359186608: 200
2025-07-06 11:32:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751776357352-4359186608: 200
2025-07-06 11:32:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751776362368-4361396384: GET /api/scraping/sessions
2025-07-06 11:32:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751776362368-4361396384: GET /api/scraping/sessions
2025-07-06 11:32:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751776362368-4361396384: 200
2025-07-06 11:32:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751776362368-4361396384: 200
2025-07-06 11:32:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751776367387-4359129792: GET /api/scraping/sessions
2025-07-06 11:32:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751776367387-4359129792: GET /api/scraping/sessions
2025-07-06 11:32:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751776367387-4359129792: 200
2025-07-06 11:32:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751776367387-4359129792: 200
2025-07-06 11:32:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751776372412-4358813488: GET /api/scraping/sessions
2025-07-06 11:32:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751776372412-4358813488: GET /api/scraping/sessions
2025-07-06 11:32:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751776372412-4358813488: 200
2025-07-06 11:32:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751776372412-4358813488: 200
2025-07-06 11:32:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751776377427-4357692816: GET /api/scraping/sessions
2025-07-06 11:32:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751776377427-4357692816: GET /api/scraping/sessions
2025-07-06 11:32:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751776377427-4357692816: 200
2025-07-06 11:32:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751776377427-4357692816: 200
2025-07-06 11:33:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751776382446-4357898448: GET /api/scraping/sessions
2025-07-06 11:33:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751776382446-4357898448: GET /api/scraping/sessions
2025-07-06 11:33:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751776382446-4357898448: 200
2025-07-06 11:33:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751776382446-4357898448: 200
2025-07-06 11:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751776387468-4359129264: GET /api/scraping/sessions
2025-07-06 11:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751776387468-4359129264: GET /api/scraping/sessions
2025-07-06 11:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751776387468-4359129264: 200
2025-07-06 11:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751776387468-4359129264: 200
2025-07-06 11:33:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751776392485-4361399888: GET /api/scraping/sessions
2025-07-06 11:33:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751776392485-4361399888: GET /api/scraping/sessions
2025-07-06 11:33:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751776392485-4361399888: 200
2025-07-06 11:33:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751776392485-4361399888: 200
2025-07-06 11:33:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751776397506-4357636048: GET /api/scraping/sessions
2025-07-06 11:33:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751776397506-4357636048: GET /api/scraping/sessions
2025-07-06 11:33:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751776397506-4357636048: 200
2025-07-06 11:33:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751776397506-4357636048: 200
2025-07-06 11:33:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751776402530-4358028064: GET /api/scraping/sessions
2025-07-06 11:33:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751776402530-4358028064: GET /api/scraping/sessions
2025-07-06 11:33:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751776402530-4358028064: 200
2025-07-06 11:33:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751776402530-4358028064: 200
2025-07-06 11:33:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751776407552-4357692768: GET /api/scraping/sessions
2025-07-06 11:33:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751776407552-4357692768: GET /api/scraping/sessions
2025-07-06 11:33:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751776407552-4357692768: 200
2025-07-06 11:33:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751776407552-4357692768: 200
2025-07-06 11:33:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751776412575-4358006096: GET /api/scraping/sessions
2025-07-06 11:33:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751776412575-4358006096: GET /api/scraping/sessions
2025-07-06 11:33:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751776412575-4358006096: 200
2025-07-06 11:33:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751776412575-4358006096: 200
2025-07-06 11:33:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751776417598-4361397152: GET /api/scraping/sessions
2025-07-06 11:33:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751776417598-4361397152: GET /api/scraping/sessions
2025-07-06 11:33:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751776417598-4361397152: 200
2025-07-06 11:33:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751776417598-4361397152: 200
2025-07-06 11:33:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751776422623-4359186992: GET /api/scraping/sessions
2025-07-06 11:33:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751776422623-4359186992: GET /api/scraping/sessions
2025-07-06 11:33:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751776422623-4359186992: 200
2025-07-06 11:33:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751776422623-4359186992: 200
2025-07-06 11:33:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751776427646-4358154128: GET /api/scraping/sessions
2025-07-06 11:33:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751776427646-4358154128: GET /api/scraping/sessions
2025-07-06 11:33:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751776427646-4358154128: 200
2025-07-06 11:33:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751776427646-4358154128: 200
2025-07-06 11:33:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751776432669-4361398016: GET /api/scraping/sessions
2025-07-06 11:33:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751776432669-4361398016: GET /api/scraping/sessions
2025-07-06 11:33:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751776432669-4361398016: 200
2025-07-06 11:33:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751776432669-4361398016: 200
2025-07-06 11:33:57 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:33:57 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 11:33:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751776437690-4359185216: GET /api/scraping/sessions
2025-07-06 11:33:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751776437690-4359185216: GET /api/scraping/sessions
2025-07-06 11:33:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751776437690-4359185216: 200
2025-07-06 11:33:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751776437690-4359185216: 200
2025-07-06 11:34:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751776442708-4361399792: GET /api/scraping/sessions
2025-07-06 11:34:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751776442708-4361399792: GET /api/scraping/sessions
2025-07-06 11:34:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751776442708-4361399792: 200
2025-07-06 11:34:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751776442708-4361399792: 200
2025-07-06 11:34:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751776446440-4358153168: GET /api/profiles
2025-07-06 11:34:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751776446440-4358153168: GET /api/profiles
2025-07-06 11:34:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751776446440-4358153168: 307
2025-07-06 11:34:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751776446440-4358153168: 307
2025-07-06 11:34:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751776446446-4361397872: GET /api/profiles/
2025-07-06 11:34:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751776446446-4361397872: GET /api/profiles/
2025-07-06 11:34:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751776446446-4361397872: 200
2025-07-06 11:34:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751776446446-4361397872: 200
2025-07-06 11:34:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751776448732-4359186992: GET /api/profiles
2025-07-06 11:34:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751776448732-4359186992: GET /api/profiles
2025-07-06 11:34:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751776448732-4359186992: 307
2025-07-06 11:34:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751776448732-4359186992: 307
2025-07-06 11:34:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751776448743-4361605280: GET /api/profiles/
2025-07-06 11:34:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751776448743-4361605280: GET /api/profiles/
2025-07-06 11:34:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751776448743-4361605280: 200
2025-07-06 11:34:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751776448743-4361605280: 200
2025-07-06 11:34:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751776455560-4361632160: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 11:34:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751776455560-4361632160: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 11:34:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751776455560-4361632160: 404
2025-07-06 11:34:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751776455560-4361632160: 404
2025-07-06 11:35:36 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:35:36 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:35:37 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:35:37 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:35:37 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:35:37 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:35:37 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:35:37 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:35:37 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:35:37 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:35:37 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:35:37 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:35:50 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:35:50 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:35:50 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:35:50 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:35:50 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:35:50 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:35:50 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:35:50 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:35:50 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:35:50 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:35:50 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:35:50 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:36:04 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:36:04 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:36:05 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:36:05 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:36:05 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:36:05 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:36:05 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:36:05 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:36:05 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:36:05 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:36:05 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:36:05 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:36:17 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:36:17 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:36:18 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:36:18 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:36:18 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:36:18 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:36:18 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:36:18 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:36:18 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:36:18 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:36:18 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:36:18 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:36:31 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:36:31 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:36:32 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:36:32 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:36:32 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:36:32 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:36:32 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:36:32 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:36:32 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:36:32 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:36:32 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:36:32 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:36:46 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:36:46 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:36:46 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:36:46 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:36:46 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:36:46 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:36:46 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:36:46 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:36:46 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:36:46 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:36:46 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:36:46 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:36:59 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:36:59 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 11:37:00 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:37:00 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 11:37:00 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:37:00 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 11:37:00 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:37:00 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 11:37:00 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:37:00 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 11:37:00 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 11:37:00 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:05:10 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:05:10 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:05:10 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:05:10 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:05:10 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:05:10 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:05:10 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:05:10 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:05:10 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:05:10 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:05:10 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:05:10 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:05:33 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:05:33 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:05:33 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:05:33 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:05:33 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:05:33 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:05:33 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:05:33 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:05:33 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:05:33 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:05:33 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:05:33 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:05:56 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:05:56 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:05:56 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:05:56 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:05:56 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:05:56 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:05:56 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:05:56 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:05:56 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:05:56 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:05:56 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:05:56 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:06:12 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:06:12 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:06:12 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:06:12 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:06:12 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:06:12 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:06:12 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:06:12 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:06:12 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:06:12 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:06:12 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:06:12 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:06:25 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:06:25 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:06:25 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:06:25 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:06:25 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:06:25 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:06:25 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:06:25 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:06:25 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:06:25 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:06:25 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:06:25 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:06:50 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:06:50 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:06:50 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:06:50 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:06:50 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:06:50 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:06:50 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:06:50 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:06:50 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:06:50 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:06:50 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:06:50 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:07:14 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:07:14 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:07:14 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:07:14 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:07:14 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:07:14 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:07:14 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:07:14 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:07:14 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:07:14 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:07:14 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:07:14 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:07:32 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:07:32 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:07:32 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:07:32 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:07:32 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:07:32 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:07:32 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:07:32 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:07:32 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:07:32 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:07:32 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:07:32 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:07:46 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:07:46 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:07:47 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:07:47 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:07:47 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:07:47 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:07:47 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:07:47 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:07:47 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:07:47 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:07:47 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:07:47 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:08:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751778531154-4372922432: GET /api/profiles
2025-07-06 12:08:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751778531154-4372922432: GET /api/profiles
2025-07-06 12:08:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751778531154-4372922432: 307
2025-07-06 12:08:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751778531154-4372922432: 307
2025-07-06 12:08:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751778531161-4373045200: GET /api/profiles/
2025-07-06 12:08:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751778531161-4373045200: GET /api/profiles/
2025-07-06 12:08:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751778531161-4373045200: 200
2025-07-06 12:08:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751778531161-4373045200: 200
2025-07-06 12:08:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751778535698-4382368336: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 12:08:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751778535698-4382368336: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 12:08:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751778535698-4382368336: 404
2025-07-06 12:08:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751778535698-4382368336: 404
2025-07-06 12:09:01 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:09:01 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 12:09:04 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 12:09:04 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:09:04 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 12:09:04 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:09:04 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 12:09:04 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:09:04 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 12:09:04 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:09:04 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 12:09:04 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:09:04 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 12:09:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751778550595-4576079936: GET /api/profiles
2025-07-06 12:09:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751778550595-4576079936: GET /api/profiles
2025-07-06 12:09:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751778550595-4576079936: 307
2025-07-06 12:09:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751778550595-4576079936: 307
2025-07-06 12:09:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751778550602-4576236352: GET /api/profiles/
2025-07-06 12:09:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751778550602-4576236352: GET /api/profiles/
2025-07-06 12:09:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751778550602-4576236352: 200
2025-07-06 12:09:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751778550602-4576236352: 200
2025-07-06 12:09:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751778556063-4585539040: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 12:09:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751778556063-4585539040: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 12:09:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751778556063-4585539040: 404
2025-07-06 12:09:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751778556063-4585539040: 404
2025-07-06 13:53:51 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 13:53:51 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 13:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751784831658-4585641152: GET /api/profiles
2025-07-06 13:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751784831658-4585641152: GET /api/profiles
2025-07-06 13:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751784831658-4585641152: 307
2025-07-06 13:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751784831658-4585641152: 307
2025-07-06 13:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751784831674-4585538176: GET /api/profiles/
2025-07-06 13:53:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751784831674-4585538176: GET /api/profiles/
2025-07-06 13:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751784831674-4585538176: 200
2025-07-06 13:53:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751784831674-4585538176: 200
2025-07-06 13:53:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751784834648-4585734544: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 13:53:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751784834648-4585734544: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 13:53:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751784834648-4585734544: 404
2025-07-06 13:53:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751784834648-4585734544: 404
2025-07-06 13:53:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751784838098-4585657344: GET /api/profiles
2025-07-06 13:53:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751784838098-4585657344: GET /api/profiles
2025-07-06 13:53:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751784838098-4585657344: 307
2025-07-06 13:53:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751784838098-4585657344: 307
2025-07-06 13:53:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751784838103-4585764224: GET /api/profiles/
2025-07-06 13:53:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751784838103-4585764224: GET /api/profiles/
2025-07-06 13:53:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751784838103-4585764224: 200
2025-07-06 13:53:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751784838103-4585764224: 200
2025-07-06 13:53:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751784839297-4585656528: GET /api/profiles
2025-07-06 13:53:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751784839297-4585656528: GET /api/profiles
2025-07-06 13:53:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751784839297-4585656528: 307
2025-07-06 13:53:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751784839297-4585656528: 307
2025-07-06 13:53:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751784839307-4585786048: GET /api/profiles/
2025-07-06 13:53:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751784839307-4585786048: GET /api/profiles/
2025-07-06 13:53:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751784839307-4585786048: 200
2025-07-06 13:53:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751784839307-4585786048: 200
2025-07-06 13:54:02 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 13:54:02 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 13:54:05 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 13:54:05 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 13:54:05 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 13:54:05 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 13:54:05 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 13:54:05 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 13:54:05 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 13:54:05 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 13:54:05 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 13:54:05 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 13:54:05 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 13:54:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751784851612-4396396608: GET /api/profiles
2025-07-06 13:54:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751784851612-4396396608: GET /api/profiles
2025-07-06 13:54:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751784851612-4396396608: 307
2025-07-06 13:54:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751784851612-4396396608: 307
2025-07-06 13:54:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751784851636-4396519376: GET /api/profiles/
2025-07-06 13:54:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751784851636-4396519376: GET /api/profiles/
2025-07-06 13:54:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751784851636-4396519376: 200
2025-07-06 13:54:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751784851636-4396519376: 200
2025-07-06 13:54:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751784854788-4397463536: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 13:54:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751784854788-4397463536: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 13:54:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751784854788-4397463536: 404
2025-07-06 13:54:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751784854788-4397463536: 404
2025-07-06 13:54:39 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 13:54:39 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 13:58:41 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 13:58:42 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 13:58:42 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 13:58:42 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 13:58:42 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 13:58:42 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 13:58:42 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 13:58:42 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 13:58:42 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 13:58:42 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 13:58:42 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 13:58:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751785128248-4368949360: GET /api/profiles
2025-07-06 13:58:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751785128248-4368949360: GET /api/profiles
2025-07-06 13:58:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751785128248-4368949360: 307
2025-07-06 13:58:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751785128248-4368949360: 307
2025-07-06 13:58:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751785128256-4369075264: GET /api/profiles/
2025-07-06 13:58:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751785128256-4369075264: GET /api/profiles/
2025-07-06 13:58:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751785128256-4369075264: 200
2025-07-06 13:58:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751785128256-4369075264: 200
2025-07-06 13:58:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751785130093-4372179600: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 13:58:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751785130093-4372179600: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 13:58:50 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 13:58:50 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 13:58:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751785130093-4372179600: 200
2025-07-06 13:58:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751785130093-4372179600: 200
2025-07-06 13:58:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751785139589-4372289952: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 13:58:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751785139589-4372289952: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 13:58:59 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 13:58:59 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 13:58:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751785139589-4372289952: 200
2025-07-06 13:58:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751785139589-4372289952: 200
2025-07-06 14:00:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751785234180-4368927328: GET /api/profiles
2025-07-06 14:00:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751785234180-4368927328: GET /api/profiles
2025-07-06 14:00:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751785234180-4368927328: 307
2025-07-06 14:00:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751785234180-4368927328: 307
2025-07-06 14:00:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751785234183-4369075888: GET /api/profiles/
2025-07-06 14:00:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751785234183-4369075888: GET /api/profiles/
2025-07-06 14:00:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751785234183-4369075888: 200
2025-07-06 14:00:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751785234183-4369075888: 200
2025-07-06 14:00:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751785240579-4372511376: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 14:00:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751785240579-4372511376: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 14:00:40 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 14:00:40 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 14:00:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751785240579-4372511376: 200
2025-07-06 14:00:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751785240579-4372511376: 200
2025-07-06 14:00:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751785246844-4368949936: POST /api/profiles/e7bcdacb-cea1-478b-865a-92baf3939251/facebook-login
2025-07-06 14:00:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751785246844-4368949936: POST /api/profiles/e7bcdacb-cea1-478b-865a-92baf3939251/facebook-login
2025-07-06 14:00:46 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test (e7bcdacb-cea1-478b-865a-92baf3939251)
2025-07-06 14:00:46 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test (e7bcdacb-cea1-478b-865a-92baf3939251)
2025-07-06 14:00:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751785246844-4368949936: 200
2025-07-06 14:00:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751785246844-4368949936: 200
2025-07-06 14:01:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751785280990-4372290960: GET /api/profiles
2025-07-06 14:01:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751785280990-4372290960: GET /api/profiles
2025-07-06 14:01:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751785280990-4372290960: 307
2025-07-06 14:01:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751785280990-4372290960: 307
2025-07-06 14:01:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751785281000-4368301744: GET /api/profiles/
2025-07-06 14:01:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751785281000-4368301744: GET /api/profiles/
2025-07-06 14:01:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751785281000-4368301744: 200
2025-07-06 14:01:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751785281000-4368301744: 200
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751785290964-4372509840: OPTIONS /api/profiles
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751785290964-4372509840: OPTIONS /api/profiles
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751785290964-4372509840: 200
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751785290964-4372509840: 200
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751785290969-4372558320: POST /api/profiles
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751785290969-4372558320: POST /api/profiles
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751785290969-4372558320: 307
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751785290969-4372558320: 307
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751785290974-4368453696: OPTIONS /api/profiles/
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751785290974-4368453696: OPTIONS /api/profiles/
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751785290974-4368453696: 200
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751785290974-4368453696: 200
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751785290978-4372291440: POST /api/profiles/
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751785290978-4372291440: POST /api/profiles/
2025-07-06 14:01:30 | INFO | api.profiles:create_profile:94 | Created new profile: testt3 (e62180b1-e38a-47de-8166-5e9007ccafdd)
2025-07-06 14:01:30 | INFO | api.profiles:create_profile:94 | Created new profile: testt3 (e62180b1-e38a-47de-8166-5e9007ccafdd)
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751785290978-4372291440: 200
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751785290978-4372291440: 200
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751785290993-4368926608: GET /api/profiles
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751785290993-4368926608: GET /api/profiles
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751785290993-4368926608: 307
2025-07-06 14:01:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751785290993-4368926608: 307
2025-07-06 14:01:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751785291015-4369130832: GET /api/profiles/
2025-07-06 14:01:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751785291015-4369130832: GET /api/profiles/
2025-07-06 14:01:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751785291015-4369130832: 200
2025-07-06 14:01:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751785291015-4369130832: 200
2025-07-06 14:01:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751785296012-4372512624: POST /api/profiles/e62180b1-e38a-47de-8166-5e9007ccafdd/facebook-login
2025-07-06 14:01:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751785296012-4372512624: POST /api/profiles/e62180b1-e38a-47de-8166-5e9007ccafdd/facebook-login
2025-07-06 14:01:36 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: testt3 (e62180b1-e38a-47de-8166-5e9007ccafdd)
2025-07-06 14:01:36 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: testt3 (e62180b1-e38a-47de-8166-5e9007ccafdd)
2025-07-06 14:01:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751785296012-4372512624: 200
2025-07-06 14:01:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751785296012-4372512624: 200
2025-07-06 14:10:06 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 14:10:06 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-06 14:10:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751785806204-4372701536: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 14:10:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751785806204-4372701536: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 14:10:06 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 14:10:06 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 14:10:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751785806204-4372701536: 200
2025-07-06 14:10:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751785806204-4372701536: 200
2025-07-06 14:14:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751786070059-4368950560: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 14:14:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751786070059-4368950560: POST /api/profiles/b6d77250-b8c9-4114-8fbd-1d63830a91bb/facebook-login
2025-07-06 14:14:30 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 14:14:30 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: test2 (b6d77250-b8c9-4114-8fbd-1d63830a91bb)
2025-07-06 14:14:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751786070059-4368950560: 200
2025-07-06 14:14:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751786070059-4368950560: 200
2025-07-06 14:17:58 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:17:58 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:17:59 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:17:59 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:17:59 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:17:59 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:17:59 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:17:59 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:17:59 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:17:59 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:17:59 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:17:59 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:18:07 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:18:07 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:18:07 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:18:07 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:18:07 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:18:07 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:18:07 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:18:07 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:18:07 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:18:07 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:18:07 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:18:07 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:18:29 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:18:29 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:18:29 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:18:29 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:18:29 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:18:29 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:18:29 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:18:29 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:18:29 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:18:29 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:18:29 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:18:29 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:19:06 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:19:06 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:19:06 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:19:06 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:19:06 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:19:06 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:19:06 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:19:06 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:19:06 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:19:06 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:19:06 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:19:06 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:19:30 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:19:30 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:19:30 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:19:30 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:19:30 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:19:30 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:19:30 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:19:30 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:19:30 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:19:30 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:19:30 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:19:30 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:20:09 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:20:09 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:20:09 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:20:09 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:20:09 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:20:09 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:20:09 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:20:09 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:20:09 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:20:09 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:20:09 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:20:09 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:20:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751786435446-4357070960: GET /api/profiles
2025-07-06 14:20:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751786435446-4357070960: GET /api/profiles
2025-07-06 14:20:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751786435446-4357070960: 307
2025-07-06 14:20:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751786435446-4357070960: 307
2025-07-06 14:20:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751786435449-4357196864: GET /api/profiles/
2025-07-06 14:20:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751786435449-4357196864: GET /api/profiles/
2025-07-06 14:20:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751786435449-4357196864: 200
2025-07-06 14:20:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751786435449-4357196864: 200
2025-07-06 14:23:14 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:23:14 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:23:14 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:23:14 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:23:14 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:23:14 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:23:14 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:23:14 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:23:14 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:23:14 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:23:14 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:23:14 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:24:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751786660441-4427522448: POST /api/profiles/3d0762db-4480-45c3-abea-f4f6b9cb88b8/facebook-login
2025-07-06 14:24:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751786660441-4427522448: POST /api/profiles/3d0762db-4480-45c3-abea-f4f6b9cb88b8/facebook-login
2025-07-06 14:24:20 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: Test Profile (3d0762db-4480-45c3-abea-f4f6b9cb88b8)
2025-07-06 14:24:20 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: Test Profile (3d0762db-4480-45c3-abea-f4f6b9cb88b8)
2025-07-06 14:24:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751786660441-4427522448: 200
2025-07-06 14:24:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751786660441-4427522448: 200
2025-07-06 14:24:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751786697307-4428542688: GET /api/profiles/3d0762db-4480-45c3-abea-f4f6b9cb88b8/facebook-login-status
2025-07-06 14:24:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751786697307-4428542688: GET /api/profiles/3d0762db-4480-45c3-abea-f4f6b9cb88b8/facebook-login-status
2025-07-06 14:24:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751786697307-4428542688: 404
2025-07-06 14:24:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751786697307-4428542688: 404
2025-07-06 14:26:36 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:26:36 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:26:37 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:26:37 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:26:37 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:26:37 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:26:37 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:26:37 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:26:37 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:26:37 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:26:37 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:26:37 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:26:58 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:26:58 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:26:59 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:26:59 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:26:59 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:26:59 | INFO | database.connection:check_database_connection:74 | Database connection successful
2025-07-06 14:26:59 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:26:59 | INFO | database.connection:init_database:59 | Database tables created successfully
2025-07-06 14:26:59 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:26:59 | INFO | main:startup_event:80 | Database initialized successfully
2025-07-06 14:26:59 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:26:59 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:27:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751786860380-4371566800: POST /api/profiles/3d0762db-4480-45c3-abea-f4f6b9cb88b8/facebook-login
2025-07-06 14:27:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751786860380-4371566800: POST /api/profiles/3d0762db-4480-45c3-abea-f4f6b9cb88b8/facebook-login
2025-07-06 14:27:40 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: Test Profile (3d0762db-4480-45c3-abea-f4f6b9cb88b8)
2025-07-06 14:27:40 | INFO | api.profiles:facebook_login:172 | Facebook login initiated for profile: Test Profile (3d0762db-4480-45c3-abea-f4f6b9cb88b8)
2025-07-06 14:27:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751786860380-4371566800: 200
2025-07-06 14:27:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751786860380-4371566800: 200
2025-07-06 14:32:15 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 14:32:59 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 14:33:00 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:33:00 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:33:00 | ERROR | database.connection:check_database_connection:77 | Database connection failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:00 | ERROR | database.connection:check_database_connection:77 | Database connection failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:00 | ERROR | main:startup_event:82 | Database connection failed
2025-07-06 14:33:00 | ERROR | main:startup_event:82 | Database connection failed
2025-07-06 14:33:00 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:33:00 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186210-5103833248: GET /api/analytics/dashboard
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186210-5103833248: GET /api/analytics/dashboard
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186215-5103915504: GET /api/profiles
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186215-5103915504: GET /api/profiles
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186216-5105999008: GET /api/campaigns
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186216-5105999008: GET /api/campaigns
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186217-5106103440: GET /api/scraping/sessions
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186217-5106103440: GET /api/scraping/sessions
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186215-5103915504: 307
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186215-5103915504: 307
2025-07-06 14:33:06 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:06 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186216-5105999008: 307
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186216-5105999008: 307
2025-07-06 14:33:06 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:06 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:06 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:06 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:06 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:06 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186210-5103833248: 500
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186210-5103833248: 500
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186217-5106103440: 500
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186217-5106103440: 500
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186272-5106877824: GET /api/campaigns/
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186272-5106877824: GET /api/campaigns/
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186273-5106914064: GET /api/profiles/
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751787186273-5106914064: GET /api/profiles/
2025-07-06 14:33:06 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:06 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:06 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:06 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:06 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:06 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:06 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:06 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186272-5106877824: 500
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186272-5106877824: 500
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186273-5106914064: 500
2025-07-06 14:33:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751787186273-5106914064: 500
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187277-5106141168: GET /api/analytics/dashboard
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187277-5106141168: GET /api/analytics/dashboard
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187278-5106763664: GET /api/scraping/sessions
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187278-5106763664: GET /api/scraping/sessions
2025-07-06 14:33:07 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:07 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:07 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:07 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:07 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:07 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:07 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:07 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187277-5106141168: 500
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187277-5106141168: 500
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187278-5106763664: 500
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187278-5106763664: 500
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187289-5106792624: GET /api/campaigns
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187289-5106792624: GET /api/campaigns
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187289-5106792624: 307
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187289-5106792624: 307
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187292-5103885040: GET /api/profiles
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187292-5103885040: GET /api/profiles
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187292-5103885040: 307
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187292-5103885040: 307
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187324-5106952560: GET /api/campaigns/
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187324-5106952560: GET /api/campaigns/
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187324-5106907888: GET /api/profiles/
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751787187324-5106907888: GET /api/profiles/
2025-07-06 14:33:07 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:07 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:07 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:07 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:33:07 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:07 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:07 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:07 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187324-5106952560: 500
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187324-5106952560: 500
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187324-5106907888: 500
2025-07-06 14:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751787187324-5106907888: 500
2025-07-06 14:33:12 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:33:12 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:41:40 | INFO | __main__:<module>:160 | Starting server on 127.0.0.1:8000
2025-07-06 14:41:41 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:41:41 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:41:41 | ERROR | database.connection:check_database_connection:77 | Database connection failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:41 | ERROR | database.connection:check_database_connection:77 | Database connection failed: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:41 | ERROR | main:startup_event:82 | Database connection failed
2025-07-06 14:41:41 | ERROR | main:startup_event:82 | Database connection failed
2025-07-06 14:41:41 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:41:41 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 14:41:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751787705355-4377129024: GET /api/analytics/dashboard
2025-07-06 14:41:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751787705355-4377129024: GET /api/analytics/dashboard
2025-07-06 14:41:45 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:45 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:45 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:45 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751787705355-4377129024: 500
2025-07-06 14:41:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751787705355-4377129024: 500
2025-07-06 14:41:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751787706380-4381064112: GET /api/analytics/dashboard
2025-07-06 14:41:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751787706380-4381064112: GET /api/analytics/dashboard
2025-07-06 14:41:46 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:46 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:46 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:46 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751787706380-4381064112: 500
2025-07-06 14:41:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751787706380-4381064112: 500
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708088-4381142464: GET /api/analytics/dashboard
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708088-4381142464: GET /api/analytics/dashboard
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708092-4381192000: GET /api/scraping/sessions
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708092-4381192000: GET /api/scraping/sessions
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708094-4377129360: GET /api/profiles
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708094-4377129360: GET /api/profiles
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708095-4376899392: GET /api/campaigns
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708095-4376899392: GET /api/campaigns
2025-07-06 14:41:48 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:48 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:48 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:48 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708094-4377129360: 307
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708094-4377129360: 307
2025-07-06 14:41:48 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:48 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708095-4376899392: 307
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708095-4376899392: 307
2025-07-06 14:41:48 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:48 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708088-4381142464: 500
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708088-4381142464: 500
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708092-4381192000: 500
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708092-4381192000: 500
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708108-4381304912: GET /api/profiles/
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708108-4381304912: GET /api/profiles/
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708110-4381466096: GET /api/campaigns/
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751787708110-4381466096: GET /api/campaigns/
2025-07-06 14:41:48 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:48 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:48 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:48 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:48 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:48 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:48 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:48 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708108-4381304912: 500
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708108-4381304912: 500
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708110-4381466096: 500
2025-07-06 14:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751787708110-4381466096: 500
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709113-4381558192: GET /api/analytics/dashboard
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709113-4381558192: GET /api/analytics/dashboard
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709115-4381575296: GET /api/scraping/sessions
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709115-4381575296: GET /api/scraping/sessions
2025-07-06 14:41:49 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:49 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:49 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:49 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:49 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:49 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:49 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:49 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709113-4381558192: 500
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709113-4381558192: 500
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709115-4381575296: 500
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709115-4381575296: 500
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709173-4381576208: GET /api/profiles
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709173-4381576208: GET /api/profiles
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709178-4381515584: GET /api/campaigns
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709178-4381515584: GET /api/campaigns
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709173-4381576208: 307
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709173-4381576208: 307
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709178-4381515584: 307
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709178-4381515584: 307
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709187-4381652928: GET /api/profiles/
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709187-4381652928: GET /api/profiles/
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709188-4381654800: GET /api/campaigns/
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751787709188-4381654800: GET /api/campaigns/
2025-07-06 14:41:49 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:49 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:49 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:49 | ERROR | api.campaigns:get_campaigns:44 | Error fetching campaigns: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:41:49 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:49 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:49 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:49 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709187-4381652928: 500
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709187-4381652928: 500
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709188-4381654800: 500
2025-07-06 14:41:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751787709188-4381654800: 500
2025-07-06 14:42:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751787739132-4381598480: GET /api/analytics/dashboard
2025-07-06 14:42:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751787739132-4381598480: GET /api/analytics/dashboard
2025-07-06 14:42:19 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:42:19 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:42:19 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:42:19 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:42:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751787739132-4381598480: 500
2025-07-06 14:42:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751787739132-4381598480: 500
2025-07-06 14:42:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751787740148-4381598864: GET /api/analytics/dashboard
2025-07-06 14:42:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751787740148-4381598864: GET /api/analytics/dashboard
2025-07-06 14:42:20 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:42:20 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:42:20 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:42:20 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:42:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751787740148-4381598864: 500
2025-07-06 14:42:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751787740148-4381598864: 500
2025-07-06 14:42:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751787770164-4381612736: GET /api/analytics/dashboard
2025-07-06 14:42:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751787770164-4381612736: GET /api/analytics/dashboard
2025-07-06 14:42:50 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:42:50 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:42:50 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:42:50 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:42:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751787770164-4381612736: 500
2025-07-06 14:42:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751787770164-4381612736: 500
2025-07-06 14:42:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751787771183-4381782272: GET /api/analytics/dashboard
2025-07-06 14:42:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751787771183-4381782272: GET /api/analytics/dashboard
2025-07-06 14:42:51 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:42:51 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:42:51 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:42:51 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:42:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751787771183-4381782272: 500
2025-07-06 14:42:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751787771183-4381782272: 500
2025-07-06 14:43:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751787801202-4381702416: GET /api/analytics/dashboard
2025-07-06 14:43:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751787801202-4381702416: GET /api/analytics/dashboard
2025-07-06 14:43:21 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:43:21 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:43:21 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:43:21 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:43:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751787801202-4381702416: 500
2025-07-06 14:43:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751787801202-4381702416: 500
2025-07-06 14:43:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751787802219-4381834016: GET /api/analytics/dashboard
2025-07-06 14:43:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751787802219-4381834016: GET /api/analytics/dashboard
2025-07-06 14:43:22 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:43:22 | ERROR | api.analytics:get_dashboard_stats:68 | Error fetching dashboard stats: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:43:22 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:43:22 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:43:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751787802219-4381834016: 500
2025-07-06 14:43:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751787802219-4381834016: 500
2025-07-06 14:48:57 | INFO | __main__:<module>:167 | Starting server on 127.0.0.1:8000
2025-07-06 14:48:57 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:48:57 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:48:57 | ERROR | main:startup_event:83 | Database initialization failed: name 'logger' is not defined
2025-07-06 14:48:57 | ERROR | main:startup_event:83 | Database initialization failed: name 'logger' is not defined
2025-07-06 14:48:57 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:48:57 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:49:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751788141482-4378222704: GET /api/analytics/dashboard
2025-07-06 14:49:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751788141482-4378222704: GET /api/analytics/dashboard
2025-07-06 14:49:01 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:01 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751788141482-4378222704: 500
2025-07-06 14:49:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751788141482-4378222704: 500
2025-07-06 14:49:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751788142525-4382348816: GET /api/analytics/dashboard
2025-07-06 14:49:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751788142525-4382348816: GET /api/analytics/dashboard
2025-07-06 14:49:02 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:02 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751788142525-4382348816: 500
2025-07-06 14:49:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751788142525-4382348816: 500
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143897-4382454736: GET /api/analytics/dashboard
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143897-4382454736: GET /api/analytics/dashboard
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143899-4378225680: GET /api/profiles
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143899-4378225680: GET /api/profiles
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143901-4382529088: GET /api/campaigns
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143901-4382529088: GET /api/campaigns
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143902-4382546480: GET /api/scraping/sessions
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143902-4382546480: GET /api/scraping/sessions
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143899-4378225680: 307
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143899-4378225680: 307
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143901-4382529088: 307
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143901-4382529088: 307
2025-07-06 14:49:03 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:49:03 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:49:03 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:49:03 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143902-4382546480: 500
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143902-4382546480: 500
2025-07-06 14:49:03 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:03 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143925-4383133808: GET /api/campaigns/
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143925-4383133808: GET /api/campaigns/
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143925-4383133808: 200
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143925-4383133808: 200
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143927-4382489472: GET /api/profiles/
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788143927-4382489472: GET /api/profiles/
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143897-4382454736: 500
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143897-4382454736: 500
2025-07-06 14:49:03 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:49:03 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:49:03 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:49:03 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143927-4382489472: 500
2025-07-06 14:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788143927-4382489472: 500
2025-07-06 14:49:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751788144985-4383237168: GET /api/scraping/sessions
2025-07-06 14:49:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751788144985-4383237168: GET /api/scraping/sessions
2025-07-06 14:49:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751788144987-4382456896: GET /api/analytics/dashboard
2025-07-06 14:49:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751788144987-4382456896: GET /api/analytics/dashboard
2025-07-06 14:49:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751788144987-4382583248: GET /api/profiles
2025-07-06 14:49:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751788144987-4382583248: GET /api/profiles
2025-07-06 14:49:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751788144987-4382583248: 307
2025-07-06 14:49:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751788144987-4382583248: 307
2025-07-06 14:49:04 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:49:04 | ERROR | api.scraping:get_scraping_sessions:47 | Error fetching scraping sessions: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:49:04 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:49:04 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:49:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751788144985-4383237168: 500
2025-07-06 14:49:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751788144985-4383237168: 500
2025-07-06 14:49:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751788145008-4383310896: GET /api/profiles/
2025-07-06 14:49:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751788145008-4383310896: GET /api/profiles/
2025-07-06 14:49:05 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:49:05 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:49:05 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:49:05 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:49:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751788145008-4383310896: 500
2025-07-06 14:49:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751788145008-4383310896: 500
2025-07-06 14:49:05 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:05 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751788144987-4382456896: 500
2025-07-06 14:49:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751788144987-4382456896: 500
2025-07-06 14:49:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751788175043-4382399264: GET /api/analytics/dashboard
2025-07-06 14:49:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751788175043-4382399264: GET /api/analytics/dashboard
2025-07-06 14:49:35 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:35 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751788175043-4382399264: 500
2025-07-06 14:49:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751788175043-4382399264: 500
2025-07-06 14:49:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751788176064-4383408816: GET /api/analytics/dashboard
2025-07-06 14:49:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751788176064-4383408816: GET /api/analytics/dashboard
2025-07-06 14:49:36 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:36 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:49:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751788176064-4383408816: 500
2025-07-06 14:49:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751788176064-4383408816: 500
2025-07-06 14:49:52 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:49:52 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:49:52 | ERROR | main:shutdown_event:122 | Error closing database: name 'logger' is not defined
2025-07-06 14:49:52 | ERROR | main:shutdown_event:122 | Error closing database: name 'logger' is not defined
2025-07-06 14:49:53 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:49:53 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:49:53 | ERROR | app.core.database:init_database:142 | Failed to initialize database: name 'async_engine' is not defined
2025-07-06 14:49:53 | ERROR | app.core.database:init_database:142 | Failed to initialize database: name 'async_engine' is not defined
2025-07-06 14:49:53 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:49:53 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:49:53 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:49:53 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:50:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751788206084-4401651824: GET /api/analytics/dashboard
2025-07-06 14:50:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751788206084-4401651824: GET /api/analytics/dashboard
2025-07-06 14:50:06 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:50:06 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:50:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751788206084-4401651824: 500
2025-07-06 14:50:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751788206084-4401651824: 500
2025-07-06 14:50:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751788207121-4405634528: GET /api/analytics/dashboard
2025-07-06 14:50:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751788207121-4405634528: GET /api/analytics/dashboard
2025-07-06 14:50:07 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:50:07 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:50:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751788207121-4405634528: 500
2025-07-06 14:50:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751788207121-4405634528: 500
2025-07-06 14:50:14 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:50:14 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:50:14 | ERROR | app.core.database:close_database:152 | Error closing database: name 'async_engine' is not defined
2025-07-06 14:50:14 | ERROR | app.core.database:close_database:152 | Error closing database: name 'async_engine' is not defined
2025-07-06 14:50:14 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:50:14 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:51:32 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:51:32 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:51:32 | ERROR | app.core.database:init_database:142 | Failed to initialize database: name 'async_engine' is not defined
2025-07-06 14:51:32 | ERROR | app.core.database:init_database:142 | Failed to initialize database: name 'async_engine' is not defined
2025-07-06 14:51:32 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:51:32 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:51:32 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:51:32 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:51:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751788292970-4565253760: GET /api/analytics/dashboard
2025-07-06 14:51:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751788292970-4565253760: GET /api/analytics/dashboard
2025-07-06 14:51:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751788292970-4566568288: GET /api/analytics/dashboard
2025-07-06 14:51:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751788292970-4566568288: GET /api/analytics/dashboard
2025-07-06 14:51:32 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:51:32 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:51:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751788292970-4565253760: 500
2025-07-06 14:51:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751788292970-4565253760: 500
2025-07-06 14:51:32 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:51:32 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[MessageCampaign(message_campaigns)]'. Original exception was: Mapper 'Mapper[Profile(profiles)]' has no property 'message_campaigns'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-07-06 14:51:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751788292970-4566568288: 500
2025-07-06 14:51:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751788292970-4566568288: 500
2025-07-06 14:52:00 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:52:00 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:52:00 | ERROR | app.core.database:close_database:152 | Error closing database: name 'async_engine' is not defined
2025-07-06 14:52:00 | ERROR | app.core.database:close_database:152 | Error closing database: name 'async_engine' is not defined
2025-07-06 14:52:00 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:52:00 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:52:01 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:52:01 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:52:01 | ERROR | app.core.database:init_database:142 | Failed to initialize database: name 'async_engine' is not defined
2025-07-06 14:52:01 | ERROR | app.core.database:init_database:142 | Failed to initialize database: name 'async_engine' is not defined
2025-07-06 14:52:01 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:52:01 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:52:01 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:52:01 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:52:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788322999-4424093760: GET /api/analytics/dashboard
2025-07-06 14:52:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751788322999-4424093760: GET /api/analytics/dashboard
2025-07-06 14:52:03 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(profiles.id) AS count_1 
FROM profiles]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 14:52:03 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(profiles.id) AS count_1 
FROM profiles]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 14:52:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788322999-4424093760: 500
2025-07-06 14:52:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751788322999-4424093760: 500
2025-07-06 14:52:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751788324024-4424588112: GET /api/analytics/dashboard
2025-07-06 14:52:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751788324024-4424588112: GET /api/analytics/dashboard
2025-07-06 14:52:04 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(profiles.id) AS count_1 
FROM profiles]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 14:52:04 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(profiles.id) AS count_1 
FROM profiles]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 14:52:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751788324024-4424588112: 500
2025-07-06 14:52:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751788324024-4424588112: 500
2025-07-06 14:52:14 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:52:14 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:52:14 | ERROR | app.core.database:close_database:152 | Error closing database: name 'async_engine' is not defined
2025-07-06 14:52:14 | ERROR | app.core.database:close_database:152 | Error closing database: name 'async_engine' is not defined
2025-07-06 14:52:14 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:52:14 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:52:14 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:52:14 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:52:14 | ERROR | app.core.database:init_database:142 | Failed to initialize database: name 'async_engine' is not defined
2025-07-06 14:52:14 | ERROR | app.core.database:init_database:142 | Failed to initialize database: name 'async_engine' is not defined
2025-07-06 14:52:14 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:52:14 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:52:14 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:52:14 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:52:41 | INFO | __main__:<module>:167 | Starting server on 127.0.0.1:8000
2025-07-06 14:52:41 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:52:41 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:52:41 | ERROR | app.core.database:init_database:142 | Failed to initialize database: name 'async_engine' is not defined
2025-07-06 14:52:41 | ERROR | app.core.database:init_database:142 | Failed to initialize database: name 'async_engine' is not defined
2025-07-06 14:52:41 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:52:41 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:52:41 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:52:41 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367575-4438482704: GET /api/analytics/dashboard
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367575-4438482704: GET /api/analytics/dashboard
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367583-4439693728: GET /api/profiles
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367583-4439693728: GET /api/profiles
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367584-4439802928: GET /api/campaigns
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367584-4439802928: GET /api/campaigns
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367584-4439832512: GET /api/scraping/sessions
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367584-4439832512: GET /api/scraping/sessions
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367583-4439693728: 307
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367583-4439693728: 307
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367584-4439802928: 307
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367584-4439802928: 307
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367584-4439832512: 200
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367584-4439832512: 200
2025-07-06 14:52:47 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(profiles.id) AS count_1 
FROM profiles]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 14:52:47 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(profiles.id) AS count_1 
FROM profiles]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367575-4438482704: 500
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367575-4438482704: 500
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367635-4438496160: GET /api/profiles/
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367635-4438496160: GET /api/profiles/
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367636-4439768656: GET /api/campaigns/
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751788367636-4439768656: GET /api/campaigns/
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367636-4439768656: 200
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367636-4439768656: 200
2025-07-06 14:52:47 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:52:47 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:52:47 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:52:47 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367635-4438496160: 500
2025-07-06 14:52:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751788367635-4438496160: 500
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751788368638-4439766016: GET /api/analytics/dashboard
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751788368638-4439766016: GET /api/analytics/dashboard
2025-07-06 14:52:48 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(profiles.id) AS count_1 
FROM profiles]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 14:52:48 | ERROR | api.analytics:get_dashboard_stats:58 | Error fetching dashboard stats: (sqlite3.OperationalError) no such table: profiles
[SQL: SELECT count(profiles.id) AS count_1 
FROM profiles]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751788368638-4439766016: 500
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751788368638-4439766016: 500
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751788368678-4442726160: GET /api/profiles
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751788368678-4442726160: GET /api/profiles
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751788368678-4442726160: 307
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751788368678-4442726160: 307
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751788368686-4442765728: GET /api/profiles/
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751788368686-4442765728: GET /api/profiles/
2025-07-06 14:52:48 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:52:48 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:52:48 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:52:48 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751788368686-4442765728: 500
2025-07-06 14:52:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751788368686-4442765728: 500
2025-07-06 14:53:09 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:53:09 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:53:09 | ERROR | app.core.database:close_database:152 | Error closing database: name 'async_engine' is not defined
2025-07-06 14:53:09 | ERROR | app.core.database:close_database:152 | Error closing database: name 'async_engine' is not defined
2025-07-06 14:53:09 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:53:09 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:53:10 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:53:10 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:53:10 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:53:10 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:53:10 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:53:10 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:53:10 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:53:10 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:53:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751788398657-4421759424: GET /api/analytics/dashboard
2025-07-06 14:53:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751788398657-4421759424: GET /api/analytics/dashboard
2025-07-06 14:53:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751788398657-4421759424: 200
2025-07-06 14:53:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751788398657-4421759424: 200
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400814-4421915456: GET /api/analytics/dashboard
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400814-4421915456: GET /api/analytics/dashboard
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400825-4422281056: GET /api/profiles
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400825-4422281056: GET /api/profiles
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400826-4422345104: GET /api/campaigns
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400826-4422345104: GET /api/campaigns
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400828-4421762400: GET /api/scraping/sessions
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400828-4421762400: GET /api/scraping/sessions
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400825-4422281056: 307
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400825-4422281056: 307
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400826-4422345104: 307
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400826-4422345104: 307
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400828-4421762400: 200
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400828-4421762400: 200
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400841-4422347456: GET /api/campaigns/
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400841-4422347456: GET /api/campaigns/
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400841-4422347456: 200
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400841-4422347456: 200
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400842-4422497136: GET /api/profiles/
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788400842-4422497136: GET /api/profiles/
2025-07-06 14:53:20 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:53:20 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:53:20 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:53:20 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400814-4421915456: 200
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400814-4421915456: 200
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400842-4422497136: 500
2025-07-06 14:53:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788400842-4422497136: 500
2025-07-06 14:53:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751788401881-4422268480: GET /api/profiles
2025-07-06 14:53:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751788401881-4422268480: GET /api/profiles
2025-07-06 14:53:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751788401881-4422268480: 307
2025-07-06 14:53:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751788401881-4422268480: 307
2025-07-06 14:53:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751788401892-4422178032: GET /api/profiles/
2025-07-06 14:53:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751788401892-4422178032: GET /api/profiles/
2025-07-06 14:53:21 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:53:21 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:53:21 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:53:21 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:53:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751788401892-4422178032: 500
2025-07-06 14:53:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751788401892-4422178032: 500
2025-07-06 14:53:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751788430882-4421742400: GET /api/analytics/dashboard
2025-07-06 14:53:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751788430882-4421742400: GET /api/analytics/dashboard
2025-07-06 14:53:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751788430882-4421742400: 200
2025-07-06 14:53:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751788430882-4421742400: 200
2025-07-06 14:53:57 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:53:57 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:53:57 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:53:57 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:53:57 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:53:57 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:53:58 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:53:58 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:53:58 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:53:58 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:53:58 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:53:58 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:53:58 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:53:58 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:54:10 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:54:10 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:54:10 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:54:10 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:54:10 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:54:10 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:54:11 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:54:11 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:54:11 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:54:11 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:54:11 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:54:11 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:54:11 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:54:11 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:54:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788460915-4450036224: GET /api/analytics/dashboard
2025-07-06 14:54:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751788460915-4450036224: GET /api/analytics/dashboard
2025-07-06 14:54:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788460915-4450036224: 200
2025-07-06 14:54:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751788460915-4450036224: 200
2025-07-06 14:54:23 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:54:23 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:54:23 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:54:23 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:54:23 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:54:23 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:54:23 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:54:23 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:54:23 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:54:23 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:54:23 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:54:23 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:54:23 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:54:23 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474297-4395929904: GET /api/analytics/dashboard
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474297-4395929904: GET /api/analytics/dashboard
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474298-4396018512: GET /api/profiles
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474298-4396018512: GET /api/profiles
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474299-4396119520: GET /api/scraping/sessions
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474299-4396119520: GET /api/scraping/sessions
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474300-4396157920: GET /api/campaigns
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474300-4396157920: GET /api/campaigns
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474298-4396018512: 307
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474298-4396018512: 307
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474299-4396119520: 200
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474299-4396119520: 200
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474300-4396157920: 307
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474300-4396157920: 307
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474336-4396155904: GET /api/profiles/
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474336-4396155904: GET /api/profiles/
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474337-4396157632: GET /api/campaigns/
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751788474337-4396157632: GET /api/campaigns/
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474337-4396157632: 200
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474337-4396157632: 200
2025-07-06 14:54:34 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:54:34 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:54:34 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:54:34 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474297-4395929904: 200
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474297-4395929904: 200
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474336-4396155904: 500
2025-07-06 14:54:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751788474336-4396155904: 500
2025-07-06 14:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751788475410-4396081456: GET /api/profiles
2025-07-06 14:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751788475410-4396081456: GET /api/profiles
2025-07-06 14:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751788475410-4396081456: 307
2025-07-06 14:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751788475410-4396081456: 307
2025-07-06 14:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751788475415-4396155232: GET /api/profiles/
2025-07-06 14:54:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751788475415-4396155232: GET /api/profiles/
2025-07-06 14:54:35 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:54:35 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:54:35 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:54:35 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751788475415-4396155232: 500
2025-07-06 14:54:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751788475415-4396155232: 500
2025-07-06 14:54:37 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:54:37 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:54:37 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:54:37 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:54:37 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:54:37 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:54:38 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:54:38 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:54:38 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:54:38 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:54:38 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:54:38 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:54:38 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:54:38 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:54:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751788480617-4740360800: GET /api/profiles
2025-07-06 14:54:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751788480617-4740360800: GET /api/profiles
2025-07-06 14:54:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751788480617-4740360800: 307
2025-07-06 14:54:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751788480617-4740360800: 307
2025-07-06 14:54:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751788480622-4741752384: GET /api/profiles/
2025-07-06 14:54:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751788480622-4741752384: GET /api/profiles/
2025-07-06 14:54:40 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:54:40 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:54:40 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:54:40 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:54:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751788480622-4741752384: 500
2025-07-06 14:54:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751788480622-4741752384: 500
2025-07-06 14:54:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751788481647-4744387024: GET /api/profiles
2025-07-06 14:54:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751788481647-4744387024: GET /api/profiles
2025-07-06 14:54:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751788481647-4744387024: 307
2025-07-06 14:54:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751788481647-4744387024: 307
2025-07-06 14:54:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751788481653-4744342544: GET /api/profiles/
2025-07-06 14:54:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751788481653-4744342544: GET /api/profiles/
2025-07-06 14:54:41 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:54:41 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:54:41 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:54:41 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:54:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751788481653-4744342544: 500
2025-07-06 14:54:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751788481653-4744342544: 500
2025-07-06 14:54:49 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:54:49 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:54:49 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:54:49 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:54:49 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:54:49 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:54:49 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:54:49 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:54:49 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:54:49 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:54:49 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:54:49 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:54:49 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:54:49 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:56:25 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:56:25 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:56:25 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:56:25 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:56:25 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:56:25 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:57:58 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:57:58 | INFO | main:startup_event:71 | Starting Facebook Automation Backend Server...
2025-07-06 14:57:58 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:57:58 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 14:57:58 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:57:58 | INFO | main:startup_event:81 | Database initialized successfully
2025-07-06 14:57:58 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:57:58 | INFO | main:startup_event:85 | Backend server started successfully
2025-07-06 14:57:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751788678898-4403144448: GET /api/profiles
2025-07-06 14:57:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751788678898-4403144448: GET /api/profiles
2025-07-06 14:57:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751788678899-4403178128: GET /api/profiles
2025-07-06 14:57:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751788678899-4403178128: GET /api/profiles
2025-07-06 14:57:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751788678898-4403144448: 307
2025-07-06 14:57:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751788678898-4403144448: 307
2025-07-06 14:57:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751788678899-4403178128: 307
2025-07-06 14:57:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751788678899-4403178128: 307
2025-07-06 14:58:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751788686442-4403223616: GET /api/profiles
2025-07-06 14:58:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751788686442-4403223616: GET /api/profiles
2025-07-06 14:58:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751788686442-4403223616: 307
2025-07-06 14:58:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751788686442-4403223616: 307
2025-07-06 14:58:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751788686453-4402642272: GET /api/profiles/
2025-07-06 14:58:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751788686453-4402642272: GET /api/profiles/
2025-07-06 14:58:06 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:58:06 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:58:06 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:58:06 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:58:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751788686453-4402642272: 500
2025-07-06 14:58:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751788686453-4402642272: 500
2025-07-06 14:58:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751788687478-4403886640: GET /api/profiles
2025-07-06 14:58:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751788687478-4403886640: GET /api/profiles
2025-07-06 14:58:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751788687478-4403886640: 307
2025-07-06 14:58:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751788687478-4403886640: 307
2025-07-06 14:58:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751788687486-4403913776: GET /api/profiles/
2025-07-06 14:58:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751788687486-4403913776: GET /api/profiles/
2025-07-06 14:58:07 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:58:07 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:58:07 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:58:07 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:58:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751788687486-4403913776: 500
2025-07-06 14:58:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751788687486-4403913776: 500
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691467-4403842448: GET /api/analytics/dashboard
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691467-4403842448: GET /api/analytics/dashboard
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691472-4404056848: GET /api/campaigns
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691472-4404056848: GET /api/campaigns
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691473-4404108448: GET /api/scraping/sessions
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691473-4404108448: GET /api/scraping/sessions
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691477-4402687424: GET /api/profiles
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691477-4402687424: GET /api/profiles
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691472-4404056848: 307
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691472-4404056848: 307
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691473-4404108448: 200
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691473-4404108448: 200
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691477-4402687424: 307
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691477-4402687424: 307
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691521-4403839280: GET /api/campaigns/
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691521-4403839280: GET /api/campaigns/
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691522-4404213648: GET /api/profiles/
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788691522-4404213648: GET /api/profiles/
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691521-4403839280: 200
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691521-4403839280: 200
2025-07-06 14:58:11 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:58:11 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:58:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:58:11 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691522-4404213648: 500
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691522-4404213648: 500
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691467-4403842448: 200
2025-07-06 14:58:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788691467-4403842448: 200
2025-07-06 14:58:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751788692539-4403841584: GET /api/profiles
2025-07-06 14:58:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751788692539-4403841584: GET /api/profiles
2025-07-06 14:58:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751788692539-4403841584: 307
2025-07-06 14:58:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751788692539-4403841584: 307
2025-07-06 14:58:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751788692544-4404400432: GET /api/profiles/
2025-07-06 14:58:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751788692544-4404400432: GET /api/profiles/
2025-07-06 14:58:12 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:58:12 | ERROR | api.profiles:get_profiles:41 | Error fetching profiles: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-06 14:58:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:58:12 | ERROR | database.connection:get_db:43 | Database session error: 
2025-07-06 14:58:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751788692544-4404400432: 500
2025-07-06 14:58:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751788692544-4404400432: 500
2025-07-06 14:58:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751788721556-4403841440: GET /api/analytics/dashboard
2025-07-06 14:58:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751788721556-4403841440: GET /api/analytics/dashboard
2025-07-06 14:58:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751788721556-4403841440: 200
2025-07-06 14:58:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751788721556-4403841440: 200
2025-07-06 14:59:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788751581-4403887360: GET /api/analytics/dashboard
2025-07-06 14:59:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751788751581-4403887360: GET /api/analytics/dashboard
2025-07-06 14:59:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788751581-4403887360: 200
2025-07-06 14:59:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751788751581-4403887360: 200
2025-07-06 14:59:26 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:59:26 | INFO | main:shutdown_event:114 | Shutting down Facebook Automation Backend Server...
2025-07-06 14:59:26 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:59:26 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 14:59:26 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 14:59:26 | INFO | main:shutdown_event:120 | Database connections closed
2025-07-06 15:06:42 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:06:42 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:06:42 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:06:42 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:06:42 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:06:42 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:06:42 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:06:42 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:06:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789202298-4400766256: GET /api/analytics/dashboard
2025-07-06 15:06:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789202298-4400766256: GET /api/analytics/dashboard
2025-07-06 15:06:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789202299-4400946240: GET /api/analytics/dashboard
2025-07-06 15:06:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789202299-4400946240: GET /api/analytics/dashboard
2025-07-06 15:06:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789202299-4400946240: 200
2025-07-06 15:06:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789202299-4400946240: 200
2025-07-06 15:06:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789202298-4400766256: 200
2025-07-06 15:06:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789202298-4400766256: 200
2025-07-06 15:07:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751789232315-4400898112: GET /api/analytics/dashboard
2025-07-06 15:07:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751789232315-4400898112: GET /api/analytics/dashboard
2025-07-06 15:07:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751789232315-4400898112: 200
2025-07-06 15:07:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751789232315-4400898112: 200
2025-07-06 15:07:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751789236699-4400899024: GET /api/profiles
2025-07-06 15:07:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751789236699-4400899024: GET /api/profiles
2025-07-06 15:07:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751789236699-4400899024: 404
2025-07-06 15:07:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751789236699-4400899024: 404
2025-07-06 15:07:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789262334-4400764912: GET /api/analytics/dashboard
2025-07-06 15:07:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789262334-4400764912: GET /api/analytics/dashboard
2025-07-06 15:07:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789262334-4400764912: 200
2025-07-06 15:07:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789262334-4400764912: 200
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751789274661-4401264960: GET /api/analytics/dashboard
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751789274661-4401264960: GET /api/analytics/dashboard
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751789274666-4401022816: GET /api/campaigns
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751789274666-4401022816: GET /api/campaigns
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751789274667-4401385088: GET /api/profiles
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751789274667-4401385088: GET /api/profiles
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751789274668-4401422496: GET /api/scraping/sessions
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751789274668-4401422496: GET /api/scraping/sessions
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751789274666-4401022816: 307
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751789274666-4401022816: 307
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751789274667-4401385088: 404
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751789274667-4401385088: 404
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751789274668-4401422496: 200
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751789274668-4401422496: 200
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751789274661-4401264960: 200
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751789274661-4401264960: 200
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751789274746-4401024256: GET /api/campaigns/
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751789274746-4401024256: GET /api/campaigns/
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751789274746-4401024256: 200
2025-07-06 15:07:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751789274746-4401024256: 200
2025-07-06 15:07:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751789275748-4401422784: GET /api/profiles
2025-07-06 15:07:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751789275748-4401422784: GET /api/profiles
2025-07-06 15:07:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751789275748-4401422784: 404
2025-07-06 15:07:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751789275748-4401422784: 404
2025-07-06 15:07:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751789279064-4400791952: GET /api/profiles
2025-07-06 15:07:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751789279064-4400791952: GET /api/profiles
2025-07-06 15:07:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751789279064-4400791952: 404
2025-07-06 15:07:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751789279064-4400791952: 404
2025-07-06 15:08:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751789280075-4401399936: GET /api/profiles
2025-07-06 15:08:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751789280075-4401399936: GET /api/profiles
2025-07-06 15:08:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751789280075-4401399936: 404
2025-07-06 15:08:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751789280075-4401399936: 404
2025-07-06 15:08:34 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:08:34 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:08:34 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:08:34 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:08:34 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:08:34 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:08:34 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:08:34 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:08:34 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:08:34 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:08:34 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:08:34 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:08:34 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:08:34 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:09:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751789359677-4598622960: GET /api/profiles
2025-07-06 15:09:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751789359677-4598622960: GET /api/profiles
2025-07-06 15:09:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751789359677-4598622960: 307
2025-07-06 15:09:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751789359677-4598622960: 307
2025-07-06 15:09:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751789385807-4598838896: GET /api/profiles/
2025-07-06 15:09:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751789385807-4598838896: GET /api/profiles/
2025-07-06 15:09:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751789385807-4598838896: 200
2025-07-06 15:09:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751789385807-4598838896: 200
2025-07-06 15:10:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751789401317-4598271520: POST /api/profiles/
2025-07-06 15:10:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751789401317-4598271520: POST /api/profiles/
2025-07-06 15:10:01 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789401317-4598271520: 'ProfileCreate' object has no attribute 'browser_config'
2025-07-06 15:10:01 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789401317-4598271520: 'ProfileCreate' object has no attribute 'browser_config'
2025-07-06 15:10:55 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:10:55 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:10:55 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:10:55 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:10:55 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:10:55 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:10:55 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:10:55 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:10:55 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:10:55 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:10:55 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:10:55 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:10:55 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:10:55 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:11:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751789519682-4410055792: POST /api/profiles/
2025-07-06 15:11:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751789519682-4410055792: POST /api/profiles/
2025-07-06 15:11:59 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789519682-4410055792: name 'ProfileStatus' is not defined
2025-07-06 15:11:59 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789519682-4410055792: name 'ProfileStatus' is not defined
2025-07-06 15:12:40 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:12:40 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:12:40 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:12:40 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:12:40 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:12:40 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:12:40 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:12:40 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:12:40 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:12:40 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:12:40 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:12:40 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:12:40 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:12:40 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:13:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751789636589-4626144368: POST /api/profiles/
2025-07-06 15:13:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751789636589-4626144368: POST /api/profiles/
2025-07-06 15:13:56 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789636589-4626144368: can't set attribute
2025-07-06 15:13:56 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789636589-4626144368: can't set attribute
2025-07-06 15:14:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751789679550-4643375904: GET /api/profiles
2025-07-06 15:14:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751789679550-4643375904: GET /api/profiles
2025-07-06 15:14:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751789679550-4643375904: 307
2025-07-06 15:14:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751789679550-4643375904: 307
2025-07-06 15:14:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751789679577-4643416576: GET /api/profiles/
2025-07-06 15:14:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751789679577-4643416576: GET /api/profiles/
2025-07-06 15:14:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751789679577-4643416576: 200
2025-07-06 15:14:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751789679577-4643416576: 200
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789682881-4643498256: GET /api/analytics/dashboard
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789682881-4643498256: GET /api/analytics/dashboard
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789682885-4627470176: GET /api/campaigns
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789682885-4627470176: GET /api/campaigns
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789682886-4626169232: GET /api/scraping/sessions
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789682886-4626169232: GET /api/scraping/sessions
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789682885-4627470176: 307
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789682885-4627470176: 307
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789682886-4626169232: 200
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789682886-4626169232: 200
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789682896-4625789040: GET /api/campaigns/
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751789682896-4625789040: GET /api/campaigns/
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789682896-4625789040: 200
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789682896-4625789040: 200
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789682881-4643498256: 200
2025-07-06 15:14:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751789682881-4643498256: 200
2025-07-06 15:14:51 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:14:51 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:14:51 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:14:51 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:14:51 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:14:51 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:14:51 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:14:51 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:14:51 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:14:51 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:14:51 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:14:51 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:14:51 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:14:51 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:14:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751789691954-4438999488: GET /api/profiles
2025-07-06 15:14:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751789691954-4438999488: GET /api/profiles
2025-07-06 15:14:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751789691954-4438999488: 307
2025-07-06 15:14:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751789691954-4438999488: 307
2025-07-06 15:14:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751789691957-4439120480: GET /api/profiles/
2025-07-06 15:14:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751789691957-4439120480: GET /api/profiles/
2025-07-06 15:14:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751789691957-4439120480: 200
2025-07-06 15:14:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751789691957-4439120480: 200
2025-07-06 15:15:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751789728909-4438572912: POST /api/profiles/
2025-07-06 15:15:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751789728909-4438572912: POST /api/profiles/
2025-07-06 15:15:28 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789728909-4438572912: (sqlite3.IntegrityError) NOT NULL constraint failed: profiles.id
[SQL: INSERT INTO profiles (name, description, status, user_agent, screen_resolution, timezone, language, proxy_host, proxy_port, proxy_username, proxy_password, proxy_type, facebook_email, facebook_password, facebook_user_id, facebook_username, profile_path, last_used, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('Test Profile', None, <ProfileStatus.ACTIVE: 'active'>, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-06 15:15:28 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789728909-4438572912: (sqlite3.IntegrityError) NOT NULL constraint failed: profiles.id
[SQL: INSERT INTO profiles (name, description, status, user_agent, screen_resolution, timezone, language, proxy_host, proxy_port, proxy_username, proxy_password, proxy_type, facebook_email, facebook_password, facebook_user_id, facebook_username, profile_path, last_used, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('Test Profile', None, <ProfileStatus.ACTIVE: 'active'>, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751789741308-4438568480: OPTIONS /api/profiles
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751789741308-4438568480: OPTIONS /api/profiles
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751789741308-4438568480: 200
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751789741308-4438568480: 200
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751789741312-4439399296: POST /api/profiles
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751789741312-4439399296: POST /api/profiles
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751789741312-4439399296: 307
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751789741312-4439399296: 307
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751789741318-4438967200: OPTIONS /api/profiles/
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751789741318-4438967200: OPTIONS /api/profiles/
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751789741318-4438967200: 200
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751789741318-4438967200: 200
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751789741323-4444425760: POST /api/profiles/
2025-07-06 15:15:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751789741323-4444425760: POST /api/profiles/
2025-07-06 15:15:41 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789741323-4444425760: (sqlite3.IntegrityError) NOT NULL constraint failed: profiles.id
[SQL: INSERT INTO profiles (name, description, status, user_agent, screen_resolution, timezone, language, proxy_host, proxy_port, proxy_username, proxy_password, proxy_type, facebook_email, facebook_password, facebook_user_id, facebook_username, profile_path, last_used, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('test', None, <ProfileStatus.ACTIVE: 'active'>, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-06 15:15:41 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789741323-4444425760: (sqlite3.IntegrityError) NOT NULL constraint failed: profiles.id
[SQL: INSERT INTO profiles (name, description, status, user_agent, screen_resolution, timezone, language, proxy_host, proxy_port, proxy_username, proxy_password, proxy_type, facebook_email, facebook_password, facebook_user_id, facebook_username, profile_path, last_used, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('test', None, <ProfileStatus.ACTIVE: 'active'>, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-06 15:15:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751789747792-4444463552: GET /api/profiles
2025-07-06 15:15:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751789747792-4444463552: GET /api/profiles
2025-07-06 15:15:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751789747792-4444463552: 307
2025-07-06 15:15:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751789747792-4444463552: 307
2025-07-06 15:15:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751789747795-4444506960: GET /api/profiles/
2025-07-06 15:15:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751789747795-4444506960: GET /api/profiles/
2025-07-06 15:15:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751789747795-4444506960: 200
2025-07-06 15:15:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751789747795-4444506960: 200
2025-07-06 15:16:45 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:16:45 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:16:45 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:16:45 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:16:45 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:16:45 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:16:45 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:16:45 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:16:45 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:16:45 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:16:45 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:16:45 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:16:45 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:16:45 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:16:56 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:16:56 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:16:56 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:16:56 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:16:56 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:16:56 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:16:57 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:16:57 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:16:57 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:16:57 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:16:57 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:16:57 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:16:57 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:16:57 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:17:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751789844975-4393364544: POST /api/profiles/
2025-07-06 15:17:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751789844975-4393364544: POST /api/profiles/
2025-07-06 15:17:24 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789844975-4393364544: 'ProfileCreate' object has no attribute 'proxy_config'
2025-07-06 15:17:24 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751789844975-4393364544: 'ProfileCreate' object has no attribute 'proxy_config'
2025-07-06 15:18:36 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:18:36 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:18:36 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:18:36 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:18:36 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:18:36 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:18:36 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:18:36 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:18:36 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:18:36 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:18:36 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:18:36 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:18:36 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:18:36 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:18:51 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:18:51 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:18:51 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:18:51 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:18:51 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:18:51 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:18:51 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:18:51 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:18:51 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:18:51 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:18:51 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:18:51 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:18:51 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:18:51 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:19:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751789964080-4598672496: POST /api/profiles/
2025-07-06 15:19:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751789964080-4598672496: POST /api/profiles/
2025-07-06 15:19:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751789964080-4598672496: 201
2025-07-06 15:19:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751789964080-4598672496: 201
2025-07-06 15:19:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751789997997-4598853536: GET /api/profiles/
2025-07-06 15:19:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751789997997-4598853536: GET /api/profiles/
2025-07-06 15:19:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751789997997-4598853536: 200
2025-07-06 15:19:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751789997997-4598853536: 200
2025-07-06 15:20:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751790041757-4598695248: GET /api/profiles
2025-07-06 15:20:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751790041757-4598695248: GET /api/profiles
2025-07-06 15:20:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751790041757-4598695248: 307
2025-07-06 15:20:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751790041757-4598695248: 307
2025-07-06 15:20:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751790041768-4600537776: GET /api/profiles/
2025-07-06 15:20:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751790041768-4600537776: GET /api/profiles/
2025-07-06 15:20:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751790041768-4600537776: 200
2025-07-06 15:20:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751790041768-4600537776: 200
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050580-4600540800: OPTIONS /api/profiles
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050580-4600540800: OPTIONS /api/profiles
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050580-4600540800: 200
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050580-4600540800: 200
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050595-4600439040: POST /api/profiles
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050595-4600439040: POST /api/profiles
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050595-4600439040: 307
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050595-4600439040: 307
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050600-4600439040: OPTIONS /api/profiles/
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050600-4600439040: OPTIONS /api/profiles/
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050600-4600439040: 200
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050600-4600439040: 200
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050603-4600538304: POST /api/profiles/
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050603-4600538304: POST /api/profiles/
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050603-4600538304: 201
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050603-4600538304: 201
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050620-4598696064: GET /api/profiles
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050620-4598696064: GET /api/profiles
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050620-4598696064: 307
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050620-4598696064: 307
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050636-4600558688: GET /api/profiles/
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751790050636-4600558688: GET /api/profiles/
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050636-4600558688: 200
2025-07-06 15:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751790050636-4600558688: 200
2025-07-06 15:20:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751790053919-4598673120: POST /api/profiles/318d863b-e6ee-496d-a6bd-55c2b9311644/facebook-login
2025-07-06 15:20:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751790053919-4598673120: POST /api/profiles/318d863b-e6ee-496d-a6bd-55c2b9311644/facebook-login
2025-07-06 15:20:53 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751790053919-4598673120: type object 'Profile' has no attribute 'proxy_config'
2025-07-06 15:20:53 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751790053919-4598673120: type object 'Profile' has no attribute 'proxy_config'
2025-07-06 15:21:09 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:21:09 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:21:09 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:21:09 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:21:09 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:21:09 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:21:12 | INFO | __main__:<module>:168 | Starting server on 127.0.0.1:8000
2025-07-06 15:21:12 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:21:12 | INFO | main:startup_event:72 | Starting Facebook Automation Backend Server...
2025-07-06 15:21:12 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:21:12 | INFO | app.core.database:init_database:139 | Database tables created successfully
2025-07-06 15:21:12 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:21:12 | INFO | main:startup_event:82 | Database initialized successfully
2025-07-06 15:21:12 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:21:12 | INFO | main:startup_event:86 | Backend server started successfully
2025-07-06 15:21:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751790079162-4391682112: GET /api/profiles
2025-07-06 15:21:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751790079162-4391682112: GET /api/profiles
2025-07-06 15:21:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751790079162-4391682112: 307
2025-07-06 15:21:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751790079162-4391682112: 307
2025-07-06 15:21:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751790079222-4391803296: GET /api/profiles/
2025-07-06 15:21:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751790079222-4391803296: GET /api/profiles/
2025-07-06 15:21:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751790079222-4391803296: 200
2025-07-06 15:21:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751790079222-4391803296: 200
2025-07-06 15:21:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751790083251-4391257424: GET /api/profiles
2025-07-06 15:21:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751790083251-4391257424: GET /api/profiles
2025-07-06 15:21:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751790083251-4391257424: 307
2025-07-06 15:21:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751790083251-4391257424: 307
2025-07-06 15:21:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751790083256-4392209088: GET /api/profiles/
2025-07-06 15:21:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751790083256-4392209088: GET /api/profiles/
2025-07-06 15:21:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751790083256-4392209088: 200
2025-07-06 15:21:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751790083256-4392209088: 200
2025-07-06 15:21:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751790086068-4392086592: POST /api/profiles/318d863b-e6ee-496d-a6bd-55c2b9311644/facebook-login
2025-07-06 15:21:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751790086068-4392086592: POST /api/profiles/318d863b-e6ee-496d-a6bd-55c2b9311644/facebook-login
2025-07-06 15:21:26 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751790086068-4392086592: type object 'Profile' has no attribute 'proxy_config'
2025-07-06 15:21:26 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751790086068-4392086592: type object 'Profile' has no attribute 'proxy_config'
2025-07-06 15:21:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751790091151-4392886768: POST /api/profiles/318d863b-e6ee-496d-a6bd-55c2b9311644/facebook-login-terminate
2025-07-06 15:21:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751790091151-4392886768: POST /api/profiles/318d863b-e6ee-496d-a6bd-55c2b9311644/facebook-login-terminate
2025-07-06 15:21:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751790091151-4392886768: 200
2025-07-06 15:21:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751790091151-4392886768: 200
2025-07-06 15:21:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751790094455-4392208992: POST /api/profiles/d538c701-e7b6-42be-828d-6a187b0cf024/facebook-login-terminate
2025-07-06 15:21:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751790094455-4392208992: POST /api/profiles/d538c701-e7b6-42be-828d-6a187b0cf024/facebook-login-terminate
2025-07-06 15:21:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751790094455-4392208992: 200
2025-07-06 15:21:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751790094455-4392208992: 200
2025-07-06 15:21:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751790097342-4391802576: POST /api/profiles/d538c701-e7b6-42be-828d-6a187b0cf024/facebook-login
2025-07-06 15:21:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751790097342-4391802576: POST /api/profiles/d538c701-e7b6-42be-828d-6a187b0cf024/facebook-login
2025-07-06 15:21:37 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751790097342-4391802576: type object 'Profile' has no attribute 'proxy_config'
2025-07-06 15:21:37 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751790097342-4391802576: type object 'Profile' has no attribute 'proxy_config'
2025-07-06 15:21:42 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:21:42 | INFO | main:shutdown_event:115 | Shutting down Facebook Automation Backend Server...
2025-07-06 15:21:42 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:21:42 | INFO | app.core.database:close_database:150 | Database connections closed
2025-07-06 15:21:42 | INFO | main:shutdown_event:121 | Database connections closed
2025-07-06 15:21:42 | INFO | main:shutdown_event:121 | Database connections closed
