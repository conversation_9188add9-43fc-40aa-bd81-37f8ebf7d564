"""
Database configuration optimized for SQLite performance.
"""
import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy import event, text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase

from .config import settings
from loguru import logger


class Base(DeclarativeBase):
    """Base class for all database models."""
    pass


# Create async engine with optimizations
engine = create_async_engine(
    settings.database_url,
    echo=settings.database_echo,
    pool_pre_ping=True,
    pool_recycle=300,  # Recycle connections every 5 minutes
    connect_args={
        "check_same_thread": False,  # Allow multiple threads
    }
)

# Session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,  # Manual flush for better control
)


@event.listens_for(engine.sync_engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set SQLite PRAGMA settings for optimal performance."""
    cursor = dbapi_connection.cursor()
    
    # Apply performance settings
    for pragma, value in settings.sqlite_pragma_settings.items():
        cursor.execute(f"PRAGMA {pragma} = {value}")
    
    cursor.close()


async def init_database():
    """Initialize database with tables."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def close_database():
    """Close database connections."""
    await engine.dispose()


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Get database session with automatic cleanup.
    Optimized for performance with manual transaction control.
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency for FastAPI to get database session."""
    async with get_db_session() as session:
        yield session


class DatabaseManager:
    """Database manager for handling connections and operations efficiently."""
    
    def __init__(self):
        self._engine = engine
        self._session_factory = AsyncSessionLocal
        self._lock = asyncio.Lock()
    
    async def execute_query(self, query: str, params: dict = None):
        """Execute raw SQL query with parameters."""
        async with get_db_session() as session:
            result = await session.execute(text(query), params or {})
            return result.fetchall()
    
    async def health_check(self) -> bool:
        """Check database health."""
        try:
            async with get_db_session() as session:
                await session.execute(text("SELECT 1"))
                return True
        except Exception:
            return False
    
    async def optimize_database(self):
        """Run database optimization commands."""
        async with self._lock:
            async with get_db_session() as session:
                # Analyze tables for query optimization
                await session.execute(text("ANALYZE"))
                
                # Vacuum to reclaim space (use sparingly)
                # await session.execute(text("VACUUM"))
                
                # Update statistics
                await session.execute(text("PRAGMA optimize"))


# Global database manager instance
db_manager = DatabaseManager()


# Utility functions for main.py
async def init_database():
    """Initialize database - create tables if they don't exist."""
    try:
        async with engine.begin() as conn:
            # Import all models to ensure they're registered
            from ..models.profile import Profile

            # Create all tables
            await conn.run_sync(Base.metadata.create_all)

        logger.info("Database tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        return False


async def close_database():
    """Close database connections."""
    try:
        await engine.dispose()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error closing database: {e}")
