"""Database models for Facebook Automation App."""

# Only import Profile for now to avoid relationship conflicts
from .profile import Profile, ProxyConfig

# Other models commented out for compatibility
# from .campaign import Campaign, MessageLog, CampaignStatus, MessageStatus
# from .scraped_data import ScrapedUser, ScrapingSession, ScrapingType, Gender
# from .messaging import (
#     MessageTemplate, MessageCampaign, Message, MessageQueue, MessageAnalytics,
#     MessageStatus as MessagingStatus, MessageType, CampaignStatus as MessagingCampaignStatus
# )

__all__ = [
    "Profile",
    "ProxyConfig",
    # Other models commented out for compatibility
    # "ProfileStatus",
    # "ProxyType",
    # "Campaign",
    # "MessageLog",
    # "CampaignStatus",
    # "MessageStatus",
    # "ScrapedUser",
    # "ScrapingSession",
    # "ScrapingType",
    # "Gender",
    # "MessageTemplate",
    # "MessageCampaign",
    # "Message",
    # "MessageQueue",
    # "MessageAnalytics",
    # "MessagingStatus",
    # "MessageType",
    # "MessagingCampaignStatus"
]
