
import asyncio
import sys
import os
import json
import time

# Add zendriver to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../zendriver_local"))

try:
    import zendriver as zd
except ImportError as e:
    print(f"Error importing zendriver: {e}")
    print("Make sure zendriver is installed and accessible")
    sys.exit(1)

async def main():
    """Launch antidetect browser for Facebook login."""
    try:
        print("Starting antidetect browser for Facebook login...")
        print("Profile: test (ID: 318d863b-e6ee-496d-a6bd-55c2b9311644)")
        
        # Browser configuration with antidetect settings
        config = {
        "user_data_dir": "/var/folders/4x/pqrmqft56vbcg_qz5m_t8ms00000gn/T/browser_profiles/profile_318d863b-e6ee-496d-a6bd-55c2b9311644",
        "headless": False,
        "disable_blink_features": "AutomationControlled",
        "exclude_switches": [
                "enable-automation"
        ],
        "use_subprocess": False,
        "disable_dev_shm_usage": True,
        "no_sandbox": True,
        "arguments": [
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "--window-size=1920,1080",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-extensions-except",
                "--disable-plugins-discovery",
                "--no-first-run",
                "--no-service-autorun",
                "--password-store=basic",
                "--disable-webrtc",
                "--disable-background-networking"
        ]
}

        # Start browser with antidetect configuration
        browser = await zd.start(**config)
        print("Browser started successfully")
        
        # Navigate to Facebook login
        page = await browser.get("https://www.facebook.com/login")
        print("Navigated to Facebook login page")
        
        # Set page title to identify the session
        await page.evaluate("""
            document.title = 'Facebook Login - test - Complete login manually then click Complete Login in app';
        """)
        
        print("\n" + "="*60)
        print("FACEBOOK LOGIN INSTRUCTIONS:")
        print("1. Enter your Facebook email/phone and password")
        print("2. Complete any 2FA verification if prompted")
        print("3. Solve any CAPTCHA or security challenges")
        print("4. Once logged in, click 'Complete Login' in the app")
        print("="*60 + "\n")
        
        # Keep browser open for manual login
        # Browser will stay open until user completes login or timeout
        session_file = os.path.join(os.path.dirname(__file__), "session_status.json")
        
        start_time = time.time()
        timeout = 3600  # 1 hour
        
        while time.time() - start_time < timeout:
            # Check if session should be terminated
            if os.path.exists(session_file):
                with open(session_file, 'r') as f:
                    status = json.load(f)
                    if status.get('action') == 'terminate':
                        print("Session termination requested")
                        break
            
            # Check if user is logged in by looking for Facebook elements
            try:
                # Check if we're on Facebook homepage or logged in
                current_url = page.url
                if 'facebook.com' in current_url and '/login' not in current_url:
                    print("Login detected! User appears to be logged in.")
                    # Update session status
                    with open(session_file, 'w') as f:
                        json.dump({
                            'status': 'logged_in',
                            'url': current_url,
                            'timestamp': time.time()
                        }, f)
            except Exception as e:
                print(f"Error checking login status: {e}")
            
            await asyncio.sleep(5)  # Check every 5 seconds
        
        print("Session ending...")
        await browser.stop()
        
    except Exception as e:
        print(f"Error in browser session: {e}")
        import traceback
        traceback.print_exc()
    
    print("Browser session ended")

if __name__ == "__main__":
    asyncio.run(main())
