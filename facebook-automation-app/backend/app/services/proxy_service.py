"""
Proxy service for testing and validating proxy connections.
"""
import asyncio
import aiohttp
import socket
import time
from typing import Dict, Any, Optional, Tuple
import logging

from ..models.profile import ProxyConfig, ProxyType

logger = logging.getLogger(__name__)


class ProxyService:
    """Service for testing and managing proxy connections."""
    
    def __init__(self):
        self.test_timeout = 10  # seconds
        self.test_urls = [
            "http://httpbin.org/ip",
            "https://api.ipify.org?format=json",
            "http://ip-api.com/json"
        ]
    
    async def test_proxy_connection(self, proxy_config: ProxyConfig) -> Dict[str, Any]:
        """Test proxy connection and return detailed results."""
        if proxy_config.proxy_type == ProxyType.NO_PROXY:
            return {
                "status": "no_proxy",
                "message": "No proxy configured - using direct connection",
                "response_time": 0,
                "ip_address": None,
                "working": True
            }
        
        # Validate configuration first
        is_valid, validation_message = proxy_config.validate_proxy_config()
        if not is_valid:
            return {
                "status": "error",
                "message": validation_message,
                "response_time": 0,
                "ip_address": None,
                "working": False
            }
        
        # Test different proxy types
        if proxy_config.proxy_type in [ProxyType.HTTP, ProxyType.HTTPS]:
            return await self._test_http_proxy(proxy_config)
        elif proxy_config.proxy_type == ProxyType.SOCKS5:
            return await self._test_socks5_proxy(proxy_config)
        elif proxy_config.proxy_type == ProxyType.SSH:
            return await self._test_ssh_proxy(proxy_config)
        else:
            return {
                "status": "error",
                "message": f"Unsupported proxy type: {proxy_config.proxy_type}",
                "response_time": 0,
                "ip_address": None,
                "working": False
            }
    
    async def _test_http_proxy(self, proxy_config: ProxyConfig) -> Dict[str, Any]:
        """Test HTTP/HTTPS proxy connection."""
        proxy_url = proxy_config.get_proxy_url()
        if not proxy_url:
            return {
                "status": "error",
                "message": "Invalid proxy configuration",
                "response_time": 0,
                "ip_address": None,
                "working": False
            }
        
        start_time = time.time()
        
        try:
            # Create connector with proxy
            connector = aiohttp.ProxyConnector.from_url(proxy_url)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=self.test_timeout)
            ) as session:
                
                # Test with multiple URLs
                for test_url in self.test_urls:
                    try:
                        async with session.get(test_url) as response:
                            if response.status == 200:
                                response_time = time.time() - start_time
                                data = await response.json()
                                
                                # Extract IP address from response
                                ip_address = None
                                if "ip" in data:
                                    ip_address = data["ip"]
                                elif "query" in data:
                                    ip_address = data["query"]
                                
                                return {
                                    "status": "success",
                                    "message": "Proxy connection successful",
                                    "response_time": round(response_time, 2),
                                    "ip_address": ip_address,
                                    "working": True,
                                    "test_url": test_url
                                }
                    except Exception as e:
                        logger.debug(f"Failed to test with {test_url}: {str(e)}")
                        continue
                
                # If all URLs failed
                response_time = time.time() - start_time
                return {
                    "status": "error",
                    "message": "All test URLs failed",
                    "response_time": round(response_time, 2),
                    "ip_address": None,
                    "working": False
                }
                
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            return {
                "status": "timeout",
                "message": f"Proxy connection timed out after {self.test_timeout} seconds",
                "response_time": round(response_time, 2),
                "ip_address": None,
                "working": False
            }
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "status": "error",
                "message": f"Proxy connection failed: {str(e)}",
                "response_time": round(response_time, 2),
                "ip_address": None,
                "working": False
            }
    
    async def _test_socks5_proxy(self, proxy_config: ProxyConfig) -> Dict[str, Any]:
        """Test SOCKS5 proxy connection."""
        start_time = time.time()
        
        try:
            # For SOCKS5, we'll do a basic socket connection test
            # In a real implementation, you'd use aiohttp-socks or similar
            
            # Basic socket test
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.test_timeout)
            
            result = sock.connect_ex((proxy_config.host, proxy_config.port))
            sock.close()
            
            response_time = time.time() - start_time
            
            if result == 0:
                return {
                    "status": "success",
                    "message": "SOCKS5 proxy connection successful (basic test)",
                    "response_time": round(response_time, 2),
                    "ip_address": None,
                    "working": True,
                    "note": "Full SOCKS5 testing requires additional libraries"
                }
            else:
                return {
                    "status": "error",
                    "message": f"SOCKS5 proxy connection failed (error code: {result})",
                    "response_time": round(response_time, 2),
                    "ip_address": None,
                    "working": False
                }
                
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "status": "error",
                "message": f"SOCKS5 proxy test failed: {str(e)}",
                "response_time": round(response_time, 2),
                "ip_address": None,
                "working": False
            }
    
    async def _test_ssh_proxy(self, proxy_config: ProxyConfig) -> Dict[str, Any]:
        """Test SSH proxy connection."""
        start_time = time.time()
        
        try:
            # For SSH, we'll do a basic socket connection test
            # In a real implementation, you'd use paramiko or asyncssh
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.test_timeout)
            
            result = sock.connect_ex((proxy_config.host, proxy_config.port))
            sock.close()
            
            response_time = time.time() - start_time
            
            if result == 0:
                return {
                    "status": "success",
                    "message": "SSH proxy connection successful (basic test)",
                    "response_time": round(response_time, 2),
                    "ip_address": None,
                    "working": True,
                    "note": "Full SSH testing requires authentication"
                }
            else:
                return {
                    "status": "error",
                    "message": f"SSH proxy connection failed (error code: {result})",
                    "response_time": round(response_time, 2),
                    "ip_address": None,
                    "working": False
                }
                
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "status": "error",
                "message": f"SSH proxy test failed: {str(e)}",
                "response_time": round(response_time, 2),
                "ip_address": None,
                "working": False
            }
    
    def get_proxy_recommendations(self, proxy_type: ProxyType) -> Dict[str, Any]:
        """Get recommendations for proxy configuration."""
        recommendations = {
            ProxyType.NO_PROXY: {
                "description": "Direct connection without proxy",
                "use_cases": ["Local development", "No anonymity required"],
                "security_level": "Low",
                "speed": "Fast"
            },
            ProxyType.HTTP: {
                "description": "HTTP proxy for web traffic",
                "use_cases": ["Web browsing", "API requests"],
                "common_ports": [8080, 3128, 8888, 80],
                "security_level": "Medium",
                "speed": "Fast"
            },
            ProxyType.HTTPS: {
                "description": "HTTPS proxy with SSL encryption",
                "use_cases": ["Secure web browsing", "Encrypted traffic"],
                "common_ports": [8080, 3128, 8888, 443],
                "security_level": "High",
                "speed": "Medium"
            },
            ProxyType.SOCKS5: {
                "description": "SOCKS5 proxy for all traffic types",
                "use_cases": ["All protocols", "High anonymity"],
                "common_ports": [1080, 1081, 9050],
                "security_level": "High",
                "speed": "Medium"
            },
            ProxyType.SSH: {
                "description": "SSH tunnel proxy",
                "use_cases": ["Secure tunneling", "Server access"],
                "common_ports": [22, 2222],
                "security_level": "Very High",
                "speed": "Slow"
            }
        }
        
        return recommendations.get(proxy_type, {})
    
    async def batch_test_proxies(self, proxy_configs: list[ProxyConfig]) -> Dict[int, Dict[str, Any]]:
        """Test multiple proxy configurations concurrently."""
        tasks = []
        for proxy_config in proxy_configs:
            task = asyncio.create_task(self.test_proxy_connection(proxy_config))
            tasks.append((proxy_config.id, task))
        
        results = {}
        for proxy_id, task in tasks:
            try:
                result = await task
                results[proxy_id] = result
            except Exception as e:
                results[proxy_id] = {
                    "status": "error",
                    "message": f"Test failed: {str(e)}",
                    "response_time": 0,
                    "ip_address": None,
                    "working": False
                }
        
        return results
