#!/bin/bash

echo "🔄 Restarting Facebook Automation Backend Server..."

# Kill existing backend processes
echo "Stopping existing backend processes..."
pkill -f "python.*main.py" || true
pkill -f "uvicorn.*main:app" || true

# Wait a moment for processes to stop
sleep 2

# Navigate to backend directory
cd "$(dirname "$0")/backend"

echo "Starting backend server..."

# Check if virtual environment exists
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

# Install dependencies if needed
if [ -f "requirements.txt" ]; then
    echo "Installing/updating dependencies..."
    pip install -r requirements.txt > /dev/null 2>&1
fi

# Start the backend server
echo "Starting server on http://localhost:8000..."
python main.py &

# Store the PID
BACKEND_PID=$!
echo "Backend server started with PID: $BACKEND_PID"

# Wait a moment for server to start
sleep 3

# Test if server is running
echo "Testing server connection..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ Backend server is running successfully!"
    echo "📋 Available endpoints:"
    echo "   - Health: http://localhost:8000/health"
    echo "   - Docs: http://localhost:8000/docs"
    echo "   - Profiles: http://localhost:8000/api/profiles"
    echo ""
    echo "🧪 To test Facebook endpoints, run:"
    echo "   node test_facebook_endpoints.js"
else
    echo "❌ Backend server failed to start or is not responding"
    echo "Check the logs above for errors"
fi

echo ""
echo "To stop the server, run: kill $BACKEND_PID"
echo "Or use: pkill -f 'python.*main.py'"
