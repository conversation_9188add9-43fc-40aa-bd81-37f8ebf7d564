{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, Button, Grid, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Switch, FormControlLabel, Divider, Avatar, Menu, ListItemIcon, ListItemText, Alert, LinearProgress, Tooltip, FormHelperText } from '@mui/material';\nimport { Add as AddIcon, Person as PersonIcon, MoreVert as MoreVertIcon, Edit as EditIcon, Delete as DeleteIcon, PlayArrow as PlayIcon, Refresh as RefreshIcon, Computer as ComputerIcon, Security as SecurityIcon, Language as LanguageIcon, LocationOn as LocationIcon } from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProfileCard({\n  profile,\n  onEdit,\n  onDelete,\n  onTest\n}) {\n  _s();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const handleMenuClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'flex-start',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: 'primary.main',\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            children: profile.name.charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                mb: 0.5\n              },\n              children: profile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: profile.status || 'inactive',\n              size: \"small\",\n              color: getStatusColor(profile.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleMenuClick,\n          children: /*#__PURE__*/_jsxDEV(MoreVertIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(ComputerIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"Chrome\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LocationIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.timezone || 'UTC'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Proxy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.proxy_type && profile.proxy_type !== 'no_proxy' ? profile.proxy_type.toUpperCase() : 'No Proxy'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.language || 'en-US'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 24\n          }, this),\n          onClick: () => onTest(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"text\",\n          startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 24\n          }, this),\n          onClick: () => onEdit(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        anchorEl: anchorEl,\n        open: Boolean(anchorEl),\n        onClose: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onEdit(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(EditIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Edit Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onTest(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(PlayIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Test Browser\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onDelete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Delete Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n}\n_s(ProfileCard, \"+aMDa7FPcESUyQDF1vq0RSMn4/k=\");\n_c = ProfileCard;\nfunction Profiles() {\n  _s2();\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: ''\n  });\n  const queryClient = useQueryClient();\n  const {\n    setProfiles\n  } = useApp();\n\n  // Fetch profiles\n  const {\n    data: profilesData,\n    isLoading,\n    error,\n    refetch\n  } = useQuery('profiles', async () => {\n    try {\n      console.log('Making API call to /api/profiles...');\n      const response = await profilesAPI.getAll();\n      console.log('Full API Response:', response);\n      console.log('Response status:', response.status);\n      console.log('Response headers:', response.headers);\n      console.log('Response data:', response.data);\n      console.log('Response data type:', typeof response.data);\n      console.log('Response data is array:', Array.isArray(response.data));\n      if (Array.isArray(response.data)) {\n        console.log('Response data length:', response.data.length);\n        if (response.data.length > 0) {\n          console.log('First item:', response.data[0]);\n        }\n      }\n      return response.data;\n    } catch (error) {\n      console.error('API call failed:', error);\n      console.error('Error response:', error.response);\n      throw error;\n    }\n  }, {\n    refetchOnWindowFocus: false,\n    staleTime: 0,\n    // Always refetch\n    cacheTime: 0,\n    // Don't cache\n    retry: 1,\n    onSuccess: data => {\n      console.log('Query onSuccess - Profiles fetched successfully:', data);\n      setProfiles(data || []);\n    },\n    onError: error => {\n      console.error('Query onError - Failed to load profiles:', error);\n      toast.error(`Failed to load profiles: ${error.message}`);\n    }\n  });\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      console.error('Create profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to create profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(({\n    id,\n    data\n  }) => profilesAPI.update(id, data), {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setEditDialogOpen(false);\n      resetForm();\n      toast.success('Profile updated successfully');\n    },\n    onError: error => {\n      var _error$response2, _error$response2$data;\n      console.error('Update profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to update profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: error => {\n      toast.error('Failed to delete profile');\n    }\n  });\n\n  // Test browser mutation\n  const testMutation = useMutation(profileId => profilesAPI.testBrowser(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'success') {\n        toast.success('Browser test completed successfully');\n      } else {\n        toast.warning(`Browser test: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response3, _error$response3$data;\n      console.error('Browser test error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Browser test failed';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Test proxy mutation\n  const testProxyMutation = useMutation(profileId => profilesAPI.testProxy(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'success') {\n        toast.success(`Proxy test successful! Response time: ${result.response_time}s`);\n      } else if (result.status === 'no_proxy') {\n        toast.info('No proxy configured for this profile');\n      } else {\n        toast.error(`Proxy test failed: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response4, _error$response4$data;\n      console.error('Proxy test error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Proxy test failed';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Mock data for testing\n  const mockProfiles = [{\n    id: \"mock-1\",\n    name: \"Mock Profile 1\",\n    status: \"active\",\n    user_agent: \"Mozilla/5.0 (Test)\",\n    timezone: \"UTC\",\n    language: \"en-US\",\n    proxy_type: \"http\",\n    proxy_host: \"proxy.example.com\",\n    proxy_port: 8080,\n    created_at: \"2025-07-06T02:46:41\",\n    updated_at: \"2025-07-06T02:46:41\"\n  }, {\n    id: \"mock-2\",\n    name: \"Mock Profile 2\",\n    status: \"inactive\",\n    user_agent: null,\n    timezone: \"Asia/Ho_Chi_Minh\",\n    language: \"vi-VN\",\n    proxy_type: \"no_proxy\",\n    proxy_host: null,\n    proxy_port: null,\n    created_at: \"2025-07-06T03:54:06\",\n    updated_at: \"2025-07-06T03:54:06\"\n  }];\n  const profiles = Array.isArray(profilesData) ? profilesData : [];\n\n  // Debug logging\n  console.log('=== PROFILES DEBUG ===');\n  console.log('profilesData:', profilesData);\n  console.log('profiles:', profiles);\n  console.log('profiles.length:', profiles.length);\n  console.log('isLoading:', isLoading);\n  console.log('error:', error);\n  console.log('======================');\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: ''\n    });\n    setSelectedProfile(null);\n  };\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n  const handleEdit = profile => {\n    setSelectedProfile(profile);\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome',\n      // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '',\n      // Not in current API response\n      proxy_password: '' // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n  const handleDelete = profile => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n  const handleTest = profile => {\n    // Test both proxy and browser\n    if (profile.proxy_type && profile.proxy_type !== 'no_proxy') {\n      testProxyMutation.mutate(profile.id);\n    } else {\n      testMutation.mutate(profile.id);\n    }\n  };\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = {\n      ...formData\n    };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n    if (selectedProfile) {\n      updateMutation.mutate({\n        id: selectedProfile.id,\n        data: finalData\n      });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Helper function to safely convert form data\n  const sanitizeFormData = data => {\n    const sanitized = {\n      ...data\n    };\n\n    // Convert empty strings to null for backend\n    Object.keys(sanitized).forEach(key => {\n      if (sanitized[key] === '') {\n        sanitized[key] = null;\n      }\n    });\n\n    // Convert port to integer if provided\n    if (sanitized.proxy_port && typeof sanitized.proxy_port === 'string') {\n      const port = parseInt(sanitized.proxy_port);\n      sanitized.proxy_port = isNaN(port) ? null : port;\n    }\n    return sanitized;\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      className: \"fade-in\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        children: \"Failed to load profiles. Please check your connection to the backend.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 700\n        },\n        children: \"Profile Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            console.log('Manual refresh clicked');\n            queryClient.invalidateQueries('profiles');\n            refetch();\n          },\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: async () => {\n            console.log('Testing API directly...');\n            try {\n              const response = await fetch('http://localhost:8000/api/profiles');\n              console.log('Direct fetch response:', response);\n              const data = await response.json();\n              console.log('Direct fetch data:', data);\n              toast.success(`Direct API test: ${data.length} profiles found`);\n            } catch (error) {\n              console.error('Direct fetch error:', error);\n              toast.error(`Direct API test failed: ${error.message}`);\n            }\n          },\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Test API\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"warning\",\n          onClick: () => {\n            console.log('Using mock data for testing...');\n            queryClient.setQueryData('profiles', mockProfiles);\n            toast.info('Loaded mock profiles for testing');\n          },\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Load Mock Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 24\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"New Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 7\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(LinearProgress, {\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 21\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2,\n        p: 2,\n        bgcolor: 'info.light',\n        borderRadius: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 1\n        },\n        children: \"Debug Info:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [\"\\u2022 profilesData type: \", typeof profilesData, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 53\n        }, this), \"\\u2022 profilesData is array: \", Array.isArray(profilesData).toString(), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 76\n        }, this), \"\\u2022 profilesData length: \", Array.isArray(profilesData) ? profilesData.length : 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 93\n        }, this), \"\\u2022 profiles length: \", profiles.length, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 47\n        }, this), \"\\u2022 isLoading: \", isLoading.toString(), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 46\n        }, this), \"\\u2022 error: \", error ? error.message : 'None', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 52\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 9\n      }, this), profilesData && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Raw Data:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            fontSize: '12px',\n            maxHeight: '200px',\n            overflow: 'auto'\n          },\n          children: JSON.stringify(profilesData, null, 2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 628,\n      columnNumber: 7\n    }, this), profiles.length === 0 && !isLoading ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center',\n          py: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n          sx: {\n            fontSize: 64,\n            color: 'text.secondary',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1\n          },\n          children: \"No Profiles Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 3\n          },\n          children: \"Create your first browser profile to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 26\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Create Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 652,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: profiles.map(profile => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(ProfileCard, {\n          profile: profile,\n          onEdit: handleEdit,\n          onDelete: handleDelete,\n          onTest: handleTest\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 15\n        }, this)\n      }, profile.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 673,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: createDialogOpen || editDialogOpen,\n      onClose: () => {\n        setCreateDialogOpen(false);\n        setEditDialogOpen(false);\n        resetForm();\n      },\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: selectedProfile ? 'Edit Profile' : 'Create New Profile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Profile Name\",\n                value: formData.name,\n                onChange: e => handleFormChange('name', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Browser Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.browser_type,\n                  onChange: e => handleFormChange('browser_type', e.target.value),\n                  label: \"Browser Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"chrome\",\n                    children: \"Chrome\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"firefox\",\n                    children: \"Firefox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"edge\",\n                    children: \"Edge\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Screen Resolution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.screen_resolution,\n                  onChange: e => handleFormChange('screen_resolution', e.target.value),\n                  label: \"Screen Resolution\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1920x1080\",\n                    children: \"1920x1080\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1366x768\",\n                    children: \"1366x768\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1440x900\",\n                    children: \"1440x900\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1280x720\",\n                    children: \"1280x720\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Timezone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.timezone,\n                  onChange: e => handleFormChange('timezone', e.target.value),\n                  label: \"Timezone\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"UTC\",\n                    children: \"UTC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Asia/Ho_Chi_Minh\",\n                    children: \"Asia/Ho_Chi_Minh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 752,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"America/New_York\",\n                    children: \"America/New_York\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 753,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Europe/London\",\n                    children: \"Europe/London\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.language,\n                  onChange: e => handleFormChange('language', e.target.value),\n                  label: \"Language\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"en-US\",\n                    children: \"English (US)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"vi-VN\",\n                    children: \"Vietnamese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 768,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"zh-CN\",\n                    children: \"Chinese (Simplified)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ja-JP\",\n                    children: \"Japanese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"User Agent (Optional)\",\n                value: formData.user_agent,\n                onChange: e => handleFormChange('user_agent', e.target.value),\n                placeholder: \"Leave empty for automatic generation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 600\n                },\n                children: \"Proxy Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Proxy Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.proxy_type,\n                  onChange: e => {\n                    const proxyType = e.target.value;\n                    handleFormChange('proxy_type', proxyType);\n                    handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                  },\n                  label: \"Proxy Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"no_proxy\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"No Proxy (Local Network)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 805,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Direct connection without proxy\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 806,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 804,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"http\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTP\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 813,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Standard HTTP proxy (ports: 8080, 3128, 8888)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 814,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 812,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"https\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTPS\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 821,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Encrypted HTTPS proxy (ports: 8080, 3128, 443)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 822,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 820,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 819,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"socks5\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SOCKS5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 829,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"High anonymity proxy (ports: 1080, 1081, 9050)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 830,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 828,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ssh\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SSH\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Secure SSH tunnel (ports: 22, 2222)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 838,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 836,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: [formData.proxy_type === 'no_proxy' && 'Using direct connection', formData.proxy_type === 'http' && 'Fast, suitable for web browsing', formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data', formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols', formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 15\n            }, this), formData.proxy_type !== 'no_proxy' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Host\",\n                  value: formData.proxy_host,\n                  onChange: e => handleFormChange('proxy_host', e.target.value),\n                  required: true,\n                  placeholder: formData.proxy_type === 'ssh' ? \"e.g., ssh.example.com or *************\" : \"e.g., proxy.example.com or *************\",\n                  helperText: \"IP address or domain name of the proxy server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 857,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Port\",\n                  value: formData.proxy_port,\n                  onChange: e => handleFormChange('proxy_port', e.target.value),\n                  type: \"number\",\n                  required: true,\n                  placeholder: formData.proxy_type === 'http' ? \"8080, 3128, 8888\" : formData.proxy_type === 'https' ? \"8080, 3128, 443\" : formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" : formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\",\n                  helperText: `Common ${formData.proxy_type.toUpperCase()} ports`,\n                  inputProps: {\n                    min: 1,\n                    max: 65535\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\",\n                  value: formData.proxy_username,\n                  onChange: e => handleFormChange('proxy_username', e.target.value),\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH username for authentication\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Required for SSH connections\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 889,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\",\n                  value: formData.proxy_password,\n                  onChange: e => handleFormChange('proxy_password', e.target.value),\n                  type: \"password\",\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH password or leave empty for key-based auth\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Password or private key authentication\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2,\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    onClick: () => {\n                      if (selectedProfile) {\n                        testProxyMutation.mutate(selectedProfile.id);\n                      } else {\n                        toast.info('Save the profile first to test proxy connection');\n                      }\n                    },\n                    disabled: !formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading,\n                    sx: {\n                      textTransform: 'none'\n                    },\n                    children: testProxyMutation.isLoading ? 'Testing...' : 'Test Proxy Connection'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Test proxy connection to ensure it works with antidetect browser\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 944,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 928,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 927,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 699,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setCreateDialogOpen(false);\n            setEditDialogOpen(false);\n            resetForm();\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 955,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleSubmit,\n          disabled: !formData.name || createMutation.isLoading || updateMutation.isLoading,\n          children: selectedProfile ? 'Update' : 'Create'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 964,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 954,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 686,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 564,\n    columnNumber: 5\n  }, this);\n}\n_s2(Profiles, \"rfOnSaNVwjJtDENkhurI8iOt/WI=\", false, function () {\n  return [useQueryClient, useApp, useQuery, useMutation, useMutation, useMutation, useMutation, useMutation];\n});\n_c2 = Profiles;\nexport default Profiles;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProfileCard\");\n$RefreshReg$(_c2, \"Profiles\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Switch", "FormControlLabel", "Divider", "Avatar", "<PERSON><PERSON>", "ListItemIcon", "ListItemText", "<PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON><PERSON>", "FormHelperText", "Add", "AddIcon", "Person", "PersonIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "PlayArrow", "PlayIcon", "Refresh", "RefreshIcon", "Computer", "ComputerIcon", "Security", "SecurityIcon", "Language", "LanguageIcon", "LocationOn", "LocationIcon", "useQuery", "useMutation", "useQueryClient", "toast", "profilesAPI", "useApp", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfileCard", "profile", "onEdit", "onDelete", "onTest", "_s", "anchorEl", "setAnchorEl", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "getStatusColor", "status", "sx", "height", "children", "display", "justifyContent", "alignItems", "mb", "bgcolor", "width", "mr", "name", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "label", "size", "color", "onClick", "my", "container", "spacing", "item", "xs", "fontSize", "timezone", "proxy_type", "language", "mt", "gap", "startIcon", "textTransform", "open", "Boolean", "onClose", "_c", "Profiles", "_s2", "createDialogOpen", "setCreateDialogOpen", "editDialogOpen", "setEditDialogOpen", "selectedProfile", "setSelectedProfile", "formData", "setFormData", "browser_type", "user_agent", "screen_resolution", "proxy_enabled", "proxy_host", "proxy_port", "proxy_username", "proxy_password", "queryClient", "setProfiles", "data", "profilesData", "isLoading", "error", "refetch", "console", "log", "response", "getAll", "headers", "Array", "isArray", "length", "refetchOnWindowFocus", "staleTime", "cacheTime", "retry", "onSuccess", "onError", "message", "createMutation", "create", "invalidateQueries", "resetForm", "success", "_error$response", "_error$response$data", "errorMessage", "detail", "updateMutation", "id", "update", "_error$response2", "_error$response2$data", "deleteMutation", "delete", "testMutation", "profileId", "testBrowser", "result", "warning", "_error$response3", "_error$response3$data", "testProxyMutation", "testProxy", "response_time", "info", "_error$response4", "_error$response4$data", "mockProfiles", "created_at", "updated_at", "profiles", "handleCreate", "handleEdit", "toString", "handleDelete", "window", "confirm", "mutate", "handleTest", "handleSubmit", "submitData", "port", "parseInt", "isNaN", "finalData", "browser_config", "proxy_config", "host", "username", "password", "handleFormChange", "field", "value", "prev", "sanitizeFormData", "sanitized", "Object", "keys", "for<PERSON>ach", "key", "className", "severity", "fetch", "json", "setQueryData", "p", "borderRadius", "style", "maxHeight", "overflow", "JSON", "stringify", "textAlign", "py", "map", "sm", "md", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "onChange", "e", "target", "required", "placeholder", "proxyType", "helperText", "type", "inputProps", "min", "max", "disabled", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Grid,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Switch,\n  FormControlLabel,\n  Divider,\n  Avatar,\n  Menu,\n  ListItemIcon,\n  ListItemText,\n  Alert,\n  LinearProgress,\n  Tooltip,\n  FormHelperText,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Person as PersonIcon,\n  MoreVert as MoreVertIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  PlayArrow as PlayIcon,\n\n  Refresh as RefreshIcon,\n\n  Computer as ComputerIcon,\n  Security as SecurityIcon,\n  Language as LanguageIcon,\n  LocationOn as LocationIcon,\n} from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\n\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\n\nfunction ProfileCard({ profile, onEdit, onDelete, onTest }) {\n  const [anchorEl, setAnchorEl] = useState(null);\n\n  const handleMenuClick = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Avatar\n              sx={{\n                bgcolor: 'primary.main',\n                width: 48,\n                height: 48,\n                mr: 2,\n              }}\n            >\n              {profile.name.charAt(0).toUpperCase()}\n            </Avatar>\n            <Box>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                {profile.name}\n              </Typography>\n              <Chip\n                label={profile.status || 'inactive'}\n                size=\"small\"\n                color={getStatusColor(profile.status)}\n              />\n            </Box>\n          </Box>\n          <IconButton onClick={handleMenuClick}>\n            <MoreVertIcon />\n          </IconButton>\n        </Box>\n\n        <Divider sx={{ my: 2 }} />\n\n        <Grid container spacing={2}>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <ComputerIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Browser\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              Chrome\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LocationIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Location\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.timezone || 'UTC'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <SecurityIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Proxy\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.proxy_type && profile.proxy_type !== 'no_proxy'\n                ? profile.proxy_type.toUpperCase()\n                : 'No Proxy'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LanguageIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Language\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.language || 'en-US'}\n            </Typography>\n          </Grid>\n        </Grid>\n\n        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>\n          <Button\n            size=\"small\"\n            variant=\"outlined\"\n            startIcon={<PlayIcon />}\n            onClick={() => onTest(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Test\n          </Button>\n          <Button\n            size=\"small\"\n            variant=\"text\"\n            startIcon={<EditIcon />}\n            onClick={() => onEdit(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Edit\n          </Button>\n        </Box>\n\n        <Menu\n          anchorEl={anchorEl}\n          open={Boolean(anchorEl)}\n          onClose={handleMenuClose}\n        >\n          <MenuItem onClick={() => { onEdit(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <EditIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Edit Profile</ListItemText>\n          </MenuItem>\n          <MenuItem onClick={() => { onTest(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <PlayIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Test Browser</ListItemText>\n          </MenuItem>\n          <MenuItem onClick={() => { onDelete(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <DeleteIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Delete Profile</ListItemText>\n          </MenuItem>\n        </Menu>\n      </CardContent>\n    </Card>\n  );\n}\n\nfunction Profiles() {\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: '',\n  });\n\n  const queryClient = useQueryClient();\n  const { setProfiles } = useApp();\n\n  // Fetch profiles\n  const { data: profilesData, isLoading, error, refetch } = useQuery(\n    'profiles',\n    async () => {\n      try {\n        console.log('Making API call to /api/profiles...');\n        const response = await profilesAPI.getAll();\n        console.log('Full API Response:', response);\n        console.log('Response status:', response.status);\n        console.log('Response headers:', response.headers);\n        console.log('Response data:', response.data);\n        console.log('Response data type:', typeof response.data);\n        console.log('Response data is array:', Array.isArray(response.data));\n\n        if (Array.isArray(response.data)) {\n          console.log('Response data length:', response.data.length);\n          if (response.data.length > 0) {\n            console.log('First item:', response.data[0]);\n          }\n        }\n\n        return response.data;\n      } catch (error) {\n        console.error('API call failed:', error);\n        console.error('Error response:', error.response);\n        throw error;\n      }\n    },\n    {\n      refetchOnWindowFocus: false,\n      staleTime: 0, // Always refetch\n      cacheTime: 0, // Don't cache\n      retry: 1,\n      onSuccess: (data) => {\n        console.log('Query onSuccess - Profiles fetched successfully:', data);\n        setProfiles(data || []);\n      },\n      onError: (error) => {\n        console.error('Query onError - Failed to load profiles:', error);\n        toast.error(`Failed to load profiles: ${error.message}`);\n      },\n    }\n  );\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: (error) => {\n      console.error('Create profile error:', error);\n      const errorMessage = error?.response?.data?.detail ||\n                          error?.message ||\n                          'Failed to create profile';\n      toast.error(errorMessage);\n    },\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(\n    ({ id, data }) => profilesAPI.update(id, data),\n    {\n      onSuccess: () => {\n        queryClient.invalidateQueries('profiles');\n        setEditDialogOpen(false);\n        resetForm();\n        toast.success('Profile updated successfully');\n      },\n      onError: (error) => {\n        console.error('Update profile error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to update profile';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: (error) => {\n      toast.error('Failed to delete profile');\n    },\n  });\n\n  // Test browser mutation\n  const testMutation = useMutation(\n    (profileId) => profilesAPI.testBrowser(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'success') {\n          toast.success('Browser test completed successfully');\n        } else {\n          toast.warning(`Browser test: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Browser test error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Browser test failed';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  // Test proxy mutation\n  const testProxyMutation = useMutation(\n    (profileId) => profilesAPI.testProxy(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'success') {\n          toast.success(`Proxy test successful! Response time: ${result.response_time}s`);\n        } else if (result.status === 'no_proxy') {\n          toast.info('No proxy configured for this profile');\n        } else {\n          toast.error(`Proxy test failed: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Proxy test error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Proxy test failed';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  // Mock data for testing\n  const mockProfiles = [\n    {\n      id: \"mock-1\",\n      name: \"Mock Profile 1\",\n      status: \"active\",\n      user_agent: \"Mozilla/5.0 (Test)\",\n      timezone: \"UTC\",\n      language: \"en-US\",\n      proxy_type: \"http\",\n      proxy_host: \"proxy.example.com\",\n      proxy_port: 8080,\n      created_at: \"2025-07-06T02:46:41\",\n      updated_at: \"2025-07-06T02:46:41\"\n    },\n    {\n      id: \"mock-2\",\n      name: \"Mock Profile 2\",\n      status: \"inactive\",\n      user_agent: null,\n      timezone: \"Asia/Ho_Chi_Minh\",\n      language: \"vi-VN\",\n      proxy_type: \"no_proxy\",\n      proxy_host: null,\n      proxy_port: null,\n      created_at: \"2025-07-06T03:54:06\",\n      updated_at: \"2025-07-06T03:54:06\"\n    }\n  ];\n\n  const profiles = Array.isArray(profilesData) ? profilesData : [];\n\n  // Debug logging\n  console.log('=== PROFILES DEBUG ===');\n  console.log('profilesData:', profilesData);\n  console.log('profiles:', profiles);\n  console.log('profiles.length:', profiles.length);\n  console.log('isLoading:', isLoading);\n  console.log('error:', error);\n  console.log('======================');\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: '',\n    });\n    setSelectedProfile(null);\n  };\n\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n\n  const handleEdit = (profile) => {\n    setSelectedProfile(profile);\n\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome', // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '', // Not in current API response\n      proxy_password: '', // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n\n  const handleDelete = (profile) => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n\n  const handleTest = (profile) => {\n    // Test both proxy and browser\n    if (profile.proxy_type && profile.proxy_type !== 'no_proxy') {\n      testProxyMutation.mutate(profile.id);\n    } else {\n      testMutation.mutate(profile.id);\n    }\n  };\n\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = { ...formData };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n\n    if (selectedProfile) {\n      updateMutation.mutate({ id: selectedProfile.id, data: finalData });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n  };\n\n  // Helper function to safely convert form data\n  const sanitizeFormData = (data) => {\n    const sanitized = { ...data };\n\n    // Convert empty strings to null for backend\n    Object.keys(sanitized).forEach(key => {\n      if (sanitized[key] === '') {\n        sanitized[key] = null;\n      }\n    });\n\n    // Convert port to integer if provided\n    if (sanitized.proxy_port && typeof sanitized.proxy_port === 'string') {\n      const port = parseInt(sanitized.proxy_port);\n      sanitized.proxy_port = isNaN(port) ? null : port;\n    }\n\n    return sanitized;\n  };\n\n  if (error) {\n    return (\n      <Box className=\"fade-in\">\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          Failed to load profiles. Please check your connection to the backend.\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box className=\"fade-in\">\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n          Profile Management\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => {\n              console.log('Manual refresh clicked');\n              queryClient.invalidateQueries('profiles');\n              refetch();\n            }}\n            sx={{ textTransform: 'none' }}\n          >\n            Refresh\n          </Button>\n          <Button\n            variant=\"outlined\"\n            color=\"secondary\"\n            onClick={async () => {\n              console.log('Testing API directly...');\n              try {\n                const response = await fetch('http://localhost:8000/api/profiles');\n                console.log('Direct fetch response:', response);\n                const data = await response.json();\n                console.log('Direct fetch data:', data);\n                toast.success(`Direct API test: ${data.length} profiles found`);\n              } catch (error) {\n                console.error('Direct fetch error:', error);\n                toast.error(`Direct API test failed: ${error.message}`);\n              }\n            }}\n            sx={{ textTransform: 'none' }}\n          >\n            Test API\n          </Button>\n          <Button\n            variant=\"outlined\"\n            color=\"warning\"\n            onClick={() => {\n              console.log('Using mock data for testing...');\n              queryClient.setQueryData('profiles', mockProfiles);\n              toast.info('Loaded mock profiles for testing');\n            }}\n            sx={{ textTransform: 'none' }}\n          >\n            Load Mock Data\n          </Button>\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={handleCreate}\n            sx={{ textTransform: 'none' }}\n          >\n            New Profile\n          </Button>\n        </Box>\n      </Box>\n\n      {isLoading && <LinearProgress sx={{ mb: 3 }} />}\n\n      {/* Debug Info */}\n      <Box sx={{ mb: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>\n        <Typography variant=\"h6\" sx={{ mb: 1 }}>Debug Info:</Typography>\n        <Typography variant=\"body2\">\n          • profilesData type: {typeof profilesData}<br/>\n          • profilesData is array: {Array.isArray(profilesData).toString()}<br/>\n          • profilesData length: {Array.isArray(profilesData) ? profilesData.length : 'N/A'}<br/>\n          • profiles length: {profiles.length}<br/>\n          • isLoading: {isLoading.toString()}<br/>\n          • error: {error ? error.message : 'None'}<br/>\n        </Typography>\n        {profilesData && (\n          <Box sx={{ mt: 1 }}>\n            <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>Raw Data:</Typography>\n            <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>\n              {JSON.stringify(profilesData, null, 2)}\n            </pre>\n          </Box>\n        )}\n      </Box>\n\n\n\n      {profiles.length === 0 && !isLoading ? (\n        <Card>\n          <CardContent sx={{ textAlign: 'center', py: 8 }}>\n            <PersonIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n            <Typography variant=\"h6\" sx={{ mb: 1 }}>\n              No Profiles Found\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              Create your first browser profile to get started\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={handleCreate}\n              sx={{ textTransform: 'none' }}\n            >\n              Create Profile\n            </Button>\n          </CardContent>\n        </Card>\n      ) : (\n        <Grid container spacing={3}>\n          {profiles.map((profile) => (\n            <Grid item xs={12} sm={6} md={4} key={profile.id}>\n              <ProfileCard\n                profile={profile}\n                onEdit={handleEdit}\n                onDelete={handleDelete}\n                onTest={handleTest}\n              />\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Create/Edit Profile Dialog */}\n      <Dialog\n        open={createDialogOpen || editDialogOpen}\n        onClose={() => {\n          setCreateDialogOpen(false);\n          setEditDialogOpen(false);\n          resetForm();\n        }}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          {selectedProfile ? 'Edit Profile' : 'Create New Profile'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Grid container spacing={3}>\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Profile Name\"\n                  value={formData.name}\n                  onChange={(e) => handleFormChange('name', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Browser Type</InputLabel>\n                  <Select\n                    value={formData.browser_type}\n                    onChange={(e) => handleFormChange('browser_type', e.target.value)}\n                    label=\"Browser Type\"\n                  >\n                    <MenuItem value=\"chrome\">Chrome</MenuItem>\n                    <MenuItem value=\"firefox\">Firefox</MenuItem>\n                    <MenuItem value=\"edge\">Edge</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Screen Resolution</InputLabel>\n                  <Select\n                    value={formData.screen_resolution}\n                    onChange={(e) => handleFormChange('screen_resolution', e.target.value)}\n                    label=\"Screen Resolution\"\n                  >\n                    <MenuItem value=\"1920x1080\">1920x1080</MenuItem>\n                    <MenuItem value=\"1366x768\">1366x768</MenuItem>\n                    <MenuItem value=\"1440x900\">1440x900</MenuItem>\n                    <MenuItem value=\"1280x720\">1280x720</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Timezone</InputLabel>\n                  <Select\n                    value={formData.timezone}\n                    onChange={(e) => handleFormChange('timezone', e.target.value)}\n                    label=\"Timezone\"\n                  >\n                    <MenuItem value=\"UTC\">UTC</MenuItem>\n                    <MenuItem value=\"Asia/Ho_Chi_Minh\">Asia/Ho_Chi_Minh</MenuItem>\n                    <MenuItem value=\"America/New_York\">America/New_York</MenuItem>\n                    <MenuItem value=\"Europe/London\">Europe/London</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Language</InputLabel>\n                  <Select\n                    value={formData.language}\n                    onChange={(e) => handleFormChange('language', e.target.value)}\n                    label=\"Language\"\n                  >\n                    <MenuItem value=\"en-US\">English (US)</MenuItem>\n                    <MenuItem value=\"vi-VN\">Vietnamese</MenuItem>\n                    <MenuItem value=\"zh-CN\">Chinese (Simplified)</MenuItem>\n                    <MenuItem value=\"ja-JP\">Japanese</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"User Agent (Optional)\"\n                  value={formData.user_agent}\n                  onChange={(e) => handleFormChange('user_agent', e.target.value)}\n                  placeholder=\"Leave empty for automatic generation\"\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n                  Proxy Configuration\n                </Typography>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Proxy Type</InputLabel>\n                  <Select\n                    value={formData.proxy_type}\n                    onChange={(e) => {\n                      const proxyType = e.target.value;\n                      handleFormChange('proxy_type', proxyType);\n                      handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                    }}\n                    label=\"Proxy Type\"\n                  >\n                    <MenuItem value=\"no_proxy\">\n                      <Box>\n                        <Typography variant=\"body2\">No Proxy (Local Network)</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Direct connection without proxy\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"http\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTP</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Standard HTTP proxy (ports: 8080, 3128, 8888)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"https\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTPS</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Encrypted HTTPS proxy (ports: 8080, 3128, 443)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"socks5\">\n                      <Box>\n                        <Typography variant=\"body2\">SOCKS5</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          High anonymity proxy (ports: 1080, 1081, 9050)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"ssh\">\n                      <Box>\n                        <Typography variant=\"body2\">SSH</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Secure SSH tunnel (ports: 22, 2222)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  </Select>\n                  <FormHelperText>\n                    {formData.proxy_type === 'no_proxy' && 'Using direct connection'}\n                    {formData.proxy_type === 'http' && 'Fast, suitable for web browsing'}\n                    {formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data'}\n                    {formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols'}\n                    {formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials'}\n                  </FormHelperText>\n                </FormControl>\n              </Grid>\n\n              {formData.proxy_type !== 'no_proxy' && (\n                <>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Host\"\n                      value={formData.proxy_host}\n                      onChange={(e) => handleFormChange('proxy_host', e.target.value)}\n                      required\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"e.g., ssh.example.com or *************\"\n                          : \"e.g., proxy.example.com or *************\"\n                      }\n                      helperText=\"IP address or domain name of the proxy server\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Port\"\n                      value={formData.proxy_port}\n                      onChange={(e) => handleFormChange('proxy_port', e.target.value)}\n                      type=\"number\"\n                      required\n                      placeholder={\n                        formData.proxy_type === 'http' ? \"8080, 3128, 8888\" :\n                        formData.proxy_type === 'https' ? \"8080, 3128, 443\" :\n                        formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" :\n                        formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\"\n                      }\n                      helperText={`Common ${formData.proxy_type.toUpperCase()} ports`}\n                      inputProps={{ min: 1, max: 65535 }}\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\"}\n                      value={formData.proxy_username}\n                      onChange={(e) => handleFormChange('proxy_username', e.target.value)}\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH username for authentication\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Required for SSH connections\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\"}\n                      value={formData.proxy_password}\n                      onChange={(e) => handleFormChange('proxy_password', e.target.value)}\n                      type=\"password\"\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH password or leave empty for key-based auth\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Password or private key authentication\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n\n                  <Grid item xs={12}>\n                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>\n                      <Button\n                        variant=\"outlined\"\n                        size=\"small\"\n                        onClick={() => {\n                          if (selectedProfile) {\n                            testProxyMutation.mutate(selectedProfile.id);\n                          } else {\n                            toast.info('Save the profile first to test proxy connection');\n                          }\n                        }}\n                        disabled={!formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading}\n                        sx={{ textTransform: 'none' }}\n                      >\n                        {testProxyMutation.isLoading ? 'Testing...' : 'Test Proxy Connection'}\n                      </Button>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Test proxy connection to ensure it works with antidetect browser\n                      </Typography>\n                    </Box>\n                  </Grid>\n                </>\n              )}\n            </Grid>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => {\n              setCreateDialogOpen(false);\n              setEditDialogOpen(false);\n              resetForm();\n            }}\n          >\n            Cancel\n          </Button>\n          <Button\n            variant=\"contained\"\n            onClick={handleSubmit}\n            disabled={!formData.name || createMutation.isLoading || updateMutation.isLoading}\n          >\n            {selectedProfile ? 'Update' : 'Create'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n}\n\nexport default Profiles;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,cAAc,EACdC,OAAO,EACPC,cAAc,QACT,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,QAAQ,EAErBC,OAAO,IAAIC,WAAW,EAEtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,YAAY,QACrB,qBAAqB;AAC5B,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACnE,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,MAAM,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,SAASC,WAAWA,CAAC;EAAEC,OAAO;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAC1D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAMqE,eAAe,GAAIC,KAAK,IAAK;IACjCF,WAAW,CAACE,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BJ,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMK,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACEhB,OAAA,CAACvD,IAAI;IAACwE,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC3BnB,OAAA,CAACtD,WAAW;MAAAyE,QAAA,gBACVnB,OAAA,CAACzD,GAAG;QAAC0E,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,YAAY;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAC7FnB,OAAA,CAACzD,GAAG;UAAC0E,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjDnB,OAAA,CAACrC,MAAM;YACLsD,EAAE,EAAE;cACFO,OAAO,EAAE,cAAc;cACvBC,KAAK,EAAE,EAAE;cACTP,MAAM,EAAE,EAAE;cACVQ,EAAE,EAAE;YACN,CAAE;YAAAP,QAAA,EAEDf,OAAO,CAACuB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACTjC,OAAA,CAACzD,GAAG;YAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;cAAC0F,OAAO,EAAC,IAAI;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEZ,EAAE,EAAE;cAAI,CAAE;cAAAJ,QAAA,EACvDf,OAAO,CAACuB;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACbjC,OAAA,CAACnD,IAAI;cACHuF,KAAK,EAAEhC,OAAO,CAACY,MAAM,IAAI,UAAW;cACpCqB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEvB,cAAc,CAACX,OAAO,CAACY,MAAM;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjC,OAAA,CAAClD,UAAU;UAACyF,OAAO,EAAE5B,eAAgB;UAAAQ,QAAA,eACnCnB,OAAA,CAACxB,YAAY;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENjC,OAAA,CAACtC,OAAO;QAACuD,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BjC,OAAA,CAACpD,IAAI;QAAC6F,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAvB,QAAA,gBACzBnB,OAAA,CAACpD,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfnB,OAAA,CAACzD,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDnB,OAAA,CAACd,YAAY;cAAC+B,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEjC,OAAA,CAACxD,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjC,OAAA,CAACxD,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EAAC;UAErD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPjC,OAAA,CAACpD,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfnB,OAAA,CAACzD,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDnB,OAAA,CAACR,YAAY;cAACyB,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEjC,OAAA,CAACxD,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjC,OAAA,CAACxD,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDf,OAAO,CAAC0C,QAAQ,IAAI;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPjC,OAAA,CAACpD,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfnB,OAAA,CAACzD,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDnB,OAAA,CAACZ,YAAY;cAAC6B,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEjC,OAAA,CAACxD,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjC,OAAA,CAACxD,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDf,OAAO,CAAC2C,UAAU,IAAI3C,OAAO,CAAC2C,UAAU,KAAK,UAAU,GACpD3C,OAAO,CAAC2C,UAAU,CAAClB,WAAW,CAAC,CAAC,GAChC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPjC,OAAA,CAACpD,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfnB,OAAA,CAACzD,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDnB,OAAA,CAACV,YAAY;cAAC2B,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEjC,OAAA,CAACxD,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjC,OAAA,CAACxD,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDf,OAAO,CAAC4C,QAAQ,IAAI;UAAO;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjC,OAAA,CAACzD,GAAG;QAAC0E,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE7B,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBAC1CnB,OAAA,CAACrD,MAAM;UACL0F,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,UAAU;UAClBiB,SAAS,eAAEnD,OAAA,CAAClB,QAAQ;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAMhC,MAAM,CAACH,OAAO,CAAE;UAC/Ba,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA,CAACrD,MAAM;UACL0F,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,MAAM;UACdiB,SAAS,eAAEnD,OAAA,CAACtB,QAAQ;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAMlC,MAAM,CAACD,OAAO,CAAE;UAC/Ba,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENjC,OAAA,CAACpC,IAAI;QACH6C,QAAQ,EAAEA,QAAS;QACnB4C,IAAI,EAAEC,OAAO,CAAC7C,QAAQ,CAAE;QACxB8C,OAAO,EAAEzC,eAAgB;QAAAK,QAAA,gBAEzBnB,OAAA,CAACzC,QAAQ;UAACgF,OAAO,EAAEA,CAAA,KAAM;YAAElC,MAAM,CAACD,OAAO,CAAC;YAAEU,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAK,QAAA,gBAC/DnB,OAAA,CAACnC,YAAY;YAAAsD,QAAA,eACXnB,OAAA,CAACtB,QAAQ;cAACmE,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfjC,OAAA,CAAClC,YAAY;YAAAqD,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACXjC,OAAA,CAACzC,QAAQ;UAACgF,OAAO,EAAEA,CAAA,KAAM;YAAEhC,MAAM,CAACH,OAAO,CAAC;YAAEU,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAK,QAAA,gBAC/DnB,OAAA,CAACnC,YAAY;YAAAsD,QAAA,eACXnB,OAAA,CAAClB,QAAQ;cAAC+D,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfjC,OAAA,CAAClC,YAAY;YAAAqD,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACXjC,OAAA,CAACzC,QAAQ;UAACgF,OAAO,EAAEA,CAAA,KAAM;YAAEjC,QAAQ,CAACF,OAAO,CAAC;YAAEU,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAK,QAAA,gBACjEnB,OAAA,CAACnC,YAAY;YAAAsD,QAAA,eACXnB,OAAA,CAACpB,UAAU;cAACiE,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACfjC,OAAA,CAAClC,YAAY;YAAAqD,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX;AAACzB,EAAA,CA1JQL,WAAW;AAAAqD,EAAA,GAAXrD,WAAW;AA4JpB,SAASsD,QAAQA,CAAA,EAAG;EAAAC,GAAA;EAClB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtH,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuH,cAAc,EAAEC,iBAAiB,CAAC,GAAGxH,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyH,eAAe,EAAEC,kBAAkB,CAAC,GAAG1H,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2H,QAAQ,EAAEC,WAAW,CAAC,GAAG5H,QAAQ,CAAC;IACvCqF,IAAI,EAAE,EAAE;IACRwC,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,WAAW;IAC9BvB,QAAQ,EAAE,KAAK;IACfE,QAAQ,EAAE,OAAO;IACjBsB,aAAa,EAAE,KAAK;IACpBvB,UAAU,EAAE,UAAU;IACtBwB,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGhF,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEiF;EAAY,CAAC,GAAG9E,MAAM,CAAC,CAAC;;EAEhC;EACA,MAAM;IAAE+E,IAAI,EAAEC,YAAY;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGxF,QAAQ,CAChE,UAAU,EACV,YAAY;IACV,IAAI;MACFyF,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,MAAMC,QAAQ,GAAG,MAAMvF,WAAW,CAACwF,MAAM,CAAC,CAAC;MAC3CH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,QAAQ,CAAC;MAC3CF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,QAAQ,CAACpE,MAAM,CAAC;MAChDkE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAACE,OAAO,CAAC;MAClDJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,QAAQ,CAACP,IAAI,CAAC;MAC5CK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,OAAOC,QAAQ,CAACP,IAAI,CAAC;MACxDK,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACP,IAAI,CAAC,CAAC;MAEpE,IAAIU,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACP,IAAI,CAAC,EAAE;QAChCK,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,QAAQ,CAACP,IAAI,CAACY,MAAM,CAAC;QAC1D,IAAIL,QAAQ,CAACP,IAAI,CAACY,MAAM,GAAG,CAAC,EAAE;UAC5BP,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,QAAQ,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9C;MACF;MAEA,OAAOO,QAAQ,CAACP,IAAI;IACtB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCE,OAAO,CAACF,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACI,QAAQ,CAAC;MAChD,MAAMJ,KAAK;IACb;EACF,CAAC,EACD;IACEU,oBAAoB,EAAE,KAAK;IAC3BC,SAAS,EAAE,CAAC;IAAE;IACdC,SAAS,EAAE,CAAC;IAAE;IACdC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAGjB,IAAI,IAAK;MACnBK,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEN,IAAI,CAAC;MACrED,WAAW,CAACC,IAAI,IAAI,EAAE,CAAC;IACzB,CAAC;IACDkB,OAAO,EAAGf,KAAK,IAAK;MAClBE,OAAO,CAACF,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEpF,KAAK,CAACoF,KAAK,CAAC,4BAA4BA,KAAK,CAACgB,OAAO,EAAE,CAAC;IAC1D;EACF,CACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGvG,WAAW,CAACG,WAAW,CAACqG,MAAM,EAAE;IACrDJ,SAAS,EAAEA,CAAA,KAAM;MACfnB,WAAW,CAACwB,iBAAiB,CAAC,UAAU,CAAC;MACzCvC,mBAAmB,CAAC,KAAK,CAAC;MAC1BwC,SAAS,CAAC,CAAC;MACXxG,KAAK,CAACyG,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDN,OAAO,EAAGf,KAAK,IAAK;MAAA,IAAAsB,eAAA,EAAAC,oBAAA;MAClBrB,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMwB,YAAY,GAAG,CAAAxB,KAAK,aAALA,KAAK,wBAAAsB,eAAA,GAALtB,KAAK,CAAEI,QAAQ,cAAAkB,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAiBzB,IAAI,cAAA0B,oBAAA,uBAArBA,oBAAA,CAAuBE,MAAM,MAC9BzB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,OAAO,KACd,0BAA0B;MAC9CpG,KAAK,CAACoF,KAAK,CAACwB,YAAY,CAAC;IAC3B;EACF,CAAC,CAAC;;EAEF;EACA,MAAME,cAAc,GAAGhH,WAAW,CAChC,CAAC;IAAEiH,EAAE;IAAE9B;EAAK,CAAC,KAAKhF,WAAW,CAAC+G,MAAM,CAACD,EAAE,EAAE9B,IAAI,CAAC,EAC9C;IACEiB,SAAS,EAAEA,CAAA,KAAM;MACfnB,WAAW,CAACwB,iBAAiB,CAAC,UAAU,CAAC;MACzCrC,iBAAiB,CAAC,KAAK,CAAC;MACxBsC,SAAS,CAAC,CAAC;MACXxG,KAAK,CAACyG,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDN,OAAO,EAAGf,KAAK,IAAK;MAAA,IAAA6B,gBAAA,EAAAC,qBAAA;MAClB5B,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMwB,YAAY,GAAG,CAAAxB,KAAK,aAALA,KAAK,wBAAA6B,gBAAA,GAAL7B,KAAK,CAAEI,QAAQ,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBhC,IAAI,cAAAiC,qBAAA,uBAArBA,qBAAA,CAAuBL,MAAM,MAC9BzB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,OAAO,KACd,0BAA0B;MAC9CpG,KAAK,CAACoF,KAAK,CAACwB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;;EAED;EACA,MAAMO,cAAc,GAAGrH,WAAW,CAACG,WAAW,CAACmH,MAAM,EAAE;IACrDlB,SAAS,EAAEA,CAAA,KAAM;MACfnB,WAAW,CAACwB,iBAAiB,CAAC,UAAU,CAAC;MACzCvG,KAAK,CAACyG,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDN,OAAO,EAAGf,KAAK,IAAK;MAClBpF,KAAK,CAACoF,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,CAAC;;EAEF;EACA,MAAMiC,YAAY,GAAGvH,WAAW,CAC7BwH,SAAS,IAAKrH,WAAW,CAACsH,WAAW,CAACD,SAAS,CAAC,EACjD;IACEpB,SAAS,EAAGV,QAAQ,IAAK;MACvB,MAAMgC,MAAM,GAAGhC,QAAQ,CAACP,IAAI;MAC5B,IAAIuC,MAAM,CAACpG,MAAM,KAAK,SAAS,EAAE;QAC/BpB,KAAK,CAACyG,OAAO,CAAC,qCAAqC,CAAC;MACtD,CAAC,MAAM;QACLzG,KAAK,CAACyH,OAAO,CAAC,iBAAiBD,MAAM,CAACpB,OAAO,EAAE,CAAC;MAClD;IACF,CAAC;IACDD,OAAO,EAAGf,KAAK,IAAK;MAAA,IAAAsC,gBAAA,EAAAC,qBAAA;MAClBrC,OAAO,CAACF,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMwB,YAAY,GAAG,CAAAxB,KAAK,aAALA,KAAK,wBAAAsC,gBAAA,GAALtC,KAAK,CAAEI,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBzC,IAAI,cAAA0C,qBAAA,uBAArBA,qBAAA,CAAuBd,MAAM,MAC9BzB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,OAAO,KACd,qBAAqB;MACzCpG,KAAK,CAACoF,KAAK,CAACwB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;;EAED;EACA,MAAMgB,iBAAiB,GAAG9H,WAAW,CAClCwH,SAAS,IAAKrH,WAAW,CAAC4H,SAAS,CAACP,SAAS,CAAC,EAC/C;IACEpB,SAAS,EAAGV,QAAQ,IAAK;MACvB,MAAMgC,MAAM,GAAGhC,QAAQ,CAACP,IAAI;MAC5B,IAAIuC,MAAM,CAACpG,MAAM,KAAK,SAAS,EAAE;QAC/BpB,KAAK,CAACyG,OAAO,CAAC,yCAAyCe,MAAM,CAACM,aAAa,GAAG,CAAC;MACjF,CAAC,MAAM,IAAIN,MAAM,CAACpG,MAAM,KAAK,UAAU,EAAE;QACvCpB,KAAK,CAAC+H,IAAI,CAAC,sCAAsC,CAAC;MACpD,CAAC,MAAM;QACL/H,KAAK,CAACoF,KAAK,CAAC,sBAAsBoC,MAAM,CAACpB,OAAO,EAAE,CAAC;MACrD;IACF,CAAC;IACDD,OAAO,EAAGf,KAAK,IAAK;MAAA,IAAA4C,gBAAA,EAAAC,qBAAA;MAClB3C,OAAO,CAACF,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMwB,YAAY,GAAG,CAAAxB,KAAK,aAALA,KAAK,wBAAA4C,gBAAA,GAAL5C,KAAK,CAAEI,QAAQ,cAAAwC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB/C,IAAI,cAAAgD,qBAAA,uBAArBA,qBAAA,CAAuBpB,MAAM,MAC9BzB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,OAAO,KACd,mBAAmB;MACvCpG,KAAK,CAACoF,KAAK,CAACwB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;;EAED;EACA,MAAMsB,YAAY,GAAG,CACnB;IACEnB,EAAE,EAAE,QAAQ;IACZhF,IAAI,EAAE,gBAAgB;IACtBX,MAAM,EAAE,QAAQ;IAChBoD,UAAU,EAAE,oBAAoB;IAChCtB,QAAQ,EAAE,KAAK;IACfE,QAAQ,EAAE,OAAO;IACjBD,UAAU,EAAE,MAAM;IAClBwB,UAAU,EAAE,mBAAmB;IAC/BC,UAAU,EAAE,IAAI;IAChBuD,UAAU,EAAE,qBAAqB;IACjCC,UAAU,EAAE;EACd,CAAC,EACD;IACErB,EAAE,EAAE,QAAQ;IACZhF,IAAI,EAAE,gBAAgB;IACtBX,MAAM,EAAE,UAAU;IAClBoD,UAAU,EAAE,IAAI;IAChBtB,QAAQ,EAAE,kBAAkB;IAC5BE,QAAQ,EAAE,OAAO;IACjBD,UAAU,EAAE,UAAU;IACtBwB,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAChBuD,UAAU,EAAE,qBAAqB;IACjCC,UAAU,EAAE;EACd,CAAC,CACF;EAED,MAAMC,QAAQ,GAAG1C,KAAK,CAACC,OAAO,CAACV,YAAY,CAAC,GAAGA,YAAY,GAAG,EAAE;;EAEhE;EACAI,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EACrCD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEL,YAAY,CAAC;EAC1CI,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE8C,QAAQ,CAAC;EAClC/C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE8C,QAAQ,CAACxC,MAAM,CAAC;EAChDP,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEJ,SAAS,CAAC;EACpCG,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEH,KAAK,CAAC;EAC5BE,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EAErC,MAAMiB,SAAS,GAAGA,CAAA,KAAM;IACtBlC,WAAW,CAAC;MACVvC,IAAI,EAAE,EAAE;MACRwC,YAAY,EAAE,QAAQ;MACtBC,UAAU,EAAE,EAAE;MACdC,iBAAiB,EAAE,WAAW;MAC9BvB,QAAQ,EAAE,KAAK;MACfE,QAAQ,EAAE,OAAO;MACjBsB,aAAa,EAAE,KAAK;MACpBvB,UAAU,EAAE,UAAU;MACtBwB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFV,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMkE,YAAY,GAAGA,CAAA,KAAM;IACzBtE,mBAAmB,CAAC,IAAI,CAAC;IACzBwC,SAAS,CAAC,CAAC;EACb,CAAC;EAED,MAAM+B,UAAU,GAAI/H,OAAO,IAAK;IAC9B4D,kBAAkB,CAAC5D,OAAO,CAAC;IAE3B8D,WAAW,CAAC;MACVvC,IAAI,EAAEvB,OAAO,CAACuB,IAAI,IAAI,EAAE;MACxBwC,YAAY,EAAE,QAAQ;MAAE;MACxBC,UAAU,EAAEhE,OAAO,CAACgE,UAAU,IAAI,EAAE;MACpCC,iBAAiB,EAAEjE,OAAO,CAACiE,iBAAiB,IAAI,WAAW;MAC3DvB,QAAQ,EAAE1C,OAAO,CAAC0C,QAAQ,IAAI,KAAK;MACnCE,QAAQ,EAAE5C,OAAO,CAAC4C,QAAQ,IAAI,OAAO;MACrCsB,aAAa,EAAElE,OAAO,CAAC2C,UAAU,IAAI3C,OAAO,CAAC2C,UAAU,KAAK,UAAU;MACtEA,UAAU,EAAE3C,OAAO,CAAC2C,UAAU,IAAI,UAAU;MAC5CwB,UAAU,EAAEnE,OAAO,CAACmE,UAAU,IAAI,EAAE;MACpCC,UAAU,EAAEpE,OAAO,CAACoE,UAAU,GAAGpE,OAAO,CAACoE,UAAU,CAAC4D,QAAQ,CAAC,CAAC,GAAG,EAAE;MACnE3D,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE,CAAE;IACtB,CAAC,CAAC;IACFZ,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMuE,YAAY,GAAIjI,OAAO,IAAK;IAChC,IAAIkI,MAAM,CAACC,OAAO,CAAC,4CAA4CnI,OAAO,CAACuB,IAAI,IAAI,CAAC,EAAE;MAChFoF,cAAc,CAACyB,MAAM,CAACpI,OAAO,CAACuG,EAAE,CAAC;IACnC;EACF,CAAC;EAED,MAAM8B,UAAU,GAAIrI,OAAO,IAAK;IAC9B;IACA,IAAIA,OAAO,CAAC2C,UAAU,IAAI3C,OAAO,CAAC2C,UAAU,KAAK,UAAU,EAAE;MAC3DyE,iBAAiB,CAACgB,MAAM,CAACpI,OAAO,CAACuG,EAAE,CAAC;IACtC,CAAC,MAAM;MACLM,YAAY,CAACuB,MAAM,CAACpI,OAAO,CAACuG,EAAE,CAAC;IACjC;EACF,CAAC;EAED,MAAM+B,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAMC,UAAU,GAAG;MAAE,GAAG1E;IAAS,CAAC;;IAElC;IACA,IAAI0E,UAAU,CAACpE,UAAU,KAAK,EAAE,EAAEoE,UAAU,CAACpE,UAAU,GAAG,IAAI;IAC9D,IAAIoE,UAAU,CAACnE,UAAU,KAAK,EAAE,EAAEmE,UAAU,CAACnE,UAAU,GAAG,IAAI;IAC9D,IAAImE,UAAU,CAAClE,cAAc,KAAK,EAAE,EAAEkE,UAAU,CAAClE,cAAc,GAAG,IAAI;IACtE,IAAIkE,UAAU,CAACjE,cAAc,KAAK,EAAE,EAAEiE,UAAU,CAACjE,cAAc,GAAG,IAAI;IACtE,IAAIiE,UAAU,CAACvE,UAAU,KAAK,EAAE,EAAEuE,UAAU,CAACvE,UAAU,GAAG,IAAI;;IAE9D;IACA,IAAIuE,UAAU,CAACnE,UAAU,EAAE;MACzB,MAAMoE,IAAI,GAAGC,QAAQ,CAACF,UAAU,CAACnE,UAAU,CAAC;MAC5C,IAAIsE,KAAK,CAACF,IAAI,CAAC,EAAE;QACfhJ,KAAK,CAACoF,KAAK,CAAC,mCAAmC,CAAC;QAChD;MACF;MACA2D,UAAU,CAACnE,UAAU,GAAGoE,IAAI;IAC9B;;IAEA;IACA,IAAI3E,QAAQ,CAAClB,UAAU,KAAK,UAAU,EAAE;MACtC,IAAI,CAACkB,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,EAAE;QAChD5E,KAAK,CAACoF,KAAK,CAAC,wDAAwD,CAAC;QACrE;MACF;MAEA,MAAM4D,IAAI,GAAGC,QAAQ,CAAC5E,QAAQ,CAACO,UAAU,CAAC;MAC1C,IAAIsE,KAAK,CAACF,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,KAAK,EAAE;QAC3ChJ,KAAK,CAACoF,KAAK,CAAC,uDAAuD,CAAC;QACpE;MACF;IACF;;IAEA;IACA,MAAM+D,SAAS,GAAG;MAChBpH,IAAI,EAAEgH,UAAU,CAAChH,IAAI;MACrByC,UAAU,EAAEuE,UAAU,CAACvE,UAAU;MACjC4E,cAAc,EAAE;QACd7E,YAAY,EAAEwE,UAAU,CAACxE,YAAY,IAAI,QAAQ;QACjDE,iBAAiB,EAAEsE,UAAU,CAACtE,iBAAiB,IAAI,WAAW;QAC9DvB,QAAQ,EAAE6F,UAAU,CAAC7F,QAAQ,IAAI,KAAK;QACtCE,QAAQ,EAAE2F,UAAU,CAAC3F,QAAQ,IAAI;MACnC,CAAC;MACDiG,YAAY,EAAEN,UAAU,CAAC5F,UAAU,KAAK,UAAU,GAAG;QACnDA,UAAU,EAAE4F,UAAU,CAAC5F,UAAU;QACjCmG,IAAI,EAAEP,UAAU,CAACpE,UAAU;QAC3BqE,IAAI,EAAED,UAAU,CAACnE,UAAU;QAC3B2E,QAAQ,EAAER,UAAU,CAAClE,cAAc;QACnC2E,QAAQ,EAAET,UAAU,CAACjE;MACvB,CAAC,GAAG;IACN,CAAC;IAED,IAAIX,eAAe,EAAE;MACnB2C,cAAc,CAAC8B,MAAM,CAAC;QAAE7B,EAAE,EAAE5C,eAAe,CAAC4C,EAAE;QAAE9B,IAAI,EAAEkE;MAAU,CAAC,CAAC;IACpE,CAAC,MAAM;MACL9C,cAAc,CAACuC,MAAM,CAACO,SAAS,CAAC;IAClC;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzCrF,WAAW,CAACsF,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAI5E,IAAI,IAAK;IACjC,MAAM6E,SAAS,GAAG;MAAE,GAAG7E;IAAK,CAAC;;IAE7B;IACA8E,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;MACpC,IAAIJ,SAAS,CAACI,GAAG,CAAC,KAAK,EAAE,EAAE;QACzBJ,SAAS,CAACI,GAAG,CAAC,GAAG,IAAI;MACvB;IACF,CAAC,CAAC;;IAEF;IACA,IAAIJ,SAAS,CAAClF,UAAU,IAAI,OAAOkF,SAAS,CAAClF,UAAU,KAAK,QAAQ,EAAE;MACpE,MAAMoE,IAAI,GAAGC,QAAQ,CAACa,SAAS,CAAClF,UAAU,CAAC;MAC3CkF,SAAS,CAAClF,UAAU,GAAGsE,KAAK,CAACF,IAAI,CAAC,GAAG,IAAI,GAAGA,IAAI;IAClD;IAEA,OAAOc,SAAS;EAClB,CAAC;EAED,IAAI1E,KAAK,EAAE;IACT,oBACEhF,OAAA,CAACzD,GAAG;MAACwN,SAAS,EAAC,SAAS;MAAA5I,QAAA,eACtBnB,OAAA,CAACjC,KAAK;QAACiM,QAAQ,EAAC,OAAO;QAAC/I,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAEvC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACEjC,OAAA,CAACzD,GAAG;IAACwN,SAAS,EAAC,SAAS;IAAA5I,QAAA,gBACtBnB,OAAA,CAACzD,GAAG;MAAC0E,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFnB,OAAA,CAACxD,UAAU;QAAC0F,OAAO,EAAC,IAAI;QAACjB,EAAE,EAAE;UAAEkB,UAAU,EAAE;QAAI,CAAE;QAAAhB,QAAA,EAAC;MAElD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjC,OAAA,CAACzD,GAAG;QAAC0E,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBACnCnB,OAAA,CAACrD,MAAM;UACLuF,OAAO,EAAC,UAAU;UAClBiB,SAAS,eAAEnD,OAAA,CAAChB,WAAW;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BM,OAAO,EAAEA,CAAA,KAAM;YACb2C,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;YACrCR,WAAW,CAACwB,iBAAiB,CAAC,UAAU,CAAC;YACzClB,OAAO,CAAC,CAAC;UACX,CAAE;UACFhE,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA,CAACrD,MAAM;UACLuF,OAAO,EAAC,UAAU;UAClBI,KAAK,EAAC,WAAW;UACjBC,OAAO,EAAE,MAAAA,CAAA,KAAY;YACnB2C,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACtC,IAAI;cACF,MAAMC,QAAQ,GAAG,MAAM6E,KAAK,CAAC,oCAAoC,CAAC;cAClE/E,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAAC;cAC/C,MAAMP,IAAI,GAAG,MAAMO,QAAQ,CAAC8E,IAAI,CAAC,CAAC;cAClChF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEN,IAAI,CAAC;cACvCjF,KAAK,CAACyG,OAAO,CAAC,oBAAoBxB,IAAI,CAACY,MAAM,iBAAiB,CAAC;YACjE,CAAC,CAAC,OAAOT,KAAK,EAAE;cACdE,OAAO,CAACF,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;cAC3CpF,KAAK,CAACoF,KAAK,CAAC,2BAA2BA,KAAK,CAACgB,OAAO,EAAE,CAAC;YACzD;UACF,CAAE;UACF/E,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA,CAACrD,MAAM;UACLuF,OAAO,EAAC,UAAU;UAClBI,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAM;YACb2C,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7CR,WAAW,CAACwF,YAAY,CAAC,UAAU,EAAErC,YAAY,CAAC;YAClDlI,KAAK,CAAC+H,IAAI,CAAC,kCAAkC,CAAC;UAChD,CAAE;UACF1G,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA,CAACrD,MAAM;UACLuF,OAAO,EAAC,WAAW;UACnBiB,SAAS,eAAEnD,OAAA,CAAC5B,OAAO;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAE2F,YAAa;UACtBjH,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL8C,SAAS,iBAAI/E,OAAA,CAAChC,cAAc;MAACiD,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE;IAAE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG/CjC,OAAA,CAACzD,GAAG;MAAC0E,EAAE,EAAE;QAAEM,EAAE,EAAE,CAAC;QAAE6I,CAAC,EAAE,CAAC;QAAE5I,OAAO,EAAE,YAAY;QAAE6I,YAAY,EAAE;MAAE,CAAE;MAAAlJ,QAAA,gBAC/DnB,OAAA,CAACxD,UAAU;QAAC0F,OAAO,EAAC,IAAI;QAACjB,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAAW;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAChEjC,OAAA,CAACxD,UAAU;QAAC0F,OAAO,EAAC,OAAO;QAAAf,QAAA,GAAC,4BACL,EAAC,OAAO2D,YAAY,eAAC9E,OAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,kCACtB,EAACsD,KAAK,CAACC,OAAO,CAACV,YAAY,CAAC,CAACsD,QAAQ,CAAC,CAAC,eAACpI,OAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,gCAC/C,EAACsD,KAAK,CAACC,OAAO,CAACV,YAAY,CAAC,GAAGA,YAAY,CAACW,MAAM,GAAG,KAAK,eAACzF,OAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,4BACpE,EAACgG,QAAQ,CAACxC,MAAM,eAACzF,OAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,sBAC5B,EAAC8C,SAAS,CAACqD,QAAQ,CAAC,CAAC,eAACpI,OAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,kBAC/B,EAAC+C,KAAK,GAAGA,KAAK,CAACgB,OAAO,GAAG,MAAM,eAAChG,OAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EACZ6C,YAAY,iBACX9E,OAAA,CAACzD,GAAG;QAAC0E,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE,CAAE;QAAA9B,QAAA,gBACjBnB,OAAA,CAACxD,UAAU;UAAC0F,OAAO,EAAC,OAAO;UAACjB,EAAE,EAAE;YAAEkB,UAAU,EAAE;UAAO,CAAE;UAAAhB,QAAA,EAAC;QAAS;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC9EjC,OAAA;UAAKsK,KAAK,EAAE;YAAEzH,QAAQ,EAAE,MAAM;YAAE0H,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAArJ,QAAA,EACpEsJ,IAAI,CAACC,SAAS,CAAC5F,YAAY,EAAE,IAAI,EAAE,CAAC;QAAC;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAILgG,QAAQ,CAACxC,MAAM,KAAK,CAAC,IAAI,CAACV,SAAS,gBAClC/E,OAAA,CAACvD,IAAI;MAAA0E,QAAA,eACHnB,OAAA,CAACtD,WAAW;QAACuE,EAAE,EAAE;UAAE0J,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAzJ,QAAA,gBAC9CnB,OAAA,CAAC1B,UAAU;UAAC2C,EAAE,EAAE;YAAE4B,QAAQ,EAAE,EAAE;YAAEP,KAAK,EAAE,gBAAgB;YAAEf,EAAE,EAAE;UAAE;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpEjC,OAAA,CAACxD,UAAU;UAAC0F,OAAO,EAAC,IAAI;UAACjB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAExC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjC,OAAA,CAACxD,UAAU;UAAC0F,OAAO,EAAC,OAAO;UAACI,KAAK,EAAC,gBAAgB;UAACrB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjC,OAAA,CAACrD,MAAM;UACLuF,OAAO,EAAC,WAAW;UACnBiB,SAAS,eAAEnD,OAAA,CAAC5B,OAAO;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAE2F,YAAa;UACtBjH,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEPjC,OAAA,CAACpD,IAAI;MAAC6F,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvB,QAAA,EACxB8G,QAAQ,CAAC4C,GAAG,CAAEzK,OAAO,iBACpBJ,OAAA,CAACpD,IAAI;QAAC+F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACkI,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5J,QAAA,eAC9BnB,OAAA,CAACG,WAAW;UACVC,OAAO,EAAEA,OAAQ;UACjBC,MAAM,EAAE8H,UAAW;UACnB7H,QAAQ,EAAE+H,YAAa;UACvB9H,MAAM,EAAEkI;QAAW;UAAA3G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC,GANkC7B,OAAO,CAACuG,EAAE;QAAA7E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAO1C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDjC,OAAA,CAACjD,MAAM;MACLsG,IAAI,EAAEM,gBAAgB,IAAIE,cAAe;MACzCN,OAAO,EAAEA,CAAA,KAAM;QACbK,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,iBAAiB,CAAC,KAAK,CAAC;QACxBsC,SAAS,CAAC,CAAC;MACb,CAAE;MACF4E,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAA9J,QAAA,gBAETnB,OAAA,CAAChD,WAAW;QAAAmE,QAAA,EACT4C,eAAe,GAAG,cAAc,GAAG;MAAoB;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACdjC,OAAA,CAAC/C,aAAa;QAAAkE,QAAA,eACZnB,OAAA,CAACzD,GAAG;UAAC0E,EAAE,EAAE;YAAEiK,EAAE,EAAE;UAAE,CAAE;UAAA/J,QAAA,eACjBnB,OAAA,CAACpD,IAAI;YAAC6F,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAvB,QAAA,gBACzBnB,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBnB,OAAA,CAAC7C,SAAS;gBACR8N,SAAS;gBACT7I,KAAK,EAAC,cAAc;gBACpBmH,KAAK,EAAEtF,QAAQ,CAACtC,IAAK;gBACrBwJ,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,MAAM,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;gBAC1D+B,QAAQ;cAAA;gBAAAxJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkI,EAAE,EAAE,CAAE;cAAA3J,QAAA,eACvBnB,OAAA,CAAC5C,WAAW;gBAAC6N,SAAS;gBAAA9J,QAAA,gBACpBnB,OAAA,CAAC3C,UAAU;kBAAA8D,QAAA,EAAC;gBAAY;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCjC,OAAA,CAAC1C,MAAM;kBACLiM,KAAK,EAAEtF,QAAQ,CAACE,YAAa;kBAC7BgH,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,cAAc,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBAClEnH,KAAK,EAAC,cAAc;kBAAAjB,QAAA,gBAEpBnB,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,QAAQ;oBAAApI,QAAA,EAAC;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,SAAS;oBAAApI,QAAA,EAAC;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5CjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,MAAM;oBAAApI,QAAA,EAAC;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkI,EAAE,EAAE,CAAE;cAAA3J,QAAA,eACvBnB,OAAA,CAAC5C,WAAW;gBAAC6N,SAAS;gBAAA9J,QAAA,gBACpBnB,OAAA,CAAC3C,UAAU;kBAAA8D,QAAA,EAAC;gBAAiB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CjC,OAAA,CAAC1C,MAAM;kBACLiM,KAAK,EAAEtF,QAAQ,CAACI,iBAAkB;kBAClC8G,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,mBAAmB,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBACvEnH,KAAK,EAAC,mBAAmB;kBAAAjB,QAAA,gBAEzBnB,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,WAAW;oBAAApI,QAAA,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,UAAU;oBAAApI,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,UAAU;oBAAApI,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,UAAU;oBAAApI,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkI,EAAE,EAAE,CAAE;cAAA3J,QAAA,eACvBnB,OAAA,CAAC5C,WAAW;gBAAC6N,SAAS;gBAAA9J,QAAA,gBACpBnB,OAAA,CAAC3C,UAAU;kBAAA8D,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCjC,OAAA,CAAC1C,MAAM;kBACLiM,KAAK,EAAEtF,QAAQ,CAACnB,QAAS;kBACzBqI,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,UAAU,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBAC9DnH,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhBnB,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,KAAK;oBAAApI,QAAA,EAAC;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpCjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,kBAAkB;oBAAApI,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9DjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,kBAAkB;oBAAApI,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9DjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,eAAe;oBAAApI,QAAA,EAAC;kBAAa;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkI,EAAE,EAAE,CAAE;cAAA3J,QAAA,eACvBnB,OAAA,CAAC5C,WAAW;gBAAC6N,SAAS;gBAAA9J,QAAA,gBACpBnB,OAAA,CAAC3C,UAAU;kBAAA8D,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCjC,OAAA,CAAC1C,MAAM;kBACLiM,KAAK,EAAEtF,QAAQ,CAACjB,QAAS;kBACzBmI,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,UAAU,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBAC9DnH,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhBnB,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,OAAO;oBAAApI,QAAA,EAAC;kBAAY;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC/CjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,OAAO;oBAAApI,QAAA,EAAC;kBAAU;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC7CjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,OAAO;oBAAApI,QAAA,EAAC;kBAAoB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACvDjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,OAAO;oBAAApI,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBnB,OAAA,CAAC7C,SAAS;gBACR8N,SAAS;gBACT7I,KAAK,EAAC,uBAAuB;gBAC7BmH,KAAK,EAAEtF,QAAQ,CAACG,UAAW;gBAC3B+G,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,YAAY,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;gBAChEgC,WAAW,EAAC;cAAsC;gBAAAzJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBnB,OAAA,CAACxD,UAAU;gBAAC0F,OAAO,EAAC,IAAI;gBAACjB,EAAE,EAAE;kBAAEM,EAAE,EAAE,CAAC;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAAC;cAEzD;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkI,EAAE,EAAE,CAAE;cAAA3J,QAAA,eACvBnB,OAAA,CAAC5C,WAAW;gBAAC6N,SAAS;gBAAA9J,QAAA,gBACpBnB,OAAA,CAAC3C,UAAU;kBAAA8D,QAAA,EAAC;gBAAU;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCjC,OAAA,CAAC1C,MAAM;kBACLiM,KAAK,EAAEtF,QAAQ,CAAClB,UAAW;kBAC3BoI,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMI,SAAS,GAAGJ,CAAC,CAACC,MAAM,CAAC9B,KAAK;oBAChCF,gBAAgB,CAAC,YAAY,EAAEmC,SAAS,CAAC;oBACzCnC,gBAAgB,CAAC,eAAe,EAAEmC,SAAS,KAAK,UAAU,CAAC;kBAC7D,CAAE;kBACFpJ,KAAK,EAAC,YAAY;kBAAAjB,QAAA,gBAElBnB,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,UAAU;oBAAApI,QAAA,eACxBnB,OAAA,CAACzD,GAAG;sBAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAwB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjEjC,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,MAAM;oBAAApI,QAAA,eACpBnB,OAAA,CAACzD,GAAG;sBAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC7CjC,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,OAAO;oBAAApI,QAAA,eACrBnB,OAAA,CAACzD,GAAG;sBAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAK;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC9CjC,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,QAAQ;oBAAApI,QAAA,eACtBnB,OAAA,CAACzD,GAAG;sBAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAM;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC/CjC,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXjC,OAAA,CAACzC,QAAQ;oBAACgM,KAAK,EAAC,KAAK;oBAAApI,QAAA,eACnBnB,OAAA,CAACzD,GAAG;sBAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAG;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC5CjC,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACTjC,OAAA,CAAC9B,cAAc;kBAAAiD,QAAA,GACZ8C,QAAQ,CAAClB,UAAU,KAAK,UAAU,IAAI,yBAAyB,EAC/DkB,QAAQ,CAAClB,UAAU,KAAK,MAAM,IAAI,iCAAiC,EACnEkB,QAAQ,CAAClB,UAAU,KAAK,OAAO,IAAI,sCAAsC,EACzEkB,QAAQ,CAAClB,UAAU,KAAK,QAAQ,IAAI,wCAAwC,EAC5EkB,QAAQ,CAAClB,UAAU,KAAK,KAAK,IAAI,4CAA4C;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAENgC,QAAQ,CAAClB,UAAU,KAAK,UAAU,iBACjC/C,OAAA,CAAAE,SAAA;cAAAiB,QAAA,gBACEnB,OAAA,CAACpD,IAAI;gBAAC+F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAA3J,QAAA,eACvBnB,OAAA,CAAC7C,SAAS;kBACR8N,SAAS;kBACT7I,KAAK,EAAC,YAAY;kBAClBmH,KAAK,EAAEtF,QAAQ,CAACM,UAAW;kBAC3B4G,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,YAAY,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBAChE+B,QAAQ;kBACRC,WAAW,EACTtH,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC,0CACL;kBACD0I,UAAU,EAAC;gBAA+C;kBAAA3J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPjC,OAAA,CAACpD,IAAI;gBAAC+F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAA3J,QAAA,eACvBnB,OAAA,CAAC7C,SAAS;kBACR8N,SAAS;kBACT7I,KAAK,EAAC,YAAY;kBAClBmH,KAAK,EAAEtF,QAAQ,CAACO,UAAW;kBAC3B2G,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,YAAY,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBAChEmC,IAAI,EAAC,QAAQ;kBACbJ,QAAQ;kBACRC,WAAW,EACTtH,QAAQ,CAAClB,UAAU,KAAK,MAAM,GAAG,kBAAkB,GACnDkB,QAAQ,CAAClB,UAAU,KAAK,OAAO,GAAG,iBAAiB,GACnDkB,QAAQ,CAAClB,UAAU,KAAK,QAAQ,GAAG,kBAAkB,GACrDkB,QAAQ,CAAClB,UAAU,KAAK,KAAK,GAAG,UAAU,GAAG,aAC9C;kBACD0I,UAAU,EAAE,UAAUxH,QAAQ,CAAClB,UAAU,CAAClB,WAAW,CAAC,CAAC,QAAS;kBAChE8J,UAAU,EAAE;oBAAEC,GAAG,EAAE,CAAC;oBAAEC,GAAG,EAAE;kBAAM;gBAAE;kBAAA/J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPjC,OAAA,CAACpD,IAAI;gBAAC+F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAA3J,QAAA,eACvBnB,OAAA,CAAC7C,SAAS;kBACR8N,SAAS;kBACT7I,KAAK,EAAE6B,QAAQ,CAAClB,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEwG,KAAK,EAAEtF,QAAQ,CAACQ,cAAe;kBAC/B0G,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,gBAAgB,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBACpEgC,WAAW,EACTtH,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,iCAAiC,GACjC,2CACL;kBACD0I,UAAU,EACRxH,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,8BAA8B,GAC9B;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPjC,OAAA,CAACpD,IAAI;gBAAC+F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAA3J,QAAA,eACvBnB,OAAA,CAAC7C,SAAS;kBACR8N,SAAS;kBACT7I,KAAK,EAAE6B,QAAQ,CAAClB,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEwG,KAAK,EAAEtF,QAAQ,CAACS,cAAe;kBAC/ByG,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,gBAAgB,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBACpEmC,IAAI,EAAC,UAAU;kBACfH,WAAW,EACTtH,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,gDAAgD,GAChD,2CACL;kBACD0I,UAAU,EACRxH,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPjC,OAAA,CAACpD,IAAI;gBAAC+F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAzB,QAAA,eAChBnB,OAAA,CAACzD,GAAG;kBAAC0E,EAAE,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAE8B,GAAG,EAAE,CAAC;oBAAE5B,UAAU,EAAE;kBAAS,CAAE;kBAAAH,QAAA,gBACzDnB,OAAA,CAACrD,MAAM;oBACLuF,OAAO,EAAC,UAAU;oBAClBG,IAAI,EAAC,OAAO;oBACZE,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAIwB,eAAe,EAAE;wBACnByD,iBAAiB,CAACgB,MAAM,CAACzE,eAAe,CAAC4C,EAAE,CAAC;sBAC9C,CAAC,MAAM;wBACL/G,KAAK,CAAC+H,IAAI,CAAC,iDAAiD,CAAC;sBAC/D;oBACF,CAAE;oBACFmE,QAAQ,EAAE,CAAC7H,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,IAAIgD,iBAAiB,CAACzC,SAAU;oBACtF9D,EAAE,EAAE;sBAAEmC,aAAa,EAAE;oBAAO,CAAE;oBAAAjC,QAAA,EAE7BqG,iBAAiB,CAACzC,SAAS,GAAG,YAAY,GAAG;kBAAuB;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACTjC,OAAA,CAACxD,UAAU;oBAAC0F,OAAO,EAAC,SAAS;oBAACI,KAAK,EAAC,gBAAgB;oBAAAnB,QAAA,EAAC;kBAErD;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,eACP,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBjC,OAAA,CAAC9C,aAAa;QAAAiE,QAAA,gBACZnB,OAAA,CAACrD,MAAM;UACL4F,OAAO,EAAEA,CAAA,KAAM;YACbqB,mBAAmB,CAAC,KAAK,CAAC;YAC1BE,iBAAiB,CAAC,KAAK,CAAC;YACxBsC,SAAS,CAAC,CAAC;UACb,CAAE;UAAAjF,QAAA,EACH;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA,CAACrD,MAAM;UACLuF,OAAO,EAAC,WAAW;UACnBK,OAAO,EAAEmG,YAAa;UACtBoD,QAAQ,EAAE,CAAC7H,QAAQ,CAACtC,IAAI,IAAIsE,cAAc,CAAClB,SAAS,IAAI2B,cAAc,CAAC3B,SAAU;UAAA5D,QAAA,EAEhF4C,eAAe,GAAG,QAAQ,GAAG;QAAQ;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACyB,GAAA,CA9vBQD,QAAQ;EAAA,QAmBK9D,cAAc,EACVG,MAAM,EAG4BL,QAAQ,EA4C3CC,WAAW,EAiBXA,WAAW,EAoBXA,WAAW,EAWbA,WAAW,EAsBNA,WAAW;AAAA;AAAAqM,GAAA,GAzI9BtI,QAAQ;AAgwBjB,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAAuI,GAAA;AAAAC,YAAA,CAAAxI,EAAA;AAAAwI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}