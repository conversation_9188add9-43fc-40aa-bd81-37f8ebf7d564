"""
Profile and Proxy models optimized for performance.
"""
import json
from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, Integer, String, Text, Index, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..core.database import Base


class ProxyType(str, Enum):
    """Proxy types supported by the application for antidetect browser."""
    NO_PROXY = "no_proxy"  # Local network connection
    HTTP = "http"          # HTTP proxy
    HTTPS = "https"        # HTTPS proxy
    SOCKS5 = "socks5"      # SOCKS5 proxy
    SSH = "ssh"            # SSH tunnel proxy


class ProfileStatus(str, Enum):
    """Profile status for tracking."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    ERROR = "error"


class Profile(Base):
    """
    Browser profile model with optimized indexing.
    """
    __tablename__ = "profiles"
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # Basic info
    name: Mapped[str] = mapped_column(String(255), unique=True, index=True)
    status: Mapped[ProfileStatus] = mapped_column(
        String(20), 
        default=ProfileStatus.ACTIVE,
        index=True  # Index for filtering by status
    )
    
    # Browser configuration (stored as JSON for flexibility)
    browser_config: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    user_agent: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Profile directory path
    profile_path: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Timestamps with indexes for performance
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        index=True
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        index=True
    )
    last_used_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime, 
        nullable=True,
        index=True  # For finding recently used profiles
    )
    
    # Usage statistics
    login_count: Mapped[int] = mapped_column(Integer, default=0)
    message_sent_count: Mapped[int] = mapped_column(Integer, default=0)
    
    # Relationships
    proxy_config: Mapped[Optional["ProxyConfig"]] = relationship(
        "ProxyConfig",
        back_populates="profile",
        uselist=False,
        cascade="all, delete-orphan"
    )
    message_campaigns = relationship("MessageCampaign", back_populates="profile")
    
    def get_browser_config(self) -> dict:
        """Get browser configuration as dictionary."""
        if self.browser_config:
            return json.loads(self.browser_config)
        return {}
    
    def set_browser_config(self, config: dict):
        """Set browser configuration from dictionary."""
        self.browser_config = json.dumps(config)
    
    def __repr__(self):
        return f"<Profile(id={self.id}, name='{self.name}', status='{self.status}')>"


class ProxyConfig(Base):
    """
    Proxy configuration model with encrypted credentials.
    """
    __tablename__ = "proxy_configs"
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # Foreign key to profile
    profile_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("profiles.id"),
        nullable=False,
        index=True  # Index for joins
    )
    
    # Proxy settings
    proxy_type: Mapped[ProxyType] = mapped_column(String(20), default=ProxyType.NO_PROXY)
    host: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    port: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Encrypted credentials
    username: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    password: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Connection testing
    is_working: Mapped[bool] = mapped_column(Boolean, default=True)
    last_tested_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow
    )
    
    # Relationship
    profile: Mapped["Profile"] = relationship("Profile", back_populates="proxy_config")
    
    def get_proxy_url(self) -> Optional[str]:
        """Generate proxy URL for antidetect browser configuration."""
        if self.proxy_type == ProxyType.NO_PROXY:
            return None

        if not self.host or not self.port:
            return None

        auth = ""
        if self.username and self.password:
            auth = f"{self.username}:{self.password}@"

        proxy_type_str = self.proxy_type.value if hasattr(self.proxy_type, 'value') else str(self.proxy_type)
        return f"{proxy_type_str}://{auth}{self.host}:{self.port}"

    def get_browser_proxy_config(self) -> dict:
        """Get proxy configuration formatted for antidetect browser."""
        if self.proxy_type == ProxyType.NO_PROXY:
            return {"mode": "direct"}

        if not self.host or not self.port:
            return {"mode": "direct"}

        config = {
            "mode": "manual_proxy_configuration",
            "proxy_type": self.proxy_type.value,
            "host": self.host,
            "port": self.port,
        }

        if self.username and self.password:
            config.update({
                "username": self.username,
                "password": self.password,
                "auth_required": True
            })
        else:
            config["auth_required"] = False

        return config

    def validate_proxy_config(self) -> tuple[bool, str]:
        """Validate proxy configuration for antidetect browser compatibility."""
        if self.proxy_type == ProxyType.NO_PROXY:
            return True, "No proxy configuration - using direct connection"

        if not self.host:
            return False, "Proxy host is required"

        if not self.port or self.port <= 0 or self.port > 65535:
            return False, "Valid proxy port (1-65535) is required"

        # Check for common proxy ports based on type
        common_ports = {
            ProxyType.HTTP: [8080, 3128, 8888, 80],
            ProxyType.HTTPS: [8080, 3128, 8888, 443],
            ProxyType.SOCKS5: [1080, 1081, 9050],
            ProxyType.SSH: [22, 2222]
        }

        if self.proxy_type in common_ports:
            if self.port not in common_ports[self.proxy_type]:
                return True, f"Warning: Port {self.port} is not commonly used for {self.proxy_type.value} proxies"

        return True, "Proxy configuration is valid"
    
    def __repr__(self):
        return f"<ProxyConfig(id={self.id}, type='{self.proxy_type}', host='{self.host}')>"


# Indexes for performance optimization
Index("idx_profile_status_created", Profile.status, Profile.created_at)
Index("idx_profile_last_used", Profile.last_used_at.desc())
Index("idx_proxy_profile_type", ProxyConfig.profile_id, ProxyConfig.proxy_type)
