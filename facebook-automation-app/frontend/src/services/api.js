import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.code === 'ECONNABORTED') {
      toast.error('Request timeout. Please try again.');
    } else if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          toast.error(data.detail || 'Bad request');
          break;
        case 401:
          toast.error('Unauthorized access');
          // Handle logout if needed
          break;
        case 403:
          toast.error('Access forbidden');
          break;
        case 404:
          toast.error('Resource not found');
          break;
        case 422:
          // Validation errors
          if (data.detail && Array.isArray(data.detail)) {
            data.detail.forEach(err => {
              toast.error(`${err.loc?.join('.')}: ${err.msg}`);
            });
          } else {
            toast.error(data.detail || 'Validation error');
          }
          break;
        case 500:
          toast.error('Internal server error');
          break;
        default:
          toast.error(data.detail || 'An error occurred');
      }
    } else if (error.request) {
      toast.error('Cannot connect to server. Please check your connection.');
    } else {
      toast.error('An unexpected error occurred');
    }
    
    return Promise.reject(error);
  }
);

// API endpoints
export const profilesAPI = {
  getAll: (params = {}) => api.get('/api/profiles', { params }),
  getById: (id) => api.get(`/api/profiles/${id}`),
  create: (data) => api.post('/api/profiles', data),
  update: (id, data) => api.put(`/api/profiles/${id}`, data),
  delete: (id) => api.delete(`/api/profiles/${id}`),
  testProxy: (id) => api.post(`/api/profiles/${id}/test-proxy`),
  testBrowser: (id) => api.post(`/api/profiles/${id}/test-browser`),
  getZendriverConfig: (id) => api.get(`/api/profiles/${id}/zendriver-config`),
  getProxyTypes: () => api.get('/api/profiles/proxy-types'),
  facebookLogin: (id) => api.post(`/api/profiles/${id}/facebook-login`),
  facebookLoginComplete: (id, data = {}) => api.post(`/api/profiles/${id}/facebook-login-complete`, data),
  getFacebookStatus: (id) => api.get(`/api/profiles/${id}/facebook-status`),
  getStats: () => api.get('/api/profiles/stats'),
};

export const scrapingAPI = {
  getSessions: (params = {}) => api.get('/api/scraping/sessions', { params }),
  getSession: (id) => api.get(`/api/scraping/sessions/${id}`),
  createSession: (data) => api.post('/api/scraping/sessions', data),
  updateSession: (id, data) => api.put(`/api/scraping/sessions/${id}`, data),
  deleteSession: (id) => api.delete(`/api/scraping/sessions/${id}`),
  startScraping: (id) => api.post(`/api/scraping/sessions/${id}/start`),
  stopScraping: (id) => api.post(`/api/scraping/sessions/${id}/stop`),
};

export const messagingAPI = {
  getMessages: (params = {}) => api.get('/api/messaging/messages', { params }),
  getMessage: (id) => api.get(`/api/messaging/messages/${id}`),
  createMessage: (data) => api.post('/api/messaging/messages', data),
  sendBulkMessages: (data) => api.post('/api/messaging/send-bulk', data),
  retryMessage: (id) => api.post(`/api/messaging/messages/${id}/retry`),
  getStats: () => api.get('/api/messaging/stats'),
};

export const campaignsAPI = {
  getAll: (params = {}) => api.get('/api/campaigns', { params }),
  getById: (id) => api.get(`/api/campaigns/${id}`),
  create: (data) => api.post('/api/campaigns', data),
  update: (id, data) => api.put(`/api/campaigns/${id}`, data),
  delete: (id) => api.delete(`/api/campaigns/${id}`),
  start: (id) => api.post(`/api/campaigns/${id}/start`),
  pause: (id) => api.post(`/api/campaigns/${id}/pause`),
  getStats: () => api.get('/api/campaigns/stats'),
};

export const analyticsAPI = {
  getDashboardStats: (days = 7) => api.get(`/api/analytics/dashboard?days=${days}`),
  getPerformanceMetrics: (days = 7) => api.get(`/api/analytics/performance?days=${days}`),
  getMessagesChart: (days = 7) => api.get(`/api/analytics/charts/messages?days=${days}`),
  getScrapingChart: (days = 7) => api.get(`/api/analytics/charts/scraping?days=${days}`),
  recordAnalytics: (data) => api.post('/api/analytics/record', data),
};

export const systemAPI = {
  getHealth: () => api.get('/health'),
  getSystemHealth: () => api.get('/api/system/health'),
  getPerformanceStats: () => api.get('/api/system/performance'),
};

// Utility functions
export const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

export const handleApiError = (error, defaultMessage = 'An error occurred') => {
  if (error.response?.data?.detail) {
    return error.response.data.detail;
  }
  return defaultMessage;
};

// Connection test
export const testConnection = async () => {
  try {
    await systemAPI.getHealth();
    return true;
  } catch (error) {
    return false;
  }
};

// Test backend API connection
export const testBackendConnection = async () => {
  try {
    await systemAPI.getSystemHealth();
    return true;
  } catch (error) {
    console.error('Backend connection test failed:', error);
    return false;
  }
};

export default api;
