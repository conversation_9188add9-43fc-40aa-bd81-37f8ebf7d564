"""
Browser service for creating and managing antidetect browser instances with proxy support.
"""
import json
import os
import tempfile
from pathlib import Path
from typing import Dict, Optional, Any
import logging

from ..models.profile import Profile, ProxyConfig, ProxyType

logger = logging.getLogger(__name__)


class BrowserService:
    """Service for managing antidetect browser instances with proxy configuration."""
    
    def __init__(self, profiles_dir: str = None):
        """Initialize browser service with profiles directory."""
        self.profiles_dir = profiles_dir or os.path.join(tempfile.gettempdir(), "browser_profiles")
        Path(self.profiles_dir).mkdir(parents=True, exist_ok=True)
    
    def create_browser_config(self, profile: Profile) -> Dict[str, Any]:
        """Create comprehensive browser configuration for antidetect browser."""
        config = {
            "profile_id": profile.id,
            "profile_name": profile.name,
            "profile_path": self._get_profile_path(profile),
            "browser_settings": self._get_browser_settings(profile),
            "proxy_settings": self._get_proxy_settings(profile),
            "fingerprint_settings": self._get_fingerprint_settings(profile),
            "security_settings": self._get_security_settings(profile)
        }
        
        return config
    
    def _get_profile_path(self, profile: Profile) -> str:
        """Get or create profile directory path."""
        if profile.profile_path and os.path.exists(profile.profile_path):
            return profile.profile_path
            
        profile_dir = os.path.join(self.profiles_dir, f"profile_{profile.id}")
        Path(profile_dir).mkdir(parents=True, exist_ok=True)
        return profile_dir
    
    def _get_browser_settings(self, profile: Profile) -> Dict[str, Any]:
        """Get browser-specific settings from profile configuration."""
        return {
            "browser_type": "chrome",
            "user_agent": profile.user_agent or self._generate_user_agent(),
            "screen_resolution": profile.screen_resolution or "1920x1080",
            "timezone": profile.timezone or "UTC",
            "language": profile.language or "en-US",
            "disable_images": False,
            "disable_javascript": False,
            "disable_plugins": True,
            "disable_webgl": False,
        }
    
    def _get_proxy_settings(self, profile: Profile) -> Dict[str, Any]:
        """Get proxy settings for antidetect browser."""
        if not profile.proxy_type or profile.proxy_type == "no_proxy":
            return {"mode": "direct", "enabled": False}

        # Validate proxy configuration
        if not profile.proxy_host or not profile.proxy_port:
            logger.warning(f"Profile {profile.id} has proxy type {profile.proxy_type} but missing host/port")
            return {"mode": "direct", "enabled": False}

        # Validate port is numeric
        try:
            port = int(profile.proxy_port)
            if port < 1 or port > 65535:
                raise ValueError("Port out of range")
        except (ValueError, TypeError):
            logger.warning(f"Profile {profile.id} has invalid proxy port: {profile.proxy_port}")
            return {"mode": "direct", "enabled": False}

        proxy_config = {
            "mode": "manual",
            "enabled": True,
            "proxy_type": profile.proxy_type,
            "host": profile.proxy_host,
            "port": port,
            "auth_required": bool(profile.proxy_username),
            "username": profile.proxy_username,
            "password": profile.proxy_password
        }

        # Add validation status
        proxy_config["validation"] = {
            "is_valid": True,
            "message": "Proxy configuration validated successfully"
        }

        return proxy_config
    
    def _get_fingerprint_settings(self, profile: Profile) -> Dict[str, Any]:
        """Get fingerprint settings for antidetect browser."""
        browser_config = profile.get_browser_config()
        
        return {
            "canvas_fingerprint": browser_config.get("canvas_fingerprint", "random"),
            "webgl_fingerprint": browser_config.get("webgl_fingerprint", "random"),
            "audio_fingerprint": browser_config.get("audio_fingerprint", "random"),
            "font_fingerprint": browser_config.get("font_fingerprint", "random"),
            "screen_fingerprint": browser_config.get("screen_fingerprint", "random"),
            "timezone_spoofing": browser_config.get("timezone_spoofing", True),
            "geolocation_spoofing": browser_config.get("geolocation_spoofing", True),
        }
    
    def _get_security_settings(self, profile: Profile) -> Dict[str, Any]:
        """Get security settings for antidetect browser."""
        browser_config = profile.get_browser_config()
        
        return {
            "disable_webrtc": browser_config.get("disable_webrtc", True),
            "disable_webrtc_leak": browser_config.get("disable_webrtc_leak", True),
            "block_ads": browser_config.get("block_ads", True),
            "block_trackers": browser_config.get("block_trackers", True),
            "clear_cookies_on_exit": browser_config.get("clear_cookies_on_exit", False),
            "private_mode": browser_config.get("private_mode", False),
        }
    
    def _generate_user_agent(self, browser_type: str = "chrome") -> str:
        """Generate realistic user agent based on browser type."""
        
        # Common user agents for different browsers
        user_agents = {
            "chrome": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ],
            "firefox": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0"
            ],
            "edge": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"
            ]
        }
        
        import random
        return random.choice(user_agents.get(browser_type, user_agents["chrome"]))
    
    def validate_browser_config(self, config: Dict[str, Any]) -> tuple[bool, str]:
        """Validate browser configuration before creating instance."""
        try:
            # Check required fields
            required_fields = ["profile_id", "profile_name", "profile_path"]
            for field in required_fields:
                if field not in config:
                    return False, f"Missing required field: {field}"
            
            # Validate proxy settings if enabled
            proxy_settings = config.get("proxy_settings", {})
            if proxy_settings.get("enabled", False):
                validation = proxy_settings.get("validation", {})
                if not validation.get("is_valid", False):
                    return False, f"Invalid proxy configuration: {validation.get('message', 'Unknown error')}"
            
            # Validate profile path
            profile_path = config["profile_path"]
            if not os.path.exists(profile_path):
                try:
                    Path(profile_path).mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    return False, f"Cannot create profile directory: {str(e)}"
            
            return True, "Browser configuration is valid"
            
        except Exception as e:
            logger.error(f"Error validating browser config: {str(e)}")
            return False, f"Validation error: {str(e)}"
    
    def save_browser_config(self, profile: Profile, config: Dict[str, Any]) -> str:
        """Save browser configuration to file for antidetect browser."""
        try:
            config_path = os.path.join(config["profile_path"], "browser_config.json")
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Browser configuration saved for profile {profile.id}: {config_path}")
            return config_path
            
        except Exception as e:
            logger.error(f"Error saving browser config for profile {profile.id}: {str(e)}")
            raise
    
    def get_zendriver_config(self, profile: Profile) -> Dict[str, Any]:
        """Get configuration specifically formatted for zendriver."""
        browser_config = self.create_browser_config(profile)
        
        zendriver_config = {
            "user_data_dir": browser_config["profile_path"],
            "headless": False,  # Antidetect browsers typically run in headed mode
            "disable_blink_features": "AutomationControlled",
            "exclude_switches": ["enable-automation"],
            "use_subprocess": False,
            "disable_dev_shm_usage": True,
            "no_sandbox": True,
        }
        
        # Add proxy configuration for zendriver
        proxy_settings = browser_config["proxy_settings"]
        if proxy_settings.get("enabled", False) and proxy_settings.get("validation", {}).get("is_valid", False):
            if proxy_settings["proxy_type"] in ["http", "https"]:
                zendriver_config["proxy_server"] = f"{proxy_settings['host']}:{proxy_settings['port']}"
                if proxy_settings.get("auth_required", False):
                    zendriver_config["proxy_username"] = proxy_settings.get("username")
                    zendriver_config["proxy_password"] = proxy_settings.get("password")
            elif proxy_settings["proxy_type"] == "socks5":
                zendriver_config["proxy_server"] = f"socks5://{proxy_settings['host']}:{proxy_settings['port']}"
        
        # Add browser arguments for antidetect features
        browser_settings = browser_config["browser_settings"]
        args = [
            f"--user-agent={browser_settings['user_agent']}",
            f"--window-size={browser_settings['screen_resolution'].replace('x', ',')}",
            "--disable-blink-features=AutomationControlled",
            "--exclude-switches=enable-automation",
            "--disable-extensions-except",
            "--disable-plugins-discovery",
            "--no-first-run",
            "--no-service-autorun",
            "--password-store=basic",
        ]
        
        # Add security arguments
        security_settings = browser_config["security_settings"]
        if security_settings.get("disable_webrtc", True):
            args.append("--disable-webrtc")
        if security_settings.get("block_ads", True):
            args.append("--disable-background-networking")
        
        zendriver_config["arguments"] = args
        
        return zendriver_config
