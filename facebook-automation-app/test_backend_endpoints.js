// Test script to check which backend endpoints are available
const axios = require('axios');

const BASE_URL = 'http://localhost:8000';

async function testBackendEndpoints() {
  console.log('🔍 Testing Backend Endpoints');
  console.log('=' * 50);

  try {
    // Test 1: Get profiles
    console.log('\n1. Testing GET /api/profiles...');
    const profilesResponse = await axios.get(`${BASE_URL}/api/profiles`);
    console.log(`✅ Status: ${profilesResponse.status}`);
    console.log(`Response type: ${typeof profilesResponse.data}`);
    console.log(`Is array: ${Array.isArray(profilesResponse.data)}`);
    
    if (Array.isArray(profilesResponse.data)) {
      console.log(`Profiles count: ${profilesResponse.data.length}`);
      if (profilesResponse.data.length > 0) {
        const firstProfile = profilesResponse.data[0];
        console.log(`First profile ID type: ${typeof firstProfile.id}`);
        console.log(`First profile ID: ${firstProfile.id}`);
        console.log(`First profile name: ${firstProfile.name}`);
        
        // Test with first profile
        const profileId = firstProfile.id;
        
        // Test 2: Test proxy endpoint
        console.log(`\n2. Testing POST /api/profiles/${profileId}/test-proxy...`);
        try {
          const proxyTestResponse = await axios.post(`${BASE_URL}/api/profiles/${profileId}/test-proxy`);
          console.log(`✅ Proxy test status: ${proxyTestResponse.status}`);
          console.log(`Proxy test response:`, proxyTestResponse.data);
        } catch (error) {
          console.log(`❌ Proxy test failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        }
        
        // Test 3: Test browser endpoint
        console.log(`\n3. Testing POST /api/profiles/${profileId}/test-browser...`);
        try {
          const browserTestResponse = await axios.post(`${BASE_URL}/api/profiles/${profileId}/test-browser`);
          console.log(`✅ Browser test status: ${browserTestResponse.status}`);
          console.log(`Browser test response:`, browserTestResponse.data);
        } catch (error) {
          console.log(`❌ Browser test failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        }
        
        // Test 4: Get zendriver config
        console.log(`\n4. Testing GET /api/profiles/${profileId}/zendriver-config...`);
        try {
          const zendriverResponse = await axios.get(`${BASE_URL}/api/profiles/${profileId}/zendriver-config`);
          console.log(`✅ Zendriver config status: ${zendriverResponse.status}`);
          console.log(`Zendriver config available: ${!!zendriverResponse.data}`);
        } catch (error) {
          console.log(`❌ Zendriver config failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        }
        
        // Test 5: Get proxy types
        console.log(`\n5. Testing GET /api/profiles/proxy-types...`);
        try {
          const proxyTypesResponse = await axios.get(`${BASE_URL}/api/profiles/proxy-types`);
          console.log(`✅ Proxy types status: ${proxyTypesResponse.status}`);
          console.log(`Proxy types available: ${!!proxyTypesResponse.data}`);
        } catch (error) {
          console.log(`❌ Proxy types failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
        }
      }
    } else if (profilesResponse.data.profiles) {
      console.log(`Profiles count: ${profilesResponse.data.profiles.length}`);
      console.log(`Total: ${profilesResponse.data.total}`);
      console.log(`Page: ${profilesResponse.data.page}`);
      
      if (profilesResponse.data.profiles.length > 0) {
        const firstProfile = profilesResponse.data.profiles[0];
        console.log(`First profile ID type: ${typeof firstProfile.id}`);
        console.log(`First profile ID: ${firstProfile.id}`);
        console.log(`First profile name: ${firstProfile.name}`);
      }
    }
    
    // Test 6: Health check
    console.log(`\n6. Testing GET /health...`);
    try {
      const healthResponse = await axios.get(`${BASE_URL}/health`);
      console.log(`✅ Health check status: ${healthResponse.status}`);
      console.log(`Health response:`, healthResponse.data);
    } catch (error) {
      console.log(`❌ Health check failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }
    
    // Test 7: System health
    console.log(`\n7. Testing GET /api/system/health...`);
    try {
      const systemHealthResponse = await axios.get(`${BASE_URL}/api/system/health`);
      console.log(`✅ System health status: ${systemHealthResponse.status}`);
      console.log(`System health response:`, systemHealthResponse.data);
    } catch (error) {
      console.log(`❌ System health failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }
    
    // Test 8: Check available endpoints
    console.log(`\n8. Testing common endpoints...`);
    const endpoints = [
      '/api/scraping/sessions',
      '/api/messaging/messages',
      '/api/campaigns',
      '/api/analytics/dashboard'
    ];
    
    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${BASE_URL}${endpoint}`);
        console.log(`✅ ${endpoint}: ${response.status}`);
      } catch (error) {
        console.log(`❌ ${endpoint}: ${error.response?.status || 'Connection failed'}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Failed to connect to backend:', error.message);
    console.log('\nPossible issues:');
    console.log('1. Backend server is not running');
    console.log('2. Backend is running on different port');
    console.log('3. CORS issues');
    console.log('4. Different backend version');
  }
  
  console.log('\n' + '=' * 50);
  console.log('🎯 Backend endpoint testing completed!');
}

// Run the test
testBackendEndpoints();
