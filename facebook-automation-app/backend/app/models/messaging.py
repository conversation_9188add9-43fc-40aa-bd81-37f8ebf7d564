"""
Database models for messaging system.
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, Boolean, Float, Foreign<PERSON>ey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum
import enum

from ..core.database import Base


class MessageStatus(str, Enum):
    """Message delivery status."""
    PENDING = "pending"
    SENDING = "sending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    CANCELLED = "cancelled"


class MessageType(str, Enum):
    """Message type."""
    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    LINK = "link"
    TEMPLATE = "template"


class CampaignStatus(str, Enum):
    """Campaign status."""
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class MessageTemplate(Base):
    """Message template model."""
    __tablename__ = "message_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    content = Column(Text, nullable=False)
    variables = Column(JSON, default=list)  # List of variable names used in template
    message_type = Column(String(50), default=MessageType.TEXT.value)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(255))
    
    # Usage statistics
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True))
    
    # Relationships - commented out for compatibility
    # campaigns = relationship("MessageCampaign", back_populates="template")
    # messages = relationship("Message", back_populates="template")
    
    def __repr__(self):
        return f"<MessageTemplate(id={self.id}, name='{self.name}')>"
    
    def get_variables(self):
        """Get template variables as list."""
        return self.variables or []
    
    def render_content(self, variables: dict) -> str:
        """Render template content with variables."""
        content = self.content
        for var_name, var_value in variables.items():
            placeholder = f"{{{var_name}}}"
            content = content.replace(placeholder, str(var_value))
        return content


class MessageCampaign(Base):
    """Message campaign model."""
    __tablename__ = "message_campaigns"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    
    # Campaign configuration
    template_id = Column(Integer, ForeignKey("message_templates.id"))
    profile_id = Column(Integer, ForeignKey("profiles.id"))
    scraping_session_id = Column(Integer, ForeignKey("scraping_sessions.id"))
    
    # Targeting
    target_user_count = Column(Integer, default=0)
    target_filters = Column(JSON, default=dict)  # Filters for target users
    
    # Scheduling
    scheduled_at = Column(DateTime(timezone=True))
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    
    # Rate limiting
    messages_per_minute = Column(Integer, default=10)
    delay_between_messages = Column(Float, default=6.0)  # seconds
    
    # Status and progress
    status = Column(String(50), default=CampaignStatus.DRAFT.value)
    progress_percentage = Column(Float, default=0.0)
    current_step = Column(String(255))
    error_message = Column(Text)
    
    # Statistics
    total_messages = Column(Integer, default=0)
    sent_messages = Column(Integer, default=0)
    delivered_messages = Column(Integer, default=0)
    failed_messages = Column(Integer, default=0)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships - commented out for compatibility
    # template = relationship("MessageTemplate", back_populates="campaigns")
    # profile = relationship("Profile", back_populates="message_campaigns")
    # scraping_session = relationship("ScrapingSession", back_populates="message_campaigns")
    # messages = relationship("Message", back_populates="campaign", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<MessageCampaign(id={self.id}, name='{self.name}', status='{self.status}')>"
    
    def get_success_rate(self) -> float:
        """Calculate campaign success rate."""
        if self.total_messages == 0:
            return 0.0
        return (self.delivered_messages / self.total_messages) * 100
    
    def get_failure_rate(self) -> float:
        """Calculate campaign failure rate."""
        if self.total_messages == 0:
            return 0.0
        return (self.failed_messages / self.total_messages) * 100


class Message(Base):
    """Individual message model."""
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Campaign and template
    campaign_id = Column(Integer, ForeignKey("message_campaigns.id"), nullable=False)
    template_id = Column(Integer, ForeignKey("message_templates.id"))
    
    # Target user
    target_uid = Column(String(255), nullable=False, index=True)
    target_name = Column(String(255))
    target_profile_url = Column(String(500))
    
    # Message content
    content = Column(Text, nullable=False)
    message_type = Column(String(50), default=MessageType.TEXT.value)
    variables_used = Column(JSON, default=dict)  # Variables used for this message
    
    # Delivery information
    status = Column(String(50), default=MessageStatus.PENDING.value, index=True)
    scheduled_at = Column(DateTime(timezone=True))
    sent_at = Column(DateTime(timezone=True))
    delivered_at = Column(DateTime(timezone=True))
    failed_at = Column(DateTime(timezone=True))
    
    # Error handling
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    
    # Tracking
    facebook_message_id = Column(String(255))  # Facebook's message ID if available
    conversation_id = Column(String(255))  # Facebook conversation ID
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships - commented out for compatibility
    # campaign = relationship("MessageCampaign", back_populates="messages")
    # template = relationship("MessageTemplate", back_populates="messages")
    
    def __repr__(self):
        return f"<Message(id={self.id}, target_uid='{self.target_uid}', status='{self.status}')>"
    
    def can_retry(self) -> bool:
        """Check if message can be retried."""
        return (
            self.status == MessageStatus.FAILED.value and 
            self.retry_count < self.max_retries
        )
    
    def mark_as_sent(self, facebook_message_id: str = None):
        """Mark message as sent."""
        self.status = MessageStatus.SENT.value
        self.sent_at = func.now()
        if facebook_message_id:
            self.facebook_message_id = facebook_message_id
    
    def mark_as_delivered(self):
        """Mark message as delivered."""
        self.status = MessageStatus.DELIVERED.value
        self.delivered_at = func.now()
    
    def mark_as_failed(self, error_message: str):
        """Mark message as failed."""
        self.status = MessageStatus.FAILED.value
        self.failed_at = func.now()
        self.error_message = error_message
        self.retry_count += 1


class MessageQueue(Base):
    """Message queue for batch processing."""
    __tablename__ = "message_queue"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Queue information
    campaign_id = Column(Integer, ForeignKey("message_campaigns.id"), nullable=False)
    message_id = Column(Integer, ForeignKey("messages.id"), nullable=False)
    
    # Queue status
    status = Column(String(50), default="queued", index=True)
    priority = Column(Integer, default=0)  # Higher number = higher priority
    
    # Processing information
    assigned_worker = Column(String(255))  # Worker thread/process ID
    processing_started_at = Column(DateTime(timezone=True))
    processing_completed_at = Column(DateTime(timezone=True))
    
    # Retry information
    retry_count = Column(Integer, default=0)
    next_retry_at = Column(DateTime(timezone=True))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships - commented out for compatibility
    # campaign = relationship("MessageCampaign")
    # message = relationship("Message")
    
    def __repr__(self):
        return f"<MessageQueue(id={self.id}, message_id={self.message_id}, status='{self.status}')>"


class MessageAnalytics(Base):
    """Message analytics and reporting."""
    __tablename__ = "message_analytics"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Campaign reference
    campaign_id = Column(Integer, ForeignKey("message_campaigns.id"), nullable=False)
    
    # Time period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    hour = Column(Integer)  # 0-23 for hourly analytics
    
    # Metrics
    messages_sent = Column(Integer, default=0)
    messages_delivered = Column(Integer, default=0)
    messages_failed = Column(Integer, default=0)
    
    # Performance metrics
    avg_delivery_time = Column(Float)  # Average delivery time in seconds
    success_rate = Column(Float)  # Success rate percentage
    
    # Error analysis
    error_types = Column(JSON, default=dict)  # Count of different error types
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships - commented out for compatibility
    # campaign = relationship("MessageCampaign")
    
    def __repr__(self):
        return f"<MessageAnalytics(id={self.id}, campaign_id={self.campaign_id}, date='{self.date}')>"
