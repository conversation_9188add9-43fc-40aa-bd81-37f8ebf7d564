// Test script to verify Facebook endpoints are working
const axios = require('axios');

const BASE_URL = 'http://localhost:8000';

async function testFacebookEndpoints() {
  console.log('🧪 Testing Facebook Endpoints');
  console.log('=' * 50);

  try {
    // Step 1: Get profiles
    console.log('\n1. Getting profiles...');
    const profilesResponse = await axios.get(`${BASE_URL}/api/profiles`);
    
    let profiles = [];
    if (Array.isArray(profilesResponse.data)) {
      profiles = profilesResponse.data;
    } else if (profilesResponse.data.profiles) {
      profiles = profilesResponse.data.profiles;
    }
    
    console.log(`✅ Found ${profiles.length} profiles`);
    
    if (profiles.length === 0) {
      console.log('❌ No profiles found. Please create a profile first.');
      return;
    }
    
    const testProfile = profiles[0];
    const profileId = testProfile.id;
    
    console.log(`Using profile: ${testProfile.name} (${profileId})`);
    
    // Step 2: Test Facebook login endpoint
    console.log('\n2. Testing Facebook login endpoint...');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/profiles/${profileId}/facebook-login`);
      console.log(`✅ Facebook login endpoint works!`);
      console.log(`   Status: ${loginResponse.data.status}`);
      console.log(`   Message: ${loginResponse.data.message}`);
      console.log(`   Instructions: ${loginResponse.data.instructions?.length || 0} steps`);
    } catch (error) {
      console.log(`❌ Facebook login failed: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Step 3: Test Facebook status endpoint
    console.log('\n3. Testing Facebook status endpoint...');
    try {
      const statusResponse = await axios.get(`${BASE_URL}/api/profiles/${profileId}/facebook-status`);
      console.log(`✅ Facebook status endpoint works!`);
      console.log(`   Is logged in: ${statusResponse.data.is_logged_in}`);
      console.log(`   Profile status: ${statusResponse.data.status}`);
      console.log(`   Facebook email: ${statusResponse.data.facebook_email || 'None'}`);
    } catch (error) {
      console.log(`❌ Facebook status failed: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Step 4: Test Facebook login complete endpoint
    console.log('\n4. Testing Facebook login complete endpoint...');
    try {
      const mockData = {
        email: "<EMAIL>",
        username: "test_user",
        user_id: "123456789"
      };
      
      const completeResponse = await axios.post(
        `${BASE_URL}/api/profiles/${profileId}/facebook-login-complete`,
        mockData
      );
      console.log(`✅ Facebook login complete endpoint works!`);
      console.log(`   Status: ${completeResponse.data.status}`);
      console.log(`   Message: ${completeResponse.data.message}`);
      console.log(`   Facebook email: ${completeResponse.data.facebook_email}`);
    } catch (error) {
      console.log(`❌ Facebook login complete failed: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
    // Step 5: Test other endpoints
    console.log('\n5. Testing other endpoints...');
    
    const endpoints = [
      { method: 'POST', path: `/api/profiles/${profileId}/test-proxy`, name: 'Test Proxy' },
      { method: 'POST', path: `/api/profiles/${profileId}/test-browser`, name: 'Test Browser' },
      { method: 'GET', path: `/api/profiles/${profileId}/zendriver-config`, name: 'Zendriver Config' },
      { method: 'GET', path: `/api/profiles/proxy-types`, name: 'Proxy Types' }
    ];
    
    for (const endpoint of endpoints) {
      try {
        let response;
        if (endpoint.method === 'GET') {
          response = await axios.get(`${BASE_URL}${endpoint.path}`);
        } else {
          response = await axios.post(`${BASE_URL}${endpoint.path}`);
        }
        console.log(`   ✅ ${endpoint.name}: ${response.status}`);
      } catch (error) {
        console.log(`   ❌ ${endpoint.name}: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
      }
    }
    
    // Step 6: Check final profile status
    console.log('\n6. Checking final profile status...');
    try {
      const finalProfileResponse = await axios.get(`${BASE_URL}/api/profiles/${profileId}`);
      const finalProfile = finalProfileResponse.data;
      console.log(`✅ Final profile status:`);
      console.log(`   Name: ${finalProfile.name}`);
      console.log(`   Status: ${finalProfile.status}`);
      console.log(`   Facebook email: ${finalProfile.facebook_email || 'None'}`);
      console.log(`   Facebook username: ${finalProfile.facebook_username || 'None'}`);
      console.log(`   Last used: ${finalProfile.last_used || 'Never'}`);
    } catch (error) {
      console.log(`❌ Get profile failed: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
  
  console.log('\n' + '=' * 50);
  console.log('🎉 Facebook endpoints testing completed!');
  console.log('\nNext steps:');
  console.log('1. If all tests pass, try the frontend Facebook login feature');
  console.log('2. If tests fail, check backend server logs');
  console.log('3. Make sure backend server is running on port 8000');
}

// Run the test
testFacebookEndpoints();
