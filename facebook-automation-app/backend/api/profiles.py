"""
Profile management API endpoints
"""

from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from loguru import logger

from database.connection import get_db
from database.models import Profile
from schemas.profile import ProfileCreate, ProfileUpdate, ProfileResponse, ProfileList, ProfileStats

router = APIRouter(prefix="/api/profiles", tags=["profiles"])

@router.get("/", response_model=ProfileList)
async def get_profiles(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get all profiles with pagination and filtering"""
    try:
        query = db.query(Profile)
        
        if status:
            query = query.filter(Profile.status == status)
        
        total = query.count()
        profiles = query.offset(skip).limit(limit).all()
        
        return ProfileList(
            profiles=profiles,
            total=total,
            page=skip // limit + 1,
            per_page=limit
        )
    except Exception as e:
        logger.error(f"Error fetching profiles: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profiles")

@router.get("/stats", response_model=ProfileStats)
async def get_profile_stats(db: Session = Depends(get_db)):
    """Get profile statistics"""
    try:
        total_profiles = db.query(Profile).count()
        active_profiles = db.query(Profile).filter(Profile.status == "active").count()
        inactive_profiles = db.query(Profile).filter(Profile.status == "inactive").count()
        banned_profiles = db.query(Profile).filter(Profile.status == "banned").count()
        error_profiles = db.query(Profile).filter(Profile.status == "error").count()
        
        return ProfileStats(
            total_profiles=total_profiles,
            active_profiles=active_profiles,
            inactive_profiles=inactive_profiles,
            banned_profiles=banned_profiles,
            error_profiles=error_profiles
        )
    except Exception as e:
        logger.error(f"Error fetching profile stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profile statistics")

@router.get("/{profile_id}", response_model=ProfileResponse)
async def get_profile(profile_id: str, db: Session = Depends(get_db)):
    """Get a specific profile by ID"""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        return profile
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch profile")

@router.post("/", response_model=ProfileResponse)
async def create_profile(profile_data: ProfileCreate, db: Session = Depends(get_db)):
    """Create a new profile"""
    try:
        # Check if profile with same name exists
        existing = db.query(Profile).filter(Profile.name == profile_data.name).first()
        if existing:
            raise HTTPException(status_code=400, detail="Profile with this name already exists")
        
        # Create new profile
        profile = Profile(**profile_data.dict())
        db.add(profile)
        db.commit()
        db.refresh(profile)
        
        logger.info(f"Created new profile: {profile.name} ({profile.id})")
        return profile
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating profile: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create profile")

@router.put("/{profile_id}", response_model=ProfileResponse)
async def update_profile(
    profile_id: str, 
    profile_data: ProfileUpdate, 
    db: Session = Depends(get_db)
):
    """Update an existing profile"""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        
        # Update profile fields
        update_data = profile_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(profile, field, value)
        
        db.commit()
        db.refresh(profile)
        
        logger.info(f"Updated profile: {profile.name} ({profile.id})")
        return profile
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating profile {profile_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update profile")

@router.delete("/{profile_id}")
async def delete_profile(profile_id: str, db: Session = Depends(get_db)):
    """Delete a profile"""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        
        # Check if profile is being used in active campaigns
        # TODO: Add check for active campaigns
        
        db.delete(profile)
        db.commit()
        
        logger.info(f"Deleted profile: {profile.name} ({profile.id})")
        return {"message": "Profile deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting profile {profile_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete profile")


@router.post("/{profile_id}/facebook-login")
async def facebook_login(profile_id: str, db: Session = Depends(get_db)):
    """Initiate Facebook login for profile."""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Update profile status to indicate login in progress
        profile.status = "active"
        profile.last_used = datetime.utcnow()
        db.commit()

        logger.info(f"Facebook login initiated for profile: {profile.name} ({profile.id})")

        return {
            "status": "login_initiated",
            "message": "Facebook login session started. Please complete login manually in the browser.",
            "profile_id": profile.id,
            "profile_name": profile.name,
            "login_url": "https://www.facebook.com/login",
            "instructions": [
                "1. Browser window will open with your profile configuration",
                "2. Navigate to Facebook login page",
                "3. Enter your Facebook credentials manually",
                "4. Complete any 2FA or security checks",
                "5. Once logged in, click 'Complete Login' button in the app"
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error initiating Facebook login for profile {profile_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to initiate Facebook login")


@router.post("/{profile_id}/facebook-login-complete")
async def facebook_login_complete(
    profile_id: str,
    facebook_data: dict = None,
    db: Session = Depends(get_db)
):
    """Complete Facebook login and update profile status."""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Update profile with Facebook login information
        if facebook_data:
            if facebook_data.get('email'):
                profile.facebook_email = facebook_data['email']
            if facebook_data.get('user_id'):
                profile.facebook_user_id = facebook_data['user_id']
            if facebook_data.get('username'):
                profile.facebook_username = facebook_data['username']

        # Update profile status to ready
        profile.status = "active"
        profile.last_used = datetime.utcnow()
        db.commit()

        logger.info(f"Facebook login completed for profile: {profile.name} ({profile.id})")

        return {
            "status": "login_complete",
            "message": "Facebook login completed successfully. Profile is now ready for automation.",
            "profile_id": profile.id,
            "profile_name": profile.name,
            "profile_status": profile.status,
            "facebook_email": profile.facebook_email,
            "facebook_username": profile.facebook_username
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing Facebook login for profile {profile_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to complete Facebook login")


@router.get("/{profile_id}/facebook-status")
async def get_facebook_status(profile_id: str, db: Session = Depends(get_db)):
    """Get Facebook login status for profile."""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        is_logged_in = bool(profile.facebook_email or profile.facebook_user_id)

        return {
            "profile_id": profile.id,
            "profile_name": profile.name,
            "is_logged_in": is_logged_in,
            "facebook_email": getattr(profile, 'facebook_email', None),
            "facebook_username": getattr(profile, 'facebook_username', None),
            "facebook_user_id": getattr(profile, 'facebook_user_id', None),
            "last_used": profile.last_used.isoformat() if profile.last_used else None,
            "status": profile.status
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Facebook status for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get Facebook status")


@router.post("/{profile_id}/test-proxy")
async def test_proxy_connection(profile_id: str, db: Session = Depends(get_db)):
    """Test proxy connection for profile."""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Mock proxy test for now
        return {
            "status": "success",
            "message": "Proxy test completed (mock implementation)",
            "response_time": 0.5,
            "working": True
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing proxy for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to test proxy")


@router.post("/{profile_id}/test-browser")
async def test_browser_configuration(profile_id: str, db: Session = Depends(get_db)):
    """Test browser configuration for profile."""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Mock browser test for now
        return {
            "status": "success",
            "message": "Browser configuration test completed (mock implementation)",
            "ready_for_automation": True
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing browser for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to test browser")


@router.get("/{profile_id}/zendriver-config")
async def get_zendriver_config(profile_id: str, db: Session = Depends(get_db)):
    """Get zendriver configuration for profile."""
    try:
        profile = db.query(Profile).filter(Profile.id == profile_id).first()
        if not profile:
            raise HTTPException(status_code=404, detail="Profile not found")

        # Mock zendriver config for now
        return {
            "profile_id": profile.id,
            "profile_name": profile.name,
            "zendriver_config": {
                "user_data_dir": f"/tmp/profile_{profile.id}",
                "headless": False,
                "disable_blink_features": "AutomationControlled"
            },
            "ready_for_automation": True
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting zendriver config for profile {profile_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get zendriver config")


@router.get("/proxy-types")
async def get_proxy_types():
    """Get available proxy types."""
    return {
        "proxy_types": {
            "no_proxy": {
                "name": "No Proxy",
                "value": "no_proxy",
                "recommendations": {
                    "description": "Direct connection without proxy",
                    "use_cases": ["Local development", "No anonymity required"],
                    "security_level": "Low",
                    "speed": "Fast"
                }
            },
            "http": {
                "name": "HTTP",
                "value": "http",
                "recommendations": {
                    "description": "HTTP proxy for web traffic",
                    "use_cases": ["Web browsing", "API requests"],
                    "common_ports": [8080, 3128, 8888, 80],
                    "security_level": "Medium",
                    "speed": "Fast"
                }
            },
            "https": {
                "name": "HTTPS",
                "value": "https",
                "recommendations": {
                    "description": "HTTPS proxy with SSL encryption",
                    "use_cases": ["Secure web browsing", "Encrypted traffic"],
                    "common_ports": [8080, 3128, 8888, 443],
                    "security_level": "High",
                    "speed": "Medium"
                }
            },
            "socks5": {
                "name": "SOCKS5",
                "value": "socks5",
                "recommendations": {
                    "description": "SOCKS5 proxy supporting all traffic types",
                    "use_cases": ["All protocols", "High anonymity"],
                    "common_ports": [1080, 1081, 9050],
                    "security_level": "High",
                    "speed": "Medium"
                }
            },
            "ssh": {
                "name": "SSH",
                "value": "ssh",
                "recommendations": {
                    "description": "SSH tunnel proxy for maximum security",
                    "use_cases": ["Secure tunneling", "Server access"],
                    "common_ports": [22, 2222],
                    "security_level": "Very High",
                    "speed": "Slow"
                }
            }
        },
        "default": "no_proxy"
    }
