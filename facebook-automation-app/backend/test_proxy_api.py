#!/usr/bin/env python3
"""
Test script for proxy API endpoints to ensure proper handling of form data.
"""
import asyncio
import json
from datetime import datetime

import httpx
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from app.core.config import settings
from app.models.profile import Profile, ProxyConfig, ProxyType, ProfileStatus


async def test_proxy_api():
    """Test proxy API endpoints with various data types."""
    
    base_url = "http://localhost:8000/api/profiles"
    
    async with httpx.AsyncClient() as client:
        print("🧪 Testing Proxy API Endpoints")
        print("=" * 50)
        
        # Test 1: Create profile with no proxy
        print("\n1. Testing profile creation with no proxy...")
        no_proxy_data = {
            "name": "Test Profile No Proxy",
            "user_agent": "Mozilla/5.0 (Test Browser)",
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1920x1080",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": {
                "proxy_type": "no_proxy",
                "host": None,
                "port": None,
                "username": None,
                "password": None
            }
        }
        
        try:
            response = await client.post(base_url, json=no_proxy_data)
            if response.status_code == 201:
                profile_data = response.json()
                print(f"✅ Created profile: {profile_data['name']} (ID: {profile_data['id']})")
                no_proxy_profile_id = profile_data['id']
            else:
                print(f"❌ Failed to create no-proxy profile: {response.status_code}")
                print(f"Response: {response.text}")
                return
        except Exception as e:
            print(f"❌ Error creating no-proxy profile: {e}")
            return
        
        # Test 2: Create profile with HTTP proxy
        print("\n2. Testing profile creation with HTTP proxy...")
        http_proxy_data = {
            "name": "Test Profile HTTP Proxy",
            "user_agent": None,  # Test null user agent
            "browser_config": {
                "browser_type": "chrome",
                "screen_resolution": "1366x768",
                "timezone": "Asia/Ho_Chi_Minh",
                "language": "vi-VN"
            },
            "proxy_config": {
                "proxy_type": "http",
                "host": "proxy.example.com",
                "port": 8080,
                "username": "testuser",
                "password": "testpass"
            }
        }
        
        try:
            response = await client.post(base_url, json=http_proxy_data)
            if response.status_code == 201:
                profile_data = response.json()
                print(f"✅ Created profile: {profile_data['name']} (ID: {profile_data['id']})")
                http_proxy_profile_id = profile_data['id']
            else:
                print(f"❌ Failed to create HTTP proxy profile: {response.status_code}")
                print(f"Response: {response.text}")
                return
        except Exception as e:
            print(f"❌ Error creating HTTP proxy profile: {e}")
            return
        
        # Test 3: Create profile with empty strings (should be converted to null)
        print("\n3. Testing profile creation with empty strings...")
        empty_strings_data = {
            "name": "Test Profile Empty Strings",
            "user_agent": "",  # Empty string should become null
            "browser_config": {
                "browser_type": "firefox",
                "screen_resolution": "1280x720",
                "timezone": "UTC",
                "language": "en-US"
            },
            "proxy_config": {
                "proxy_type": "socks5",
                "host": "socks.example.com",
                "port": 1080,
                "username": "",  # Empty string should become null
                "password": ""   # Empty string should become null
            }
        }
        
        try:
            response = await client.post(base_url, json=empty_strings_data)
            if response.status_code == 201:
                profile_data = response.json()
                print(f"✅ Created profile: {profile_data['name']} (ID: {profile_data['id']})")
                empty_strings_profile_id = profile_data['id']
            else:
                print(f"❌ Failed to create empty strings profile: {response.status_code}")
                print(f"Response: {response.text}")
                return
        except Exception as e:
            print(f"❌ Error creating empty strings profile: {e}")
            return
        
        # Test 4: Test proxy connection
        print(f"\n4. Testing proxy connection for profile {http_proxy_profile_id}...")
        try:
            response = await client.post(f"{base_url}/{http_proxy_profile_id}/test-proxy")
            if response.status_code == 200:
                test_result = response.json()
                print(f"✅ Proxy test result: {test_result['status']}")
                print(f"   Message: {test_result['message']}")
                if 'response_time' in test_result:
                    print(f"   Response time: {test_result['response_time']}s")
            else:
                print(f"❌ Failed to test proxy: {response.status_code}")
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Error testing proxy: {e}")
        
        # Test 5: Test browser configuration
        print(f"\n5. Testing browser configuration for profile {http_proxy_profile_id}...")
        try:
            response = await client.post(f"{base_url}/{http_proxy_profile_id}/test-browser")
            if response.status_code == 200:
                test_result = response.json()
                print(f"✅ Browser test result: {test_result['status']}")
                print(f"   Message: {test_result['message']}")
            else:
                print(f"❌ Failed to test browser: {response.status_code}")
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Error testing browser: {e}")
        
        # Test 6: Get zendriver configuration
        print(f"\n6. Getting zendriver configuration for profile {http_proxy_profile_id}...")
        try:
            response = await client.get(f"{base_url}/{http_proxy_profile_id}/zendriver-config")
            if response.status_code == 200:
                config_data = response.json()
                print(f"✅ Zendriver config retrieved successfully")
                print(f"   Profile: {config_data['profile_name']}")
                print(f"   Ready for automation: {config_data['ready_for_automation']}")
            else:
                print(f"❌ Failed to get zendriver config: {response.status_code}")
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Error getting zendriver config: {e}")
        
        # Test 7: Get proxy types
        print("\n7. Getting available proxy types...")
        try:
            response = await client.get(f"{base_url}/proxy-types")
            if response.status_code == 200:
                proxy_types = response.json()
                print(f"✅ Retrieved {len(proxy_types['proxy_types'])} proxy types")
                for proxy_type, info in proxy_types['proxy_types'].items():
                    print(f"   - {info['name']}: {info['recommendations'].get('description', 'N/A')}")
            else:
                print(f"❌ Failed to get proxy types: {response.status_code}")
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Error getting proxy types: {e}")
        
        # Test 8: Update profile with different proxy type
        print(f"\n8. Updating profile {empty_strings_profile_id} to use SSH proxy...")
        update_data = {
            "name": "Updated Test Profile SSH",
            "proxy_config": {
                "proxy_type": "ssh",
                "host": "ssh.example.com",
                "port": 22,
                "username": "sshuser",
                "password": "sshpass"
            }
        }
        
        try:
            response = await client.put(f"{base_url}/{empty_strings_profile_id}", json=update_data)
            if response.status_code == 200:
                profile_data = response.json()
                print(f"✅ Updated profile: {profile_data['name']}")
                print(f"   Proxy type: {profile_data['proxy_config']['proxy_type']}")
            else:
                print(f"❌ Failed to update profile: {response.status_code}")
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Error updating profile: {e}")
        
        # Test 9: List all profiles
        print("\n9. Listing all profiles...")
        try:
            response = await client.get(base_url)
            if response.status_code == 200:
                profiles = response.json()
                print(f"✅ Retrieved {len(profiles)} profiles")
                for profile in profiles:
                    proxy_type = "No proxy"
                    if profile.get('proxy_config'):
                        proxy_type = profile['proxy_config']['proxy_type'].upper()
                    print(f"   - {profile['name']}: {proxy_type}")
            else:
                print(f"❌ Failed to list profiles: {response.status_code}")
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Error listing profiles: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 Proxy API testing completed!")


if __name__ == "__main__":
    asyncio.run(test_proxy_api())
