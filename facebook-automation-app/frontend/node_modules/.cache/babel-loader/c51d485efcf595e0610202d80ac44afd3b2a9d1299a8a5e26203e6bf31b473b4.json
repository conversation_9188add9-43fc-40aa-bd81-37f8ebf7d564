{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, Button, Grid, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Switch, FormControlLabel, Divider, Avatar, Menu, ListItemIcon, ListItemText, Alert, LinearProgress, Tooltip, FormHelperText } from '@mui/material';\nimport { Add as AddIcon, Person as PersonIcon, MoreVert as MoreVertIcon, Edit as EditIcon, Delete as DeleteIcon, PlayArrow as PlayIcon, Refresh as RefreshIcon, Computer as ComputerIcon, Security as SecurityIcon, Language as LanguageIcon, LocationOn as LocationIcon } from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProfileCard({\n  profile,\n  onEdit,\n  onDelete,\n  onTest\n}) {\n  _s();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const handleMenuClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'flex-start',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: 'primary.main',\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            children: profile.name.charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                mb: 0.5\n              },\n              children: profile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: profile.status || 'inactive',\n              size: \"small\",\n              color: getStatusColor(profile.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleMenuClick,\n          children: /*#__PURE__*/_jsxDEV(MoreVertIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(ComputerIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"Chrome\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LocationIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.timezone || 'UTC'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Proxy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.proxy_type && profile.proxy_type !== 'no_proxy' ? profile.proxy_type.toUpperCase() : 'No Proxy'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.language || 'en-US'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 24\n          }, this),\n          onClick: () => onTest(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"text\",\n          startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 24\n          }, this),\n          onClick: () => onEdit(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        anchorEl: anchorEl,\n        open: Boolean(anchorEl),\n        onClose: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onEdit(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(EditIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Edit Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onTest(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(PlayIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Test Browser\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onDelete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Delete Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n}\n_s(ProfileCard, \"+aMDa7FPcESUyQDF1vq0RSMn4/k=\");\n_c = ProfileCard;\nfunction Profiles() {\n  _s2();\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: ''\n  });\n  const queryClient = useQueryClient();\n  const {\n    setProfiles\n  } = useApp();\n\n  // Fetch profiles\n  const {\n    data: profilesData,\n    isLoading,\n    error\n  } = useQuery('profiles', profilesAPI.getAll, {\n    onSuccess: data => {\n      setProfiles(data.data || []);\n    },\n    onError: error => {\n      toast.error('Failed to load profiles');\n    }\n  });\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      console.error('Create profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to create profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(({\n    id,\n    data\n  }) => profilesAPI.update(id, data), {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setEditDialogOpen(false);\n      resetForm();\n      toast.success('Profile updated successfully');\n    },\n    onError: error => {\n      var _error$response2, _error$response2$data;\n      console.error('Update profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to update profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: error => {\n      toast.error('Failed to delete profile');\n    }\n  });\n\n  // Test browser mutation\n  const testMutation = useMutation(profileId => profilesAPI.testBrowser(profileId), {\n    onSuccess: () => {\n      toast.success('Browser test completed successfully');\n    },\n    onError: error => {\n      toast.error('Browser test failed');\n    }\n  });\n  const profiles = Array.isArray(profilesData === null || profilesData === void 0 ? void 0 : profilesData.profiles) ? profilesData.profiles : Array.isArray(profilesData === null || profilesData === void 0 ? void 0 : profilesData.data) ? profilesData.data : Array.isArray(profilesData) ? profilesData : [];\n  console.log('Profiles data:', profilesData);\n  console.log('Processed profiles:', profiles);\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: ''\n    });\n    setSelectedProfile(null);\n  };\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n  const handleEdit = profile => {\n    setSelectedProfile(profile);\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome',\n      // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '',\n      // Not in current API response\n      proxy_password: '' // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n  const handleDelete = profile => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n  const handleTest = profile => {\n    testMutation.mutate(profile.id);\n  };\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = {\n      ...formData\n    };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n    if (selectedProfile) {\n      updateMutation.mutate({\n        id: selectedProfile.id,\n        data: finalData\n      });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Helper function to safely convert form data\n  const sanitizeFormData = data => {\n    const sanitized = {\n      ...data\n    };\n\n    // Convert empty strings to null for backend\n    Object.keys(sanitized).forEach(key => {\n      if (sanitized[key] === '') {\n        sanitized[key] = null;\n      }\n    });\n\n    // Convert port to integer if provided\n    if (sanitized.proxy_port && typeof sanitized.proxy_port === 'string') {\n      const port = parseInt(sanitized.proxy_port);\n      sanitized.proxy_port = isNaN(port) ? null : port;\n    }\n    return sanitized;\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      className: \"fade-in\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        children: \"Failed to load profiles. Please check your connection to the backend.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 700\n        },\n        children: \"Profile Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 24\n          }, this),\n          onClick: () => queryClient.invalidateQueries('profiles'),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 24\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"New Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 7\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(LinearProgress, {\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 21\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2,\n        p: 2,\n        bgcolor: 'grey.100',\n        borderRadius: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        children: [\"Debug: profiles.length = \", profiles.length, \", isLoading = \", isLoading.toString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        children: [\"Raw data type: \", typeof profilesData, \", Array: \", Array.isArray(profilesData).toString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 7\n    }, this), profiles.length === 0 && !isLoading ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center',\n          py: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n          sx: {\n            fontSize: 64,\n            color: 'text.secondary',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1\n          },\n          children: \"No Profiles Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 3\n          },\n          children: \"Create your first browser profile to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 26\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Create Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: profiles.map(profile => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(ProfileCard, {\n          profile: profile,\n          onEdit: handleEdit,\n          onDelete: handleDelete,\n          onTest: handleTest\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 15\n        }, this)\n      }, profile.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: createDialogOpen || editDialogOpen,\n      onClose: () => {\n        setCreateDialogOpen(false);\n        setEditDialogOpen(false);\n        resetForm();\n      },\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: selectedProfile ? 'Edit Profile' : 'Create New Profile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Profile Name\",\n                value: formData.name,\n                onChange: e => handleFormChange('name', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Browser Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.browser_type,\n                  onChange: e => handleFormChange('browser_type', e.target.value),\n                  label: \"Browser Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"chrome\",\n                    children: \"Chrome\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"firefox\",\n                    children: \"Firefox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"edge\",\n                    children: \"Edge\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 576,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Screen Resolution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.screen_resolution,\n                  onChange: e => handleFormChange('screen_resolution', e.target.value),\n                  label: \"Screen Resolution\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1920x1080\",\n                    children: \"1920x1080\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1366x768\",\n                    children: \"1366x768\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1440x900\",\n                    children: \"1440x900\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1280x720\",\n                    children: \"1280x720\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Timezone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.timezone,\n                  onChange: e => handleFormChange('timezone', e.target.value),\n                  label: \"Timezone\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"UTC\",\n                    children: \"UTC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Asia/Ho_Chi_Minh\",\n                    children: \"Asia/Ho_Chi_Minh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 606,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"America/New_York\",\n                    children: \"America/New_York\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Europe/London\",\n                    children: \"Europe/London\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.language,\n                  onChange: e => handleFormChange('language', e.target.value),\n                  label: \"Language\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"en-US\",\n                    children: \"English (US)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"vi-VN\",\n                    children: \"Vietnamese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"zh-CN\",\n                    children: \"Chinese (Simplified)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ja-JP\",\n                    children: \"Japanese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"User Agent (Optional)\",\n                value: formData.user_agent,\n                onChange: e => handleFormChange('user_agent', e.target.value),\n                placeholder: \"Leave empty for automatic generation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 600\n                },\n                children: \"Proxy Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Proxy Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.proxy_type,\n                  onChange: e => {\n                    const proxyType = e.target.value;\n                    handleFormChange('proxy_type', proxyType);\n                    handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                  },\n                  label: \"Proxy Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"no_proxy\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"No Proxy (Local Network)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 659,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Direct connection without proxy\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 660,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 658,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 657,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"http\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTP\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Standard HTTP proxy (ports: 8080, 3128, 8888)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"https\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTPS\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 675,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Encrypted HTTPS proxy (ports: 8080, 3128, 443)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 676,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"socks5\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SOCKS5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 683,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"High anonymity proxy (ports: 1080, 1081, 9050)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 684,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ssh\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SSH\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 691,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Secure SSH tunnel (ports: 22, 2222)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 692,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 690,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 689,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: [formData.proxy_type === 'no_proxy' && 'Using direct connection', formData.proxy_type === 'http' && 'Fast, suitable for web browsing', formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data', formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols', formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 15\n            }, this), formData.proxy_type !== 'no_proxy' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Host\",\n                  value: formData.proxy_host,\n                  onChange: e => handleFormChange('proxy_host', e.target.value),\n                  required: true,\n                  placeholder: formData.proxy_type === 'ssh' ? \"e.g., ssh.example.com or *************\" : \"e.g., proxy.example.com or *************\",\n                  helperText: \"IP address or domain name of the proxy server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Port\",\n                  value: formData.proxy_port,\n                  onChange: e => handleFormChange('proxy_port', e.target.value),\n                  type: \"number\",\n                  required: true,\n                  placeholder: formData.proxy_type === 'http' ? \"8080, 3128, 8888\" : formData.proxy_type === 'https' ? \"8080, 3128, 443\" : formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" : formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\",\n                  helperText: `Common ${formData.proxy_type.toUpperCase()} ports`,\n                  inputProps: {\n                    min: 1,\n                    max: 65535\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\",\n                  value: formData.proxy_username,\n                  onChange: e => handleFormChange('proxy_username', e.target.value),\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH username for authentication\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Required for SSH connections\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\",\n                  value: formData.proxy_password,\n                  onChange: e => handleFormChange('proxy_password', e.target.value),\n                  type: \"password\",\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH password or leave empty for key-based auth\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Password or private key authentication\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2,\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    onClick: () => {\n                      if (selectedProfile) {\n                        handleTest(selectedProfile);\n                      } else {\n                        toast.info('Save the profile first to test proxy connection');\n                      }\n                    },\n                    disabled: !formData.proxy_host || !formData.proxy_port,\n                    sx: {\n                      textTransform: 'none'\n                    },\n                    children: \"Test Proxy Connection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Test proxy connection to ensure it works with antidetect browser\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setCreateDialogOpen(false);\n            setEditDialogOpen(false);\n            resetForm();\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleSubmit,\n          disabled: !formData.name || createMutation.isLoading || updateMutation.isLoading,\n          children: selectedProfile ? 'Update' : 'Create'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 808,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 466,\n    columnNumber: 5\n  }, this);\n}\n_s2(Profiles, \"EbMhr/JvNzlI5qRPzlurSNjNB6A=\", false, function () {\n  return [useQueryClient, useApp, useQuery, useMutation, useMutation, useMutation, useMutation];\n});\n_c2 = Profiles;\nexport default Profiles;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProfileCard\");\n$RefreshReg$(_c2, \"Profiles\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Switch", "FormControlLabel", "Divider", "Avatar", "<PERSON><PERSON>", "ListItemIcon", "ListItemText", "<PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON><PERSON>", "FormHelperText", "Add", "AddIcon", "Person", "PersonIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "PlayArrow", "PlayIcon", "Refresh", "RefreshIcon", "Computer", "ComputerIcon", "Security", "SecurityIcon", "Language", "LanguageIcon", "LocationOn", "LocationIcon", "useQuery", "useMutation", "useQueryClient", "toast", "profilesAPI", "useApp", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfileCard", "profile", "onEdit", "onDelete", "onTest", "_s", "anchorEl", "setAnchorEl", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "getStatusColor", "status", "sx", "height", "children", "display", "justifyContent", "alignItems", "mb", "bgcolor", "width", "mr", "name", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "label", "size", "color", "onClick", "my", "container", "spacing", "item", "xs", "fontSize", "timezone", "proxy_type", "language", "mt", "gap", "startIcon", "textTransform", "open", "Boolean", "onClose", "_c", "Profiles", "_s2", "createDialogOpen", "setCreateDialogOpen", "editDialogOpen", "setEditDialogOpen", "selectedProfile", "setSelectedProfile", "formData", "setFormData", "browser_type", "user_agent", "screen_resolution", "proxy_enabled", "proxy_host", "proxy_port", "proxy_username", "proxy_password", "queryClient", "setProfiles", "data", "profilesData", "isLoading", "error", "getAll", "onSuccess", "onError", "createMutation", "create", "invalidateQueries", "resetForm", "success", "_error$response", "_error$response$data", "console", "errorMessage", "response", "detail", "message", "updateMutation", "id", "update", "_error$response2", "_error$response2$data", "deleteMutation", "delete", "testMutation", "profileId", "testBrowser", "profiles", "Array", "isArray", "log", "handleCreate", "handleEdit", "toString", "handleDelete", "window", "confirm", "mutate", "handleTest", "handleSubmit", "submitData", "port", "parseInt", "isNaN", "finalData", "browser_config", "proxy_config", "host", "username", "password", "handleFormChange", "field", "value", "prev", "sanitizeFormData", "sanitized", "Object", "keys", "for<PERSON>ach", "key", "className", "severity", "p", "borderRadius", "length", "textAlign", "py", "map", "sm", "md", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "onChange", "e", "target", "required", "placeholder", "proxyType", "helperText", "type", "inputProps", "min", "max", "info", "disabled", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Grid,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Switch,\n  FormControlLabel,\n  Divider,\n  Avatar,\n  Menu,\n  ListItemIcon,\n  ListItemText,\n  Alert,\n  LinearProgress,\n  Tooltip,\n  FormHelperText,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Person as PersonIcon,\n  MoreVert as MoreVertIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  PlayArrow as PlayIcon,\n\n  Refresh as RefreshIcon,\n\n  Computer as ComputerIcon,\n  Security as SecurityIcon,\n  Language as LanguageIcon,\n  LocationOn as LocationIcon,\n} from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\n\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\n\nfunction ProfileCard({ profile, onEdit, onDelete, onTest }) {\n  const [anchorEl, setAnchorEl] = useState(null);\n\n  const handleMenuClick = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Avatar\n              sx={{\n                bgcolor: 'primary.main',\n                width: 48,\n                height: 48,\n                mr: 2,\n              }}\n            >\n              {profile.name.charAt(0).toUpperCase()}\n            </Avatar>\n            <Box>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                {profile.name}\n              </Typography>\n              <Chip\n                label={profile.status || 'inactive'}\n                size=\"small\"\n                color={getStatusColor(profile.status)}\n              />\n            </Box>\n          </Box>\n          <IconButton onClick={handleMenuClick}>\n            <MoreVertIcon />\n          </IconButton>\n        </Box>\n\n        <Divider sx={{ my: 2 }} />\n\n        <Grid container spacing={2}>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <ComputerIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Browser\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              Chrome\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LocationIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Location\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.timezone || 'UTC'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <SecurityIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Proxy\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.proxy_type && profile.proxy_type !== 'no_proxy'\n                ? profile.proxy_type.toUpperCase()\n                : 'No Proxy'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LanguageIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Language\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.language || 'en-US'}\n            </Typography>\n          </Grid>\n        </Grid>\n\n        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>\n          <Button\n            size=\"small\"\n            variant=\"outlined\"\n            startIcon={<PlayIcon />}\n            onClick={() => onTest(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Test\n          </Button>\n          <Button\n            size=\"small\"\n            variant=\"text\"\n            startIcon={<EditIcon />}\n            onClick={() => onEdit(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Edit\n          </Button>\n        </Box>\n\n        <Menu\n          anchorEl={anchorEl}\n          open={Boolean(anchorEl)}\n          onClose={handleMenuClose}\n        >\n          <MenuItem onClick={() => { onEdit(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <EditIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Edit Profile</ListItemText>\n          </MenuItem>\n          <MenuItem onClick={() => { onTest(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <PlayIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Test Browser</ListItemText>\n          </MenuItem>\n          <MenuItem onClick={() => { onDelete(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <DeleteIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Delete Profile</ListItemText>\n          </MenuItem>\n        </Menu>\n      </CardContent>\n    </Card>\n  );\n}\n\nfunction Profiles() {\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: '',\n  });\n\n  const queryClient = useQueryClient();\n  const { setProfiles } = useApp();\n\n  // Fetch profiles\n  const { data: profilesData, isLoading, error } = useQuery(\n    'profiles',\n    profilesAPI.getAll,\n    {\n      onSuccess: (data) => {\n        setProfiles(data.data || []);\n      },\n      onError: (error) => {\n        toast.error('Failed to load profiles');\n      },\n    }\n  );\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: (error) => {\n      console.error('Create profile error:', error);\n      const errorMessage = error?.response?.data?.detail ||\n                          error?.message ||\n                          'Failed to create profile';\n      toast.error(errorMessage);\n    },\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(\n    ({ id, data }) => profilesAPI.update(id, data),\n    {\n      onSuccess: () => {\n        queryClient.invalidateQueries('profiles');\n        setEditDialogOpen(false);\n        resetForm();\n        toast.success('Profile updated successfully');\n      },\n      onError: (error) => {\n        console.error('Update profile error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to update profile';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: (error) => {\n      toast.error('Failed to delete profile');\n    },\n  });\n\n  // Test browser mutation\n  const testMutation = useMutation(\n    (profileId) => profilesAPI.testBrowser(profileId),\n    {\n      onSuccess: () => {\n        toast.success('Browser test completed successfully');\n      },\n      onError: (error) => {\n        toast.error('Browser test failed');\n      },\n    }\n  );\n\n  const profiles = Array.isArray(profilesData?.profiles)\n    ? profilesData.profiles\n    : Array.isArray(profilesData?.data)\n    ? profilesData.data\n    : Array.isArray(profilesData)\n    ? profilesData\n    : [];\n\n  console.log('Profiles data:', profilesData);\n  console.log('Processed profiles:', profiles);\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: '',\n    });\n    setSelectedProfile(null);\n  };\n\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n\n  const handleEdit = (profile) => {\n    setSelectedProfile(profile);\n\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome', // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '', // Not in current API response\n      proxy_password: '', // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n\n  const handleDelete = (profile) => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n\n  const handleTest = (profile) => {\n    testMutation.mutate(profile.id);\n  };\n\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = { ...formData };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n\n    if (selectedProfile) {\n      updateMutation.mutate({ id: selectedProfile.id, data: finalData });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n  };\n\n  // Helper function to safely convert form data\n  const sanitizeFormData = (data) => {\n    const sanitized = { ...data };\n\n    // Convert empty strings to null for backend\n    Object.keys(sanitized).forEach(key => {\n      if (sanitized[key] === '') {\n        sanitized[key] = null;\n      }\n    });\n\n    // Convert port to integer if provided\n    if (sanitized.proxy_port && typeof sanitized.proxy_port === 'string') {\n      const port = parseInt(sanitized.proxy_port);\n      sanitized.proxy_port = isNaN(port) ? null : port;\n    }\n\n    return sanitized;\n  };\n\n  if (error) {\n    return (\n      <Box className=\"fade-in\">\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          Failed to load profiles. Please check your connection to the backend.\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box className=\"fade-in\">\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n          Profile Management\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => queryClient.invalidateQueries('profiles')}\n            sx={{ textTransform: 'none' }}\n          >\n            Refresh\n          </Button>\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={handleCreate}\n            sx={{ textTransform: 'none' }}\n          >\n            New Profile\n          </Button>\n        </Box>\n      </Box>\n\n      {isLoading && <LinearProgress sx={{ mb: 3 }} />}\n\n      {/* Debug info */}\n      <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>\n        <Typography variant=\"caption\">\n          Debug: profiles.length = {profiles.length}, isLoading = {isLoading.toString()}\n        </Typography>\n        <br />\n        <Typography variant=\"caption\">\n          Raw data type: {typeof profilesData}, Array: {Array.isArray(profilesData).toString()}\n        </Typography>\n      </Box>\n\n      {profiles.length === 0 && !isLoading ? (\n        <Card>\n          <CardContent sx={{ textAlign: 'center', py: 8 }}>\n            <PersonIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n            <Typography variant=\"h6\" sx={{ mb: 1 }}>\n              No Profiles Found\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              Create your first browser profile to get started\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={handleCreate}\n              sx={{ textTransform: 'none' }}\n            >\n              Create Profile\n            </Button>\n          </CardContent>\n        </Card>\n      ) : (\n        <Grid container spacing={3}>\n          {profiles.map((profile) => (\n            <Grid item xs={12} sm={6} md={4} key={profile.id}>\n              <ProfileCard\n                profile={profile}\n                onEdit={handleEdit}\n                onDelete={handleDelete}\n                onTest={handleTest}\n              />\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Create/Edit Profile Dialog */}\n      <Dialog\n        open={createDialogOpen || editDialogOpen}\n        onClose={() => {\n          setCreateDialogOpen(false);\n          setEditDialogOpen(false);\n          resetForm();\n        }}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          {selectedProfile ? 'Edit Profile' : 'Create New Profile'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Grid container spacing={3}>\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Profile Name\"\n                  value={formData.name}\n                  onChange={(e) => handleFormChange('name', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Browser Type</InputLabel>\n                  <Select\n                    value={formData.browser_type}\n                    onChange={(e) => handleFormChange('browser_type', e.target.value)}\n                    label=\"Browser Type\"\n                  >\n                    <MenuItem value=\"chrome\">Chrome</MenuItem>\n                    <MenuItem value=\"firefox\">Firefox</MenuItem>\n                    <MenuItem value=\"edge\">Edge</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Screen Resolution</InputLabel>\n                  <Select\n                    value={formData.screen_resolution}\n                    onChange={(e) => handleFormChange('screen_resolution', e.target.value)}\n                    label=\"Screen Resolution\"\n                  >\n                    <MenuItem value=\"1920x1080\">1920x1080</MenuItem>\n                    <MenuItem value=\"1366x768\">1366x768</MenuItem>\n                    <MenuItem value=\"1440x900\">1440x900</MenuItem>\n                    <MenuItem value=\"1280x720\">1280x720</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Timezone</InputLabel>\n                  <Select\n                    value={formData.timezone}\n                    onChange={(e) => handleFormChange('timezone', e.target.value)}\n                    label=\"Timezone\"\n                  >\n                    <MenuItem value=\"UTC\">UTC</MenuItem>\n                    <MenuItem value=\"Asia/Ho_Chi_Minh\">Asia/Ho_Chi_Minh</MenuItem>\n                    <MenuItem value=\"America/New_York\">America/New_York</MenuItem>\n                    <MenuItem value=\"Europe/London\">Europe/London</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Language</InputLabel>\n                  <Select\n                    value={formData.language}\n                    onChange={(e) => handleFormChange('language', e.target.value)}\n                    label=\"Language\"\n                  >\n                    <MenuItem value=\"en-US\">English (US)</MenuItem>\n                    <MenuItem value=\"vi-VN\">Vietnamese</MenuItem>\n                    <MenuItem value=\"zh-CN\">Chinese (Simplified)</MenuItem>\n                    <MenuItem value=\"ja-JP\">Japanese</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"User Agent (Optional)\"\n                  value={formData.user_agent}\n                  onChange={(e) => handleFormChange('user_agent', e.target.value)}\n                  placeholder=\"Leave empty for automatic generation\"\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n                  Proxy Configuration\n                </Typography>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Proxy Type</InputLabel>\n                  <Select\n                    value={formData.proxy_type}\n                    onChange={(e) => {\n                      const proxyType = e.target.value;\n                      handleFormChange('proxy_type', proxyType);\n                      handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                    }}\n                    label=\"Proxy Type\"\n                  >\n                    <MenuItem value=\"no_proxy\">\n                      <Box>\n                        <Typography variant=\"body2\">No Proxy (Local Network)</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Direct connection without proxy\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"http\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTP</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Standard HTTP proxy (ports: 8080, 3128, 8888)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"https\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTPS</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Encrypted HTTPS proxy (ports: 8080, 3128, 443)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"socks5\">\n                      <Box>\n                        <Typography variant=\"body2\">SOCKS5</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          High anonymity proxy (ports: 1080, 1081, 9050)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"ssh\">\n                      <Box>\n                        <Typography variant=\"body2\">SSH</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Secure SSH tunnel (ports: 22, 2222)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  </Select>\n                  <FormHelperText>\n                    {formData.proxy_type === 'no_proxy' && 'Using direct connection'}\n                    {formData.proxy_type === 'http' && 'Fast, suitable for web browsing'}\n                    {formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data'}\n                    {formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols'}\n                    {formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials'}\n                  </FormHelperText>\n                </FormControl>\n              </Grid>\n\n              {formData.proxy_type !== 'no_proxy' && (\n                <>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Host\"\n                      value={formData.proxy_host}\n                      onChange={(e) => handleFormChange('proxy_host', e.target.value)}\n                      required\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"e.g., ssh.example.com or *************\"\n                          : \"e.g., proxy.example.com or *************\"\n                      }\n                      helperText=\"IP address or domain name of the proxy server\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Port\"\n                      value={formData.proxy_port}\n                      onChange={(e) => handleFormChange('proxy_port', e.target.value)}\n                      type=\"number\"\n                      required\n                      placeholder={\n                        formData.proxy_type === 'http' ? \"8080, 3128, 8888\" :\n                        formData.proxy_type === 'https' ? \"8080, 3128, 443\" :\n                        formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" :\n                        formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\"\n                      }\n                      helperText={`Common ${formData.proxy_type.toUpperCase()} ports`}\n                      inputProps={{ min: 1, max: 65535 }}\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\"}\n                      value={formData.proxy_username}\n                      onChange={(e) => handleFormChange('proxy_username', e.target.value)}\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH username for authentication\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Required for SSH connections\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\"}\n                      value={formData.proxy_password}\n                      onChange={(e) => handleFormChange('proxy_password', e.target.value)}\n                      type=\"password\"\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH password or leave empty for key-based auth\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Password or private key authentication\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n\n                  <Grid item xs={12}>\n                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>\n                      <Button\n                        variant=\"outlined\"\n                        size=\"small\"\n                        onClick={() => {\n                          if (selectedProfile) {\n                            handleTest(selectedProfile);\n                          } else {\n                            toast.info('Save the profile first to test proxy connection');\n                          }\n                        }}\n                        disabled={!formData.proxy_host || !formData.proxy_port}\n                        sx={{ textTransform: 'none' }}\n                      >\n                        Test Proxy Connection\n                      </Button>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Test proxy connection to ensure it works with antidetect browser\n                      </Typography>\n                    </Box>\n                  </Grid>\n                </>\n              )}\n            </Grid>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => {\n              setCreateDialogOpen(false);\n              setEditDialogOpen(false);\n              resetForm();\n            }}\n          >\n            Cancel\n          </Button>\n          <Button\n            variant=\"contained\"\n            onClick={handleSubmit}\n            disabled={!formData.name || createMutation.isLoading || updateMutation.isLoading}\n          >\n            {selectedProfile ? 'Update' : 'Create'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n}\n\nexport default Profiles;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,cAAc,EACdC,OAAO,EACPC,cAAc,QACT,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,QAAQ,EAErBC,OAAO,IAAIC,WAAW,EAEtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,YAAY,QACrB,qBAAqB;AAC5B,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACnE,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,MAAM,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,SAASC,WAAWA,CAAC;EAAEC,OAAO;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAC1D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAMqE,eAAe,GAAIC,KAAK,IAAK;IACjCF,WAAW,CAACE,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BJ,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMK,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACEhB,OAAA,CAACvD,IAAI;IAACwE,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC3BnB,OAAA,CAACtD,WAAW;MAAAyE,QAAA,gBACVnB,OAAA,CAACzD,GAAG;QAAC0E,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,YAAY;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAC7FnB,OAAA,CAACzD,GAAG;UAAC0E,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjDnB,OAAA,CAACrC,MAAM;YACLsD,EAAE,EAAE;cACFO,OAAO,EAAE,cAAc;cACvBC,KAAK,EAAE,EAAE;cACTP,MAAM,EAAE,EAAE;cACVQ,EAAE,EAAE;YACN,CAAE;YAAAP,QAAA,EAEDf,OAAO,CAACuB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACTjC,OAAA,CAACzD,GAAG;YAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;cAAC0F,OAAO,EAAC,IAAI;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEZ,EAAE,EAAE;cAAI,CAAE;cAAAJ,QAAA,EACvDf,OAAO,CAACuB;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACbjC,OAAA,CAACnD,IAAI;cACHuF,KAAK,EAAEhC,OAAO,CAACY,MAAM,IAAI,UAAW;cACpCqB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEvB,cAAc,CAACX,OAAO,CAACY,MAAM;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjC,OAAA,CAAClD,UAAU;UAACyF,OAAO,EAAE5B,eAAgB;UAAAQ,QAAA,eACnCnB,OAAA,CAACxB,YAAY;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENjC,OAAA,CAACtC,OAAO;QAACuD,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BjC,OAAA,CAACpD,IAAI;QAAC6F,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAvB,QAAA,gBACzBnB,OAAA,CAACpD,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfnB,OAAA,CAACzD,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDnB,OAAA,CAACd,YAAY;cAAC+B,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEjC,OAAA,CAACxD,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjC,OAAA,CAACxD,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EAAC;UAErD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPjC,OAAA,CAACpD,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfnB,OAAA,CAACzD,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDnB,OAAA,CAACR,YAAY;cAACyB,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEjC,OAAA,CAACxD,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjC,OAAA,CAACxD,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDf,OAAO,CAAC0C,QAAQ,IAAI;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPjC,OAAA,CAACpD,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfnB,OAAA,CAACzD,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDnB,OAAA,CAACZ,YAAY;cAAC6B,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEjC,OAAA,CAACxD,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjC,OAAA,CAACxD,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDf,OAAO,CAAC2C,UAAU,IAAI3C,OAAO,CAAC2C,UAAU,KAAK,UAAU,GACpD3C,OAAO,CAAC2C,UAAU,CAAClB,WAAW,CAAC,CAAC,GAChC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPjC,OAAA,CAACpD,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACfnB,OAAA,CAACzD,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDnB,OAAA,CAACV,YAAY;cAAC2B,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEjC,OAAA,CAACxD,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjC,OAAA,CAACxD,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDf,OAAO,CAAC4C,QAAQ,IAAI;UAAO;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjC,OAAA,CAACzD,GAAG;QAAC0E,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE7B,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBAC1CnB,OAAA,CAACrD,MAAM;UACL0F,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,UAAU;UAClBiB,SAAS,eAAEnD,OAAA,CAAClB,QAAQ;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAMhC,MAAM,CAACH,OAAO,CAAE;UAC/Ba,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA,CAACrD,MAAM;UACL0F,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,MAAM;UACdiB,SAAS,eAAEnD,OAAA,CAACtB,QAAQ;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAMlC,MAAM,CAACD,OAAO,CAAE;UAC/Ba,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENjC,OAAA,CAACpC,IAAI;QACH6C,QAAQ,EAAEA,QAAS;QACnB4C,IAAI,EAAEC,OAAO,CAAC7C,QAAQ,CAAE;QACxB8C,OAAO,EAAEzC,eAAgB;QAAAK,QAAA,gBAEzBnB,OAAA,CAACzC,QAAQ;UAACgF,OAAO,EAAEA,CAAA,KAAM;YAAElC,MAAM,CAACD,OAAO,CAAC;YAAEU,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAK,QAAA,gBAC/DnB,OAAA,CAACnC,YAAY;YAAAsD,QAAA,eACXnB,OAAA,CAACtB,QAAQ;cAACmE,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfjC,OAAA,CAAClC,YAAY;YAAAqD,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACXjC,OAAA,CAACzC,QAAQ;UAACgF,OAAO,EAAEA,CAAA,KAAM;YAAEhC,MAAM,CAACH,OAAO,CAAC;YAAEU,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAK,QAAA,gBAC/DnB,OAAA,CAACnC,YAAY;YAAAsD,QAAA,eACXnB,OAAA,CAAClB,QAAQ;cAAC+D,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfjC,OAAA,CAAClC,YAAY;YAAAqD,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACXjC,OAAA,CAACzC,QAAQ;UAACgF,OAAO,EAAEA,CAAA,KAAM;YAAEjC,QAAQ,CAACF,OAAO,CAAC;YAAEU,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAK,QAAA,gBACjEnB,OAAA,CAACnC,YAAY;YAAAsD,QAAA,eACXnB,OAAA,CAACpB,UAAU;cAACiE,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACfjC,OAAA,CAAClC,YAAY;YAAAqD,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX;AAACzB,EAAA,CA1JQL,WAAW;AAAAqD,EAAA,GAAXrD,WAAW;AA4JpB,SAASsD,QAAQA,CAAA,EAAG;EAAAC,GAAA;EAClB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtH,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuH,cAAc,EAAEC,iBAAiB,CAAC,GAAGxH,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyH,eAAe,EAAEC,kBAAkB,CAAC,GAAG1H,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2H,QAAQ,EAAEC,WAAW,CAAC,GAAG5H,QAAQ,CAAC;IACvCqF,IAAI,EAAE,EAAE;IACRwC,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,WAAW;IAC9BvB,QAAQ,EAAE,KAAK;IACfE,QAAQ,EAAE,OAAO;IACjBsB,aAAa,EAAE,KAAK;IACpBvB,UAAU,EAAE,UAAU;IACtBwB,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGhF,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEiF;EAAY,CAAC,GAAG9E,MAAM,CAAC,CAAC;;EAEhC;EACA,MAAM;IAAE+E,IAAI,EAAEC,YAAY;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAGvF,QAAQ,CACvD,UAAU,EACVI,WAAW,CAACoF,MAAM,EAClB;IACEC,SAAS,EAAGL,IAAI,IAAK;MACnBD,WAAW,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IAC9B,CAAC;IACDM,OAAO,EAAGH,KAAK,IAAK;MAClBpF,KAAK,CAACoF,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CACF,CAAC;;EAED;EACA,MAAMI,cAAc,GAAG1F,WAAW,CAACG,WAAW,CAACwF,MAAM,EAAE;IACrDH,SAAS,EAAEA,CAAA,KAAM;MACfP,WAAW,CAACW,iBAAiB,CAAC,UAAU,CAAC;MACzC1B,mBAAmB,CAAC,KAAK,CAAC;MAC1B2B,SAAS,CAAC,CAAC;MACX3F,KAAK,CAAC4F,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGH,KAAK,IAAK;MAAA,IAAAS,eAAA,EAAAC,oBAAA;MAClBC,OAAO,CAACX,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMY,YAAY,GAAG,CAAAZ,KAAK,aAALA,KAAK,wBAAAS,eAAA,GAALT,KAAK,CAAEa,QAAQ,cAAAJ,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAiBZ,IAAI,cAAAa,oBAAA,uBAArBA,oBAAA,CAAuBI,MAAM,MAC9Bd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEe,OAAO,KACd,0BAA0B;MAC9CnG,KAAK,CAACoF,KAAK,CAACY,YAAY,CAAC;IAC3B;EACF,CAAC,CAAC;;EAEF;EACA,MAAMI,cAAc,GAAGtG,WAAW,CAChC,CAAC;IAAEuG,EAAE;IAAEpB;EAAK,CAAC,KAAKhF,WAAW,CAACqG,MAAM,CAACD,EAAE,EAAEpB,IAAI,CAAC,EAC9C;IACEK,SAAS,EAAEA,CAAA,KAAM;MACfP,WAAW,CAACW,iBAAiB,CAAC,UAAU,CAAC;MACzCxB,iBAAiB,CAAC,KAAK,CAAC;MACxByB,SAAS,CAAC,CAAC;MACX3F,KAAK,CAAC4F,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGH,KAAK,IAAK;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MAClBT,OAAO,CAACX,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMY,YAAY,GAAG,CAAAZ,KAAK,aAALA,KAAK,wBAAAmB,gBAAA,GAALnB,KAAK,CAAEa,QAAQ,cAAAM,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBtB,IAAI,cAAAuB,qBAAA,uBAArBA,qBAAA,CAAuBN,MAAM,MAC9Bd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEe,OAAO,KACd,0BAA0B;MAC9CnG,KAAK,CAACoF,KAAK,CAACY,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;;EAED;EACA,MAAMS,cAAc,GAAG3G,WAAW,CAACG,WAAW,CAACyG,MAAM,EAAE;IACrDpB,SAAS,EAAEA,CAAA,KAAM;MACfP,WAAW,CAACW,iBAAiB,CAAC,UAAU,CAAC;MACzC1F,KAAK,CAAC4F,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGH,KAAK,IAAK;MAClBpF,KAAK,CAACoF,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,CAAC;;EAEF;EACA,MAAMuB,YAAY,GAAG7G,WAAW,CAC7B8G,SAAS,IAAK3G,WAAW,CAAC4G,WAAW,CAACD,SAAS,CAAC,EACjD;IACEtB,SAAS,EAAEA,CAAA,KAAM;MACftF,KAAK,CAAC4F,OAAO,CAAC,qCAAqC,CAAC;IACtD,CAAC;IACDL,OAAO,EAAGH,KAAK,IAAK;MAClBpF,KAAK,CAACoF,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CACF,CAAC;EAED,MAAM0B,QAAQ,GAAGC,KAAK,CAACC,OAAO,CAAC9B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE4B,QAAQ,CAAC,GAClD5B,YAAY,CAAC4B,QAAQ,GACrBC,KAAK,CAACC,OAAO,CAAC9B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAED,IAAI,CAAC,GACjCC,YAAY,CAACD,IAAI,GACjB8B,KAAK,CAACC,OAAO,CAAC9B,YAAY,CAAC,GAC3BA,YAAY,GACZ,EAAE;EAENa,OAAO,CAACkB,GAAG,CAAC,gBAAgB,EAAE/B,YAAY,CAAC;EAC3Ca,OAAO,CAACkB,GAAG,CAAC,qBAAqB,EAAEH,QAAQ,CAAC;EAE5C,MAAMnB,SAAS,GAAGA,CAAA,KAAM;IACtBrB,WAAW,CAAC;MACVvC,IAAI,EAAE,EAAE;MACRwC,YAAY,EAAE,QAAQ;MACtBC,UAAU,EAAE,EAAE;MACdC,iBAAiB,EAAE,WAAW;MAC9BvB,QAAQ,EAAE,KAAK;MACfE,QAAQ,EAAE,OAAO;MACjBsB,aAAa,EAAE,KAAK;MACpBvB,UAAU,EAAE,UAAU;MACtBwB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFV,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM8C,YAAY,GAAGA,CAAA,KAAM;IACzBlD,mBAAmB,CAAC,IAAI,CAAC;IACzB2B,SAAS,CAAC,CAAC;EACb,CAAC;EAED,MAAMwB,UAAU,GAAI3G,OAAO,IAAK;IAC9B4D,kBAAkB,CAAC5D,OAAO,CAAC;IAE3B8D,WAAW,CAAC;MACVvC,IAAI,EAAEvB,OAAO,CAACuB,IAAI,IAAI,EAAE;MACxBwC,YAAY,EAAE,QAAQ;MAAE;MACxBC,UAAU,EAAEhE,OAAO,CAACgE,UAAU,IAAI,EAAE;MACpCC,iBAAiB,EAAEjE,OAAO,CAACiE,iBAAiB,IAAI,WAAW;MAC3DvB,QAAQ,EAAE1C,OAAO,CAAC0C,QAAQ,IAAI,KAAK;MACnCE,QAAQ,EAAE5C,OAAO,CAAC4C,QAAQ,IAAI,OAAO;MACrCsB,aAAa,EAAElE,OAAO,CAAC2C,UAAU,IAAI3C,OAAO,CAAC2C,UAAU,KAAK,UAAU;MACtEA,UAAU,EAAE3C,OAAO,CAAC2C,UAAU,IAAI,UAAU;MAC5CwB,UAAU,EAAEnE,OAAO,CAACmE,UAAU,IAAI,EAAE;MACpCC,UAAU,EAAEpE,OAAO,CAACoE,UAAU,GAAGpE,OAAO,CAACoE,UAAU,CAACwC,QAAQ,CAAC,CAAC,GAAG,EAAE;MACnEvC,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE,CAAE;IACtB,CAAC,CAAC;IACFZ,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMmD,YAAY,GAAI7G,OAAO,IAAK;IAChC,IAAI8G,MAAM,CAACC,OAAO,CAAC,4CAA4C/G,OAAO,CAACuB,IAAI,IAAI,CAAC,EAAE;MAChF0E,cAAc,CAACe,MAAM,CAAChH,OAAO,CAAC6F,EAAE,CAAC;IACnC;EACF,CAAC;EAED,MAAMoB,UAAU,GAAIjH,OAAO,IAAK;IAC9BmG,YAAY,CAACa,MAAM,CAAChH,OAAO,CAAC6F,EAAE,CAAC;EACjC,CAAC;EAED,MAAMqB,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAMC,UAAU,GAAG;MAAE,GAAGtD;IAAS,CAAC;;IAElC;IACA,IAAIsD,UAAU,CAAChD,UAAU,KAAK,EAAE,EAAEgD,UAAU,CAAChD,UAAU,GAAG,IAAI;IAC9D,IAAIgD,UAAU,CAAC/C,UAAU,KAAK,EAAE,EAAE+C,UAAU,CAAC/C,UAAU,GAAG,IAAI;IAC9D,IAAI+C,UAAU,CAAC9C,cAAc,KAAK,EAAE,EAAE8C,UAAU,CAAC9C,cAAc,GAAG,IAAI;IACtE,IAAI8C,UAAU,CAAC7C,cAAc,KAAK,EAAE,EAAE6C,UAAU,CAAC7C,cAAc,GAAG,IAAI;IACtE,IAAI6C,UAAU,CAACnD,UAAU,KAAK,EAAE,EAAEmD,UAAU,CAACnD,UAAU,GAAG,IAAI;;IAE9D;IACA,IAAImD,UAAU,CAAC/C,UAAU,EAAE;MACzB,MAAMgD,IAAI,GAAGC,QAAQ,CAACF,UAAU,CAAC/C,UAAU,CAAC;MAC5C,IAAIkD,KAAK,CAACF,IAAI,CAAC,EAAE;QACf5H,KAAK,CAACoF,KAAK,CAAC,mCAAmC,CAAC;QAChD;MACF;MACAuC,UAAU,CAAC/C,UAAU,GAAGgD,IAAI;IAC9B;;IAEA;IACA,IAAIvD,QAAQ,CAAClB,UAAU,KAAK,UAAU,EAAE;MACtC,IAAI,CAACkB,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,EAAE;QAChD5E,KAAK,CAACoF,KAAK,CAAC,wDAAwD,CAAC;QACrE;MACF;MAEA,MAAMwC,IAAI,GAAGC,QAAQ,CAACxD,QAAQ,CAACO,UAAU,CAAC;MAC1C,IAAIkD,KAAK,CAACF,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,KAAK,EAAE;QAC3C5H,KAAK,CAACoF,KAAK,CAAC,uDAAuD,CAAC;QACpE;MACF;IACF;;IAEA;IACA,MAAM2C,SAAS,GAAG;MAChBhG,IAAI,EAAE4F,UAAU,CAAC5F,IAAI;MACrByC,UAAU,EAAEmD,UAAU,CAACnD,UAAU;MACjCwD,cAAc,EAAE;QACdzD,YAAY,EAAEoD,UAAU,CAACpD,YAAY,IAAI,QAAQ;QACjDE,iBAAiB,EAAEkD,UAAU,CAAClD,iBAAiB,IAAI,WAAW;QAC9DvB,QAAQ,EAAEyE,UAAU,CAACzE,QAAQ,IAAI,KAAK;QACtCE,QAAQ,EAAEuE,UAAU,CAACvE,QAAQ,IAAI;MACnC,CAAC;MACD6E,YAAY,EAAEN,UAAU,CAACxE,UAAU,KAAK,UAAU,GAAG;QACnDA,UAAU,EAAEwE,UAAU,CAACxE,UAAU;QACjC+E,IAAI,EAAEP,UAAU,CAAChD,UAAU;QAC3BiD,IAAI,EAAED,UAAU,CAAC/C,UAAU;QAC3BuD,QAAQ,EAAER,UAAU,CAAC9C,cAAc;QACnCuD,QAAQ,EAAET,UAAU,CAAC7C;MACvB,CAAC,GAAG;IACN,CAAC;IAED,IAAIX,eAAe,EAAE;MACnBiC,cAAc,CAACoB,MAAM,CAAC;QAAEnB,EAAE,EAAElC,eAAe,CAACkC,EAAE;QAAEpB,IAAI,EAAE8C;MAAU,CAAC,CAAC;IACpE,CAAC,MAAM;MACLvC,cAAc,CAACgC,MAAM,CAACO,SAAS,CAAC;IAClC;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzCjE,WAAW,CAACkE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAIxD,IAAI,IAAK;IACjC,MAAMyD,SAAS,GAAG;MAAE,GAAGzD;IAAK,CAAC;;IAE7B;IACA0D,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;MACpC,IAAIJ,SAAS,CAACI,GAAG,CAAC,KAAK,EAAE,EAAE;QACzBJ,SAAS,CAACI,GAAG,CAAC,GAAG,IAAI;MACvB;IACF,CAAC,CAAC;;IAEF;IACA,IAAIJ,SAAS,CAAC9D,UAAU,IAAI,OAAO8D,SAAS,CAAC9D,UAAU,KAAK,QAAQ,EAAE;MACpE,MAAMgD,IAAI,GAAGC,QAAQ,CAACa,SAAS,CAAC9D,UAAU,CAAC;MAC3C8D,SAAS,CAAC9D,UAAU,GAAGkD,KAAK,CAACF,IAAI,CAAC,GAAG,IAAI,GAAGA,IAAI;IAClD;IAEA,OAAOc,SAAS;EAClB,CAAC;EAED,IAAItD,KAAK,EAAE;IACT,oBACEhF,OAAA,CAACzD,GAAG;MAACoM,SAAS,EAAC,SAAS;MAAAxH,QAAA,eACtBnB,OAAA,CAACjC,KAAK;QAAC6K,QAAQ,EAAC,OAAO;QAAC3H,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAEvC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACEjC,OAAA,CAACzD,GAAG;IAACoM,SAAS,EAAC,SAAS;IAAAxH,QAAA,gBACtBnB,OAAA,CAACzD,GAAG;MAAC0E,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFnB,OAAA,CAACxD,UAAU;QAAC0F,OAAO,EAAC,IAAI;QAACjB,EAAE,EAAE;UAAEkB,UAAU,EAAE;QAAI,CAAE;QAAAhB,QAAA,EAAC;MAElD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjC,OAAA,CAACzD,GAAG;QAAC0E,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBACnCnB,OAAA,CAACrD,MAAM;UACLuF,OAAO,EAAC,UAAU;UAClBiB,SAAS,eAAEnD,OAAA,CAAChB,WAAW;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BM,OAAO,EAAEA,CAAA,KAAMoC,WAAW,CAACW,iBAAiB,CAAC,UAAU,CAAE;UACzDrE,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA,CAACrD,MAAM;UACLuF,OAAO,EAAC,WAAW;UACnBiB,SAAS,eAAEnD,OAAA,CAAC5B,OAAO;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAEuE,YAAa;UACtB7F,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL8C,SAAS,iBAAI/E,OAAA,CAAChC,cAAc;MAACiD,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE;IAAE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG/CjC,OAAA,CAACzD,GAAG;MAAC0E,EAAE,EAAE;QAAEM,EAAE,EAAE,CAAC;QAAEsH,CAAC,EAAE,CAAC;QAAErH,OAAO,EAAE,UAAU;QAAEsH,YAAY,EAAE;MAAE,CAAE;MAAA3H,QAAA,gBAC7DnB,OAAA,CAACxD,UAAU;QAAC0F,OAAO,EAAC,SAAS;QAAAf,QAAA,GAAC,2BACH,EAACuF,QAAQ,CAACqC,MAAM,EAAC,gBAAc,EAAChE,SAAS,CAACiC,QAAQ,CAAC,CAAC;MAAA;QAAAlF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACbjC,OAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNjC,OAAA,CAACxD,UAAU;QAAC0F,OAAO,EAAC,SAAS;QAAAf,QAAA,GAAC,iBACb,EAAC,OAAO2D,YAAY,EAAC,WAAS,EAAC6B,KAAK,CAACC,OAAO,CAAC9B,YAAY,CAAC,CAACkC,QAAQ,CAAC,CAAC;MAAA;QAAAlF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELyE,QAAQ,CAACqC,MAAM,KAAK,CAAC,IAAI,CAAChE,SAAS,gBAClC/E,OAAA,CAACvD,IAAI;MAAA0E,QAAA,eACHnB,OAAA,CAACtD,WAAW;QAACuE,EAAE,EAAE;UAAE+H,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAA9H,QAAA,gBAC9CnB,OAAA,CAAC1B,UAAU;UAAC2C,EAAE,EAAE;YAAE4B,QAAQ,EAAE,EAAE;YAAEP,KAAK,EAAE,gBAAgB;YAAEf,EAAE,EAAE;UAAE;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpEjC,OAAA,CAACxD,UAAU;UAAC0F,OAAO,EAAC,IAAI;UAACjB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAExC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjC,OAAA,CAACxD,UAAU;UAAC0F,OAAO,EAAC,OAAO;UAACI,KAAK,EAAC,gBAAgB;UAACrB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjC,OAAA,CAACrD,MAAM;UACLuF,OAAO,EAAC,WAAW;UACnBiB,SAAS,eAAEnD,OAAA,CAAC5B,OAAO;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAEuE,YAAa;UACtB7F,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEPjC,OAAA,CAACpD,IAAI;MAAC6F,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvB,QAAA,EACxBuF,QAAQ,CAACwC,GAAG,CAAE9I,OAAO,iBACpBJ,OAAA,CAACpD,IAAI;QAAC+F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACuG,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAjI,QAAA,eAC9BnB,OAAA,CAACG,WAAW;UACVC,OAAO,EAAEA,OAAQ;UACjBC,MAAM,EAAE0G,UAAW;UACnBzG,QAAQ,EAAE2G,YAAa;UACvB1G,MAAM,EAAE8G;QAAW;UAAAvF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC,GANkC7B,OAAO,CAAC6F,EAAE;QAAAnE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAO1C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDjC,OAAA,CAACjD,MAAM;MACLsG,IAAI,EAAEM,gBAAgB,IAAIE,cAAe;MACzCN,OAAO,EAAEA,CAAA,KAAM;QACbK,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,iBAAiB,CAAC,KAAK,CAAC;QACxByB,SAAS,CAAC,CAAC;MACb,CAAE;MACF8D,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAnI,QAAA,gBAETnB,OAAA,CAAChD,WAAW;QAAAmE,QAAA,EACT4C,eAAe,GAAG,cAAc,GAAG;MAAoB;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACdjC,OAAA,CAAC/C,aAAa;QAAAkE,QAAA,eACZnB,OAAA,CAACzD,GAAG;UAAC0E,EAAE,EAAE;YAAEsI,EAAE,EAAE;UAAE,CAAE;UAAApI,QAAA,eACjBnB,OAAA,CAACpD,IAAI;YAAC6F,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAvB,QAAA,gBACzBnB,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBnB,OAAA,CAAC7C,SAAS;gBACRmM,SAAS;gBACTlH,KAAK,EAAC,cAAc;gBACpB+F,KAAK,EAAElE,QAAQ,CAACtC,IAAK;gBACrB6H,QAAQ,EAAGC,CAAC,IAAKxB,gBAAgB,CAAC,MAAM,EAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;gBAC1DwB,QAAQ;cAAA;gBAAA7H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACuG,EAAE,EAAE,CAAE;cAAAhI,QAAA,eACvBnB,OAAA,CAAC5C,WAAW;gBAACkM,SAAS;gBAAAnI,QAAA,gBACpBnB,OAAA,CAAC3C,UAAU;kBAAA8D,QAAA,EAAC;gBAAY;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCjC,OAAA,CAAC1C,MAAM;kBACL6K,KAAK,EAAElE,QAAQ,CAACE,YAAa;kBAC7BqF,QAAQ,EAAGC,CAAC,IAAKxB,gBAAgB,CAAC,cAAc,EAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;kBAClE/F,KAAK,EAAC,cAAc;kBAAAjB,QAAA,gBAEpBnB,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,QAAQ;oBAAAhH,QAAA,EAAC;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,SAAS;oBAAAhH,QAAA,EAAC;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5CjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,MAAM;oBAAAhH,QAAA,EAAC;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACuG,EAAE,EAAE,CAAE;cAAAhI,QAAA,eACvBnB,OAAA,CAAC5C,WAAW;gBAACkM,SAAS;gBAAAnI,QAAA,gBACpBnB,OAAA,CAAC3C,UAAU;kBAAA8D,QAAA,EAAC;gBAAiB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CjC,OAAA,CAAC1C,MAAM;kBACL6K,KAAK,EAAElE,QAAQ,CAACI,iBAAkB;kBAClCmF,QAAQ,EAAGC,CAAC,IAAKxB,gBAAgB,CAAC,mBAAmB,EAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;kBACvE/F,KAAK,EAAC,mBAAmB;kBAAAjB,QAAA,gBAEzBnB,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,WAAW;oBAAAhH,QAAA,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,UAAU;oBAAAhH,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,UAAU;oBAAAhH,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,UAAU;oBAAAhH,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACuG,EAAE,EAAE,CAAE;cAAAhI,QAAA,eACvBnB,OAAA,CAAC5C,WAAW;gBAACkM,SAAS;gBAAAnI,QAAA,gBACpBnB,OAAA,CAAC3C,UAAU;kBAAA8D,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCjC,OAAA,CAAC1C,MAAM;kBACL6K,KAAK,EAAElE,QAAQ,CAACnB,QAAS;kBACzB0G,QAAQ,EAAGC,CAAC,IAAKxB,gBAAgB,CAAC,UAAU,EAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;kBAC9D/F,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhBnB,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,KAAK;oBAAAhH,QAAA,EAAC;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpCjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,kBAAkB;oBAAAhH,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9DjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,kBAAkB;oBAAAhH,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9DjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,eAAe;oBAAAhH,QAAA,EAAC;kBAAa;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACuG,EAAE,EAAE,CAAE;cAAAhI,QAAA,eACvBnB,OAAA,CAAC5C,WAAW;gBAACkM,SAAS;gBAAAnI,QAAA,gBACpBnB,OAAA,CAAC3C,UAAU;kBAAA8D,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCjC,OAAA,CAAC1C,MAAM;kBACL6K,KAAK,EAAElE,QAAQ,CAACjB,QAAS;kBACzBwG,QAAQ,EAAGC,CAAC,IAAKxB,gBAAgB,CAAC,UAAU,EAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;kBAC9D/F,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhBnB,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,OAAO;oBAAAhH,QAAA,EAAC;kBAAY;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC/CjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,OAAO;oBAAAhH,QAAA,EAAC;kBAAU;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC7CjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,OAAO;oBAAAhH,QAAA,EAAC;kBAAoB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACvDjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,OAAO;oBAAAhH,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBnB,OAAA,CAAC7C,SAAS;gBACRmM,SAAS;gBACTlH,KAAK,EAAC,uBAAuB;gBAC7B+F,KAAK,EAAElE,QAAQ,CAACG,UAAW;gBAC3BoF,QAAQ,EAAGC,CAAC,IAAKxB,gBAAgB,CAAC,YAAY,EAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;gBAChEyB,WAAW,EAAC;cAAsC;gBAAA9H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChBnB,OAAA,CAACxD,UAAU;gBAAC0F,OAAO,EAAC,IAAI;gBAACjB,EAAE,EAAE;kBAAEM,EAAE,EAAE,CAAC;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAAC;cAEzD;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEPjC,OAAA,CAACpD,IAAI;cAAC+F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACuG,EAAE,EAAE,CAAE;cAAAhI,QAAA,eACvBnB,OAAA,CAAC5C,WAAW;gBAACkM,SAAS;gBAAAnI,QAAA,gBACpBnB,OAAA,CAAC3C,UAAU;kBAAA8D,QAAA,EAAC;gBAAU;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCjC,OAAA,CAAC1C,MAAM;kBACL6K,KAAK,EAAElE,QAAQ,CAAClB,UAAW;kBAC3ByG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMI,SAAS,GAAGJ,CAAC,CAACC,MAAM,CAACvB,KAAK;oBAChCF,gBAAgB,CAAC,YAAY,EAAE4B,SAAS,CAAC;oBACzC5B,gBAAgB,CAAC,eAAe,EAAE4B,SAAS,KAAK,UAAU,CAAC;kBAC7D,CAAE;kBACFzH,KAAK,EAAC,YAAY;kBAAAjB,QAAA,gBAElBnB,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,UAAU;oBAAAhH,QAAA,eACxBnB,OAAA,CAACzD,GAAG;sBAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAwB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjEjC,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,MAAM;oBAAAhH,QAAA,eACpBnB,OAAA,CAACzD,GAAG;sBAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC7CjC,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,OAAO;oBAAAhH,QAAA,eACrBnB,OAAA,CAACzD,GAAG;sBAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAK;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC9CjC,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,QAAQ;oBAAAhH,QAAA,eACtBnB,OAAA,CAACzD,GAAG;sBAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAM;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC/CjC,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXjC,OAAA,CAACzC,QAAQ;oBAAC4K,KAAK,EAAC,KAAK;oBAAAhH,QAAA,eACnBnB,OAAA,CAACzD,GAAG;sBAAA4E,QAAA,gBACFnB,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAG;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC5CjC,OAAA,CAACxD,UAAU;wBAAC0F,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACTjC,OAAA,CAAC9B,cAAc;kBAAAiD,QAAA,GACZ8C,QAAQ,CAAClB,UAAU,KAAK,UAAU,IAAI,yBAAyB,EAC/DkB,QAAQ,CAAClB,UAAU,KAAK,MAAM,IAAI,iCAAiC,EACnEkB,QAAQ,CAAClB,UAAU,KAAK,OAAO,IAAI,sCAAsC,EACzEkB,QAAQ,CAAClB,UAAU,KAAK,QAAQ,IAAI,wCAAwC,EAC5EkB,QAAQ,CAAClB,UAAU,KAAK,KAAK,IAAI,4CAA4C;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAENgC,QAAQ,CAAClB,UAAU,KAAK,UAAU,iBACjC/C,OAAA,CAAAE,SAAA;cAAAiB,QAAA,gBACEnB,OAAA,CAACpD,IAAI;gBAAC+F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACuG,EAAE,EAAE,CAAE;gBAAAhI,QAAA,eACvBnB,OAAA,CAAC7C,SAAS;kBACRmM,SAAS;kBACTlH,KAAK,EAAC,YAAY;kBAClB+F,KAAK,EAAElE,QAAQ,CAACM,UAAW;kBAC3BiF,QAAQ,EAAGC,CAAC,IAAKxB,gBAAgB,CAAC,YAAY,EAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;kBAChEwB,QAAQ;kBACRC,WAAW,EACT3F,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC,0CACL;kBACD+G,UAAU,EAAC;gBAA+C;kBAAAhI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPjC,OAAA,CAACpD,IAAI;gBAAC+F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACuG,EAAE,EAAE,CAAE;gBAAAhI,QAAA,eACvBnB,OAAA,CAAC7C,SAAS;kBACRmM,SAAS;kBACTlH,KAAK,EAAC,YAAY;kBAClB+F,KAAK,EAAElE,QAAQ,CAACO,UAAW;kBAC3BgF,QAAQ,EAAGC,CAAC,IAAKxB,gBAAgB,CAAC,YAAY,EAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;kBAChE4B,IAAI,EAAC,QAAQ;kBACbJ,QAAQ;kBACRC,WAAW,EACT3F,QAAQ,CAAClB,UAAU,KAAK,MAAM,GAAG,kBAAkB,GACnDkB,QAAQ,CAAClB,UAAU,KAAK,OAAO,GAAG,iBAAiB,GACnDkB,QAAQ,CAAClB,UAAU,KAAK,QAAQ,GAAG,kBAAkB,GACrDkB,QAAQ,CAAClB,UAAU,KAAK,KAAK,GAAG,UAAU,GAAG,aAC9C;kBACD+G,UAAU,EAAE,UAAU7F,QAAQ,CAAClB,UAAU,CAAClB,WAAW,CAAC,CAAC,QAAS;kBAChEmI,UAAU,EAAE;oBAAEC,GAAG,EAAE,CAAC;oBAAEC,GAAG,EAAE;kBAAM;gBAAE;kBAAApI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPjC,OAAA,CAACpD,IAAI;gBAAC+F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACuG,EAAE,EAAE,CAAE;gBAAAhI,QAAA,eACvBnB,OAAA,CAAC7C,SAAS;kBACRmM,SAAS;kBACTlH,KAAK,EAAE6B,QAAQ,CAAClB,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEoF,KAAK,EAAElE,QAAQ,CAACQ,cAAe;kBAC/B+E,QAAQ,EAAGC,CAAC,IAAKxB,gBAAgB,CAAC,gBAAgB,EAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;kBACpEyB,WAAW,EACT3F,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,iCAAiC,GACjC,2CACL;kBACD+G,UAAU,EACR7F,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,8BAA8B,GAC9B;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPjC,OAAA,CAACpD,IAAI;gBAAC+F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACuG,EAAE,EAAE,CAAE;gBAAAhI,QAAA,eACvBnB,OAAA,CAAC7C,SAAS;kBACRmM,SAAS;kBACTlH,KAAK,EAAE6B,QAAQ,CAAClB,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEoF,KAAK,EAAElE,QAAQ,CAACS,cAAe;kBAC/B8E,QAAQ,EAAGC,CAAC,IAAKxB,gBAAgB,CAAC,gBAAgB,EAAEwB,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;kBACpE4B,IAAI,EAAC,UAAU;kBACfH,WAAW,EACT3F,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,gDAAgD,GAChD,2CACL;kBACD+G,UAAU,EACR7F,QAAQ,CAAClB,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPjC,OAAA,CAACpD,IAAI;gBAAC+F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAzB,QAAA,eAChBnB,OAAA,CAACzD,GAAG;kBAAC0E,EAAE,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAE8B,GAAG,EAAE,CAAC;oBAAE5B,UAAU,EAAE;kBAAS,CAAE;kBAAAH,QAAA,gBACzDnB,OAAA,CAACrD,MAAM;oBACLuF,OAAO,EAAC,UAAU;oBAClBG,IAAI,EAAC,OAAO;oBACZE,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAIwB,eAAe,EAAE;wBACnBsD,UAAU,CAACtD,eAAe,CAAC;sBAC7B,CAAC,MAAM;wBACLnE,KAAK,CAACuK,IAAI,CAAC,iDAAiD,CAAC;sBAC/D;oBACF,CAAE;oBACFC,QAAQ,EAAE,CAACnG,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAW;oBACvDvD,EAAE,EAAE;sBAAEmC,aAAa,EAAE;oBAAO,CAAE;oBAAAjC,QAAA,EAC/B;kBAED;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTjC,OAAA,CAACxD,UAAU;oBAAC0F,OAAO,EAAC,SAAS;oBAACI,KAAK,EAAC,gBAAgB;oBAAAnB,QAAA,EAAC;kBAErD;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,eACP,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBjC,OAAA,CAAC9C,aAAa;QAAAiE,QAAA,gBACZnB,OAAA,CAACrD,MAAM;UACL4F,OAAO,EAAEA,CAAA,KAAM;YACbqB,mBAAmB,CAAC,KAAK,CAAC;YAC1BE,iBAAiB,CAAC,KAAK,CAAC;YACxByB,SAAS,CAAC,CAAC;UACb,CAAE;UAAApE,QAAA,EACH;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjC,OAAA,CAACrD,MAAM;UACLuF,OAAO,EAAC,WAAW;UACnBK,OAAO,EAAE+E,YAAa;UACtB8C,QAAQ,EAAE,CAACnG,QAAQ,CAACtC,IAAI,IAAIyD,cAAc,CAACL,SAAS,IAAIiB,cAAc,CAACjB,SAAU;UAAA5D,QAAA,EAEhF4C,eAAe,GAAG,QAAQ,GAAG;QAAQ;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACyB,GAAA,CA5mBQD,QAAQ;EAAA,QAmBK9D,cAAc,EACVG,MAAM,EAGmBL,QAAQ,EAclCC,WAAW,EAiBXA,WAAW,EAoBXA,WAAW,EAWbA,WAAW;AAAA;AAAA2K,GAAA,GArFzB5G,QAAQ;AA8mBjB,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAA6G,GAAA;AAAAC,YAAA,CAAA9G,EAAA;AAAA8G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}