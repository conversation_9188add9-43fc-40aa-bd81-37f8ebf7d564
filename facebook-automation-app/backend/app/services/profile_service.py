"""
Profile service for advanced profile management with browser fingerprinting.
"""
import json
import random
import secrets
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from loguru import logger

from ..core.config import settings
from ..core.security import security_manager
from ..models.profile import Profile, ProxyConfig, ProfileStatus, ProxyType


class ProfileService:
    """
    Advanced profile service with browser fingerprinting and anti-detection.
    """
    
    def __init__(self):
        self.profiles_dir = settings.profiles_dir
    
    async def create_profile(
        self,
        db: AsyncSession,
        name: str,
        proxy_config: Optional[Dict[str, Any]] = None,
        custom_user_agent: Optional[str] = None,
        browser_fingerprint: Optional[Dict[str, Any]] = None
    ) -> Profile:
        """Create new profile with advanced fingerprinting."""
        
        # Check name uniqueness
        existing = await db.execute(select(Profile).where(Profile.name == name))
        if existing.scalar_one_or_none():
            raise ValueError(f"Profile name '{name}' already exists")
        
        # Generate browser fingerprint
        fingerprint = browser_fingerprint or self._generate_browser_fingerprint()
        
        # Create profile
        profile = Profile(
            name=name,
            status=ProfileStatus.ACTIVE,
            user_agent=custom_user_agent or fingerprint.get("user_agent"),
            browser_config=json.dumps(fingerprint)
        )
        
        db.add(profile)
        await db.flush()  # Get profile ID
        
        # Create profile directory
        profile_dir = self.profiles_dir / f"profile_{profile.id}"
        profile_dir.mkdir(exist_ok=True)
        profile.profile_path = str(profile_dir)
        
        # Create proxy config if provided
        if proxy_config:
            await self._create_proxy_config(db, profile.id, proxy_config)
        
        await db.commit()
        
        logger.info(f"Created profile '{name}' with ID {profile.id}")
        return profile
    
    async def get_profile_with_config(
        self, 
        db: AsyncSession, 
        profile_id: int
    ) -> Optional[Profile]:
        """Get profile with all related configurations."""
        query = select(Profile).options(
            selectinload(Profile.proxy_config)
        ).where(Profile.id == profile_id)
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def list_profiles(
        self,
        db: AsyncSession,
        status: Optional[ProfileStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Profile]:
        """List profiles with filtering."""
        query = select(Profile).options(selectinload(Profile.proxy_config))
        
        if status:
            query = query.where(Profile.status == status)
        
        query = query.offset(skip).limit(limit).order_by(Profile.created_at.desc())
        
        result = await db.execute(query)
        return list(result.scalars().all())
    
    async def update_profile(
        self,
        db: AsyncSession,
        profile_id: int,
        updates: Dict[str, Any]
    ) -> Optional[Profile]:
        """Update profile with validation."""
        profile = await self.get_profile_with_config(db, profile_id)
        if not profile:
            return None
        
        # Handle name update with uniqueness check
        if "name" in updates and updates["name"] != profile.name:
            existing = await db.execute(
                select(Profile).where(
                    Profile.name == updates["name"],
                    Profile.id != profile_id
                )
            )
            if existing.scalar_one_or_none():
                raise ValueError(f"Profile name '{updates['name']}' already exists")
        
        # Update basic fields
        for field in ["name", "status", "user_agent"]:
            if field in updates:
                setattr(profile, field, updates[field])
        
        # Update browser config
        if "browser_config" in updates:
            current_config = profile.get_browser_config()
            current_config.update(updates["browser_config"])
            profile.set_browser_config(current_config)
        
        # Update proxy config
        if "proxy_config" in updates:
            await self._update_proxy_config(db, profile, updates["proxy_config"])
        
        profile.updated_at = datetime.utcnow()
        await db.commit()
        
        logger.info(f"Updated profile {profile_id}")
        return profile
    
    async def delete_profile(self, db: AsyncSession, profile_id: int) -> bool:
        """Delete profile and cleanup resources."""
        profile = await db.get(Profile, profile_id)
        if not profile:
            return False
        
        # Cleanup profile directory
        if profile.profile_path:
            profile_dir = Path(profile.profile_path)
            if profile_dir.exists():
                import shutil
                shutil.rmtree(profile_dir, ignore_errors=True)
                logger.info(f"Cleaned up profile directory: {profile_dir}")
        
        await db.delete(profile)
        await db.commit()
        
        logger.info(f"Deleted profile {profile_id}")
        return True
    
    async def update_usage_stats(
        self,
        db: AsyncSession,
        profile_id: int,
        login_increment: int = 0,
        message_increment: int = 0
    ):
        """Update profile usage statistics."""
        await db.execute(
            update(Profile)
            .where(Profile.id == profile_id)
            .values(
                login_count=Profile.login_count + login_increment,
                message_sent_count=Profile.message_sent_count + message_increment,
                last_used_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        )
        await db.commit()
    
    def _generate_browser_fingerprint(self) -> Dict[str, Any]:
        """Generate realistic browser fingerprint for anti-detection."""
        
        # Common browser versions and OS combinations
        browsers = [
            {
                "name": "Chrome",
                "versions": ["120.0.6099.109", "120.0.6099.71", "119.0.6045.199"],
                "engines": ["537.36"]
            },
            {
                "name": "Firefox", 
                "versions": ["121.0", "120.0.1", "119.0"],
                "engines": ["20100101"]
            }
        ]
        
        operating_systems = [
            {
                "name": "Windows",
                "versions": ["Windows NT 10.0; Win64; x64", "Windows NT 10.0; WOW64"],
                "platforms": ["Win32", "Win64"]
            },
            {
                "name": "macOS",
                "versions": ["Intel Mac OS X 10_15_7", "Intel Mac OS X 11_7_10"],
                "platforms": ["MacIntel"]
            },
            {
                "name": "Linux",
                "versions": ["X11; Linux x86_64", "X11; Ubuntu; Linux x86_64"],
                "platforms": ["Linux x86_64"]
            }
        ]
        
        # Select random combinations
        browser = random.choice(browsers)
        os_info = random.choice(operating_systems)
        browser_version = random.choice(browser["versions"])
        os_version = random.choice(os_info["versions"])
        
        # Generate user agent
        if browser["name"] == "Chrome":
            user_agent = f"Mozilla/5.0 ({os_version}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_version} Safari/537.36"
        else:  # Firefox
            user_agent = f"Mozilla/5.0 ({os_version}; rv:{browser_version}) Gecko/{random.choice(browser['engines'])} Firefox/{browser_version}"
        
        # Generate screen resolution
        resolutions = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1536, "height": 864},
            {"width": 1440, "height": 900},
            {"width": 1280, "height": 720}
        ]
        resolution = random.choice(resolutions)
        
        # Generate timezone
        timezones = [
            "America/New_York", "America/Los_Angeles", "Europe/London",
            "Europe/Paris", "Asia/Tokyo", "Asia/Shanghai", "Australia/Sydney"
        ]
        
        # Generate languages
        languages = [
            ["en-US", "en"],
            ["en-GB", "en"],
            ["fr-FR", "fr"],
            ["de-DE", "de"],
            ["es-ES", "es"],
            ["ja-JP", "ja"],
            ["zh-CN", "zh"]
        ]
        
        fingerprint = {
            "user_agent": user_agent,
            "browser": {
                "name": browser["name"],
                "version": browser_version,
                "engine": random.choice(browser["engines"])
            },
            "os": {
                "name": os_info["name"],
                "version": os_version,
                "platform": random.choice(os_info["platforms"])
            },
            "screen": {
                "width": resolution["width"],
                "height": resolution["height"],
                "color_depth": random.choice([24, 32]),
                "pixel_ratio": random.choice([1, 1.25, 1.5, 2])
            },
            "timezone": random.choice(timezones),
            "languages": random.choice(languages),
            "webgl": {
                "vendor": random.choice(["Google Inc.", "Mozilla", "Apple Inc."]),
                "renderer": f"ANGLE (Intel, Intel(R) HD Graphics {random.randint(4000, 6000)} Direct3D11 vs_5_0 ps_5_0)"
            },
            "canvas_fingerprint": secrets.token_hex(16),
            "audio_fingerprint": secrets.token_hex(12),
            "created_at": datetime.utcnow().isoformat()
        }
        
        return fingerprint
    
    async def _create_proxy_config(
        self,
        db: AsyncSession,
        profile_id: int,
        proxy_data: Dict[str, Any]
    ):
        """Create proxy configuration with encryption."""
        
        # Encrypt sensitive data
        encrypted_username = None
        encrypted_password = None
        
        if proxy_data.get("username"):
            encrypted_username = security_manager.encrypt_string(proxy_data["username"])
        if proxy_data.get("password"):
            encrypted_password = security_manager.encrypt_string(proxy_data["password"])
        
        proxy_config = ProxyConfig(
            profile_id=profile_id,
            proxy_type=ProxyType(proxy_data.get("proxy_type", "no_proxy")),
            host=proxy_data.get("host"),
            port=proxy_data.get("port"),
            username=encrypted_username,
            password=encrypted_password
        )
        
        db.add(proxy_config)
    
    async def _update_proxy_config(
        self,
        db: AsyncSession,
        profile: Profile,
        proxy_data: Dict[str, Any]
    ):
        """Update proxy configuration."""
        if profile.proxy_config:
            # Update existing
            proxy_config = profile.proxy_config
            proxy_config.proxy_type = ProxyType(proxy_data.get("proxy_type", "no_proxy"))
            proxy_config.host = proxy_data.get("host")
            proxy_config.port = proxy_data.get("port")
            
            if proxy_data.get("username"):
                proxy_config.username = security_manager.encrypt_string(proxy_data["username"])
            if proxy_data.get("password"):
                proxy_config.password = security_manager.encrypt_string(proxy_data["password"])
        else:
            # Create new
            await self._create_proxy_config(db, profile.id, proxy_data)


# Global service instance
profile_service = ProfileService()
