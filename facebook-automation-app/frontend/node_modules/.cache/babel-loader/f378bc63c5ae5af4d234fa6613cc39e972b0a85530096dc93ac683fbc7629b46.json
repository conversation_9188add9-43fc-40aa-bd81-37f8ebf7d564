{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, Button, Grid, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Divider, Avatar, Menu, ListItemIcon, ListItemText, Alert, LinearProgress, FormHelperText } from '@mui/material';\nimport { Add as AddIcon, Person as PersonIcon, MoreVert as MoreVertIcon, Edit as EditIcon, Delete as DeleteIcon, PlayArrow as PlayIcon, Refresh as RefreshIcon, Computer as ComputerIcon, Security as SecurityIcon, Language as LanguageIcon, LocationOn as LocationIcon, Facebook as FacebookIcon, Login as LoginIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProfileCard({\n  profile,\n  onEdit,\n  onDelete,\n  onTest,\n  onFacebookLogin,\n  onFacebookLoginComplete,\n  onFacebookLoginTerminate\n}) {\n  _s();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const handleMenuClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const isLoggedIn = profile.facebook_email || profile.facebook_user_id;\n  const isReady = profile.status === 'active' && isLoggedIn;\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'flex-start',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: 'primary.main',\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            children: profile.name.charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                mb: 0.5\n              },\n              children: profile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: profile.status || 'inactive',\n              size: \"small\",\n              color: getStatusColor(profile.status)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleMenuClick,\n          children: /*#__PURE__*/_jsxDEV(MoreVertIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(ComputerIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"Chrome\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LocationIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.timezone || 'UTC'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Proxy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.proxy_type && profile.proxy_type !== 'no_proxy' ? profile.proxy_type.toUpperCase() : 'No Proxy'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          item: true,\n          xs: 6,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n              sx: {\n                fontSize: 16,\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500\n            },\n            children: profile.language || 'en-US'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FacebookIcon, {\n            fontSize: \"small\",\n            color: \"action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              fontSize: '0.75rem'\n            },\n            children: \"Facebook:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), isLoggedIn ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#4caf50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 500,\n                color: '#4caf50'\n              },\n              children: \"Logged In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(LoginIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#ff9800'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 500,\n                color: '#ff9800'\n              },\n              children: \"Not Logged In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 24\n          }, this),\n          onClick: () => onTest(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"text\",\n          startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 24\n          }, this),\n          onClick: () => onEdit(profile),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        anchorEl: anchorEl,\n        open: Boolean(anchorEl),\n        onClose: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onEdit(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(EditIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Edit Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onTest(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(PlayIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Test Browser\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), !isLoggedIn ? /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLogin(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(FacebookIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#1877f2'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Login to Facebook\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLoginComplete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#4caf50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Complete Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onFacebookLoginTerminate(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(RefreshIcon, {\n              fontSize: \"small\",\n              sx: {\n                color: '#ff9800'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Terminate Browser Session\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => {\n            onDelete(profile);\n            handleMenuClose();\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            children: \"Delete Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n}\n_s(ProfileCard, \"+aMDa7FPcESUyQDF1vq0RSMn4/k=\");\n_c = ProfileCard;\nfunction Profiles() {\n  _s2();\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [loginInstructionsOpen, setLoginInstructionsOpen] = useState(false);\n  const [loginInstructions, setLoginInstructions] = useState([]);\n  const [loginStatusPolling, setLoginStatusPolling] = useState(new Map()); // profileId -> intervalId\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: ''\n  });\n  const queryClient = useQueryClient();\n  const {\n    setProfiles\n  } = useApp();\n\n  // Fetch profiles\n  const {\n    data: profilesData,\n    isLoading,\n    error\n  } = useQuery('profiles', () => profilesAPI.getAll().then(response => response.data), {\n    onSuccess: data => {\n      // Handle both array and object with profiles property\n      const profilesList = (data === null || data === void 0 ? void 0 : data.profiles) || data || [];\n      setProfiles(profilesList);\n    },\n    onError: error => {\n      toast.error('Failed to load profiles');\n    }\n  });\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      console.error('Create profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to create profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(({\n    id,\n    data\n  }) => profilesAPI.update(id, data), {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setEditDialogOpen(false);\n      resetForm();\n      toast.success('Profile updated successfully');\n    },\n    onError: error => {\n      var _error$response2, _error$response2$data;\n      console.error('Update profile error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to update profile';\n      toast.error(errorMessage);\n    }\n  });\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: error => {\n      toast.error('Failed to delete profile');\n    }\n  });\n\n  // Test proxy mutation\n  const testProxyMutation = useMutation(profileId => profilesAPI.testProxy(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'success') {\n        toast.success(`Proxy test successful! Response time: ${result.response_time}s`);\n      } else if (result.status === 'no_proxy') {\n        toast.info('No proxy configured for this profile');\n      } else {\n        toast.error(`Proxy test failed: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response3, _error$response4, _error$response4$data;\n      console.error('Proxy test error:', error);\n\n      // Handle specific error cases\n      if ((error === null || error === void 0 ? void 0 : (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 404) {\n        toast.warning('Proxy test endpoint not available. This feature may not be implemented yet.');\n      } else if ((error === null || error === void 0 ? void 0 : (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) === \"Endpoint not found\") {\n        toast.warning('Test feature not available in current backend version.');\n      } else {\n        var _error$response5, _error$response5$data, _error$response6, _error$response6$data;\n        const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || (error === null || error === void 0 ? void 0 : (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || (error === null || error === void 0 ? void 0 : error.message) || 'Proxy test failed';\n        toast.error(errorMessage);\n      }\n    }\n  });\n\n  // Facebook login mutations\n  const facebookLoginMutation = useMutation(profileId => profilesAPI.facebookLogin(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'login_initiated' || result.status === 'browser_launched') {\n        toast.success('Antidetect browser launched! Complete Facebook login manually in the browser window.');\n        // Show instructions dialog\n        setLoginInstructions(result.instructions || []);\n        setLoginInstructionsOpen(true);\n\n        // Start polling for login status\n        startLoginStatusPolling(result.profile_id);\n      } else if (result.status === 'session_exists') {\n        toast.info('Browser session already active. Complete login in the existing browser window.');\n        setLoginInstructions([\"Browser session is already running\", \"Complete Facebook login in the existing browser window\", \"Click 'Complete Login' button when done\"]);\n        setLoginInstructionsOpen(true);\n      } else {\n        toast.error(`Failed to start login: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response7, _error$response7$data;\n      console.error('Facebook login error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to start Facebook login';\n      toast.error(errorMessage);\n    }\n  });\n  const facebookLoginCompleteMutation = useMutation(({\n    profileId,\n    facebookData\n  }) => profilesAPI.facebookLoginComplete(profileId, facebookData), {\n    onSuccess: response => {\n      const result = response.data;\n      if (result.status === 'login_complete') {\n        toast.success(`Facebook login completed! Profile \"${result.profile_name}\" is now ready.`);\n        queryClient.invalidateQueries('profiles');\n      } else {\n        toast.error(`Failed to complete login: ${result.message}`);\n      }\n    },\n    onError: error => {\n      var _error$response8, _error$response8$data;\n      console.error('Facebook login complete error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to complete Facebook login';\n      toast.error(errorMessage);\n    }\n  });\n  const facebookLoginTerminateMutation = useMutation(profileId => profilesAPI.facebookLoginTerminate(profileId), {\n    onSuccess: response => {\n      const result = response.data;\n      toast.success('Browser session terminated successfully.');\n      stopLoginStatusPolling(result.profile_id);\n      setLoginInstructionsOpen(false);\n    },\n    onError: error => {\n      var _error$response9, _error$response9$data;\n      console.error('Facebook login terminate error:', error);\n      const errorMessage = (error === null || error === void 0 ? void 0 : (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.detail) || (error === null || error === void 0 ? void 0 : error.message) || 'Failed to terminate browser session';\n      toast.error(errorMessage);\n    }\n  });\n  const profiles = Array.isArray(profilesData === null || profilesData === void 0 ? void 0 : profilesData.profiles) ? profilesData.profiles : Array.isArray(profilesData) ? profilesData : [];\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: ''\n    });\n    setSelectedProfile(null);\n  };\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n  const handleEdit = profile => {\n    setSelectedProfile(profile);\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome',\n      // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '',\n      // Not in current API response\n      proxy_password: '' // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n  const handleDelete = profile => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n  const handleTest = profile => {\n    // Show profile information\n    const proxyInfo = profile.proxy_type && profile.proxy_type !== 'no_proxy' ? `${profile.proxy_type.toUpperCase()}${profile.proxy_host ? ` (${profile.proxy_host}:${profile.proxy_port})` : ''}` : 'No Proxy';\n\n    // Create a detailed info message\n    const profileInfo = [`Profile: ${profile.name}`, `Status: ${profile.status}`, `Proxy: ${proxyInfo}`, `Language: ${profile.language || 'en-US'}`, `Timezone: ${profile.timezone || 'UTC'}`, `Created: ${new Date(profile.created_at).toLocaleDateString()}`].join('\\n');\n\n    // Show info first\n    toast.success(`Profile Information:\\n${profileInfo}`, {\n      duration: 5000\n    });\n\n    // Try to test proxy if configured\n    if (profile.proxy_type && profile.proxy_type !== 'no_proxy' && profile.proxy_host) {\n      setTimeout(() => {\n        testProxyMutation.mutate(profile.id);\n      }, 1000);\n    }\n  };\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = {\n      ...formData\n    };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n    if (selectedProfile) {\n      updateMutation.mutate({\n        id: selectedProfile.id,\n        data: finalData\n      });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleFacebookLogin = profile => {\n    facebookLoginMutation.mutate(profile.id);\n  };\n  const handleFacebookLoginTerminate = profile => {\n    if (window.confirm(`Are you sure you want to terminate the browser session for \"${profile.name}\"?`)) {\n      facebookLoginTerminateMutation.mutate(profile.id);\n    }\n  };\n\n  // Login status polling functions\n  const startLoginStatusPolling = profileId => {\n    // Clear any existing polling for this profile\n    stopLoginStatusPolling(profileId);\n    const intervalId = setInterval(async () => {\n      try {\n        const response = await profilesAPI.facebookLoginStatus(profileId);\n        const status = response.data.login_status;\n        if (status.status === 'browser_closed' || status.status === 'expired') {\n          toast.warning('Browser session ended. Please restart login process.');\n          stopLoginStatusPolling(profileId);\n        } else if (status.status === 'no_session') {\n          stopLoginStatusPolling(profileId);\n        }\n        // Continue polling if status is 'in_progress' or 'active'\n      } catch (error) {\n        console.error('Error polling login status:', error);\n        // Don't stop polling on error, might be temporary\n      }\n    }, 10000); // Poll every 10 seconds\n\n    setLoginStatusPolling(prev => new Map(prev.set(profileId, intervalId)));\n  };\n  const stopLoginStatusPolling = profileId => {\n    const intervalId = loginStatusPolling.get(profileId);\n    if (intervalId) {\n      clearInterval(intervalId);\n      setLoginStatusPolling(prev => {\n        const newMap = new Map(prev);\n        newMap.delete(profileId);\n        return newMap;\n      });\n    }\n  };\n\n  // Cleanup polling on component unmount\n  React.useEffect(() => {\n    return () => {\n      loginStatusPolling.forEach(intervalId => {\n        clearInterval(intervalId);\n      });\n    };\n  }, [loginStatusPolling]);\n  const handleFacebookLoginComplete = profile => {\n    // Stop polling for this profile\n    stopLoginStatusPolling(profile.id);\n\n    // For now, complete without additional data\n    // In a real implementation, you might collect Facebook user data\n    facebookLoginCompleteMutation.mutate({\n      profileId: profile.id,\n      facebookData: {\n        email: \"<EMAIL>\",\n        // This would be extracted from browser\n        username: \"facebook_user\",\n        user_id: \"123456789\"\n      }\n    });\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      className: \"fade-in\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        children: \"Failed to load profiles. Please check your connection to the backend.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 686,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 700\n        },\n        children: \"Profile Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 697,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 24\n          }, this),\n          onClick: () => queryClient.invalidateQueries('profiles'),\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 24\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"New Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 700,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 7\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(LinearProgress, {\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 720,\n      columnNumber: 21\n    }, this), profiles.length === 0 && !isLoading ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center',\n          py: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n          sx: {\n            fontSize: 64,\n            color: 'text.secondary',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1\n          },\n          children: \"No Profiles Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 3\n          },\n          children: \"Create your first browser profile to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 731,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 26\n          }, this),\n          onClick: handleCreate,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Create Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 725,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: profiles.map(profile => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(ProfileCard, {\n          profile: profile,\n          onEdit: handleEdit,\n          onDelete: handleDelete,\n          onTest: handleTest,\n          onFacebookLogin: handleFacebookLogin,\n          onFacebookLoginComplete: handleFacebookLoginComplete,\n          onFacebookLoginTerminate: handleFacebookLoginTerminate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 15\n        }, this)\n      }, profile.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 747,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: createDialogOpen || editDialogOpen,\n      onClose: () => {\n        setCreateDialogOpen(false);\n        setEditDialogOpen(false);\n        resetForm();\n      },\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: selectedProfile ? 'Edit Profile' : 'Create New Profile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Profile Name\",\n                value: formData.name,\n                onChange: e => handleFormChange('name', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Browser Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.browser_type,\n                  onChange: e => handleFormChange('browser_type', e.target.value),\n                  label: \"Browser Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"chrome\",\n                    children: \"Chrome\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"firefox\",\n                    children: \"Firefox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"edge\",\n                    children: \"Edge\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Screen Resolution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.screen_resolution,\n                  onChange: e => handleFormChange('screen_resolution', e.target.value),\n                  label: \"Screen Resolution\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1920x1080\",\n                    children: \"1920x1080\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 812,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1366x768\",\n                    children: \"1366x768\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1440x900\",\n                    children: \"1440x900\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"1280x720\",\n                    children: \"1280x720\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 815,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Timezone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.timezone,\n                  onChange: e => handleFormChange('timezone', e.target.value),\n                  label: \"Timezone\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"UTC\",\n                    children: \"UTC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Asia/Ho_Chi_Minh\",\n                    children: \"Asia/Ho_Chi_Minh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"America/New_York\",\n                    children: \"America/New_York\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Europe/London\",\n                    children: \"Europe/London\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 823,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.language,\n                  onChange: e => handleFormChange('language', e.target.value),\n                  label: \"Language\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"en-US\",\n                    children: \"English (US)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"vi-VN\",\n                    children: \"Vietnamese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 845,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"zh-CN\",\n                    children: \"Chinese (Simplified)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ja-JP\",\n                    children: \"Japanese\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"User Agent (Optional)\",\n                value: formData.user_agent,\n                onChange: e => handleFormChange('user_agent', e.target.value),\n                placeholder: \"Leave empty for automatic generation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2,\n                  fontWeight: 600\n                },\n                children: \"Proxy Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Proxy Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 870,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.proxy_type,\n                  onChange: e => {\n                    const proxyType = e.target.value;\n                    handleFormChange('proxy_type', proxyType);\n                    handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                  },\n                  label: \"Proxy Type\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"no_proxy\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"No Proxy (Local Network)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 882,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Direct connection without proxy\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 883,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 881,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 880,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"http\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTP\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 890,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Standard HTTP proxy (ports: 8080, 3128, 8888)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 891,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 889,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"https\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"HTTPS\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 898,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Encrypted HTTPS proxy (ports: 8080, 3128, 443)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 899,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 897,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 896,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"socks5\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SOCKS5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 906,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"High anonymity proxy (ports: 1080, 1081, 9050)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 907,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 905,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 904,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ssh\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: \"SSH\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 914,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"Secure SSH tunnel (ports: 22, 2222)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 915,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 913,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 912,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: [formData.proxy_type === 'no_proxy' && 'Using direct connection', formData.proxy_type === 'http' && 'Fast, suitable for web browsing', formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data', formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols', formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this), formData.proxy_type !== 'no_proxy' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Host\",\n                  value: formData.proxy_host,\n                  onChange: e => handleFormChange('proxy_host', e.target.value),\n                  required: true,\n                  placeholder: formData.proxy_type === 'ssh' ? \"e.g., ssh.example.com or *************\" : \"e.g., proxy.example.com or *************\",\n                  helperText: \"IP address or domain name of the proxy server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 934,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 933,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Proxy Port\",\n                  value: formData.proxy_port,\n                  onChange: e => handleFormChange('proxy_port', e.target.value),\n                  type: \"number\",\n                  required: true,\n                  placeholder: formData.proxy_type === 'http' ? \"8080, 3128, 8888\" : formData.proxy_type === 'https' ? \"8080, 3128, 443\" : formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" : formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\",\n                  helperText: `Common ${formData.proxy_type.toUpperCase()} ports`,\n                  inputProps: {\n                    min: 1,\n                    max: 65535\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\",\n                  value: formData.proxy_username,\n                  onChange: e => handleFormChange('proxy_username', e.target.value),\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH username for authentication\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Required for SSH connections\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 967,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 966,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\",\n                  value: formData.proxy_password,\n                  onChange: e => handleFormChange('proxy_password', e.target.value),\n                  type: \"password\",\n                  placeholder: formData.proxy_type === 'ssh' ? \"SSH password or leave empty for key-based auth\" : \"Leave empty if no authentication required\",\n                  helperText: formData.proxy_type === 'ssh' ? \"Password or private key authentication\" : \"Optional - only if proxy requires authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2,\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    onClick: () => {\n                      if (selectedProfile) {\n                        testProxyMutation.mutate(selectedProfile.id);\n                      } else {\n                        // Test proxy configuration without saving\n                        const proxyInfo = `${formData.proxy_type.toUpperCase()} proxy: ${formData.proxy_host}:${formData.proxy_port}`;\n                        const authInfo = formData.proxy_username ? ` (Auth: ${formData.proxy_username})` : ' (No Auth)';\n                        toast.info(`Proxy Configuration:\\n${proxyInfo}${authInfo}\\n\\nSave profile first to test connection.`, {\n                          duration: 4000\n                        });\n                      }\n                    },\n                    disabled: !formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading,\n                    sx: {\n                      textTransform: 'none'\n                    },\n                    children: testProxyMutation.isLoading ? 'Testing...' : selectedProfile ? 'Test Connection' : 'Preview Config'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1006,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: \"Test proxy connection to ensure it works with antidetect browser\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1005,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1004,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 776,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setCreateDialogOpen(false);\n            setEditDialogOpen(false);\n            resetForm();\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1035,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleSubmit,\n          disabled: !formData.name || createMutation.isLoading || updateMutation.isLoading,\n          children: selectedProfile ? 'Update' : 'Create'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1034,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 763,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: loginInstructionsOpen,\n      onClose: () => setLoginInstructionsOpen(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(FacebookIcon, {\n          sx: {\n            color: '#1877f2'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1062,\n          columnNumber: 11\n        }, this), \"Facebook Login Instructions\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1061,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"An antidetect browser window will open. Follow these steps to complete Facebook login:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1066,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"ol\",\n          sx: {\n            pl: 2\n          },\n          children: loginInstructions.map((instruction, index) => /*#__PURE__*/_jsxDEV(Box, {\n            component: \"li\",\n            sx: {\n              mb: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: instruction\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1070,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Important:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 15\n            }, this), \" After successfully logging in to Facebook, click the \\\"Complete Login\\\" button in the profile menu to update the profile status to \\\"Ready\\\".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1079,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1078,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1065,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setLoginInstructionsOpen(false),\n          children: \"Got it\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1086,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1085,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1055,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 695,\n    columnNumber: 5\n  }, this);\n}\n_s2(Profiles, \"grwBbtnLiMUn1AWcD7bDkPPR//k=\", false, function () {\n  return [useQueryClient, useApp, useQuery, useMutation, useMutation, useMutation, useMutation, useMutation, useMutation, useMutation];\n});\n_c2 = Profiles;\nexport default Profiles;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProfileCard\");\n$RefreshReg$(_c2, \"Profiles\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "Avatar", "<PERSON><PERSON>", "ListItemIcon", "ListItemText", "<PERSON><PERSON>", "LinearProgress", "FormHelperText", "Add", "AddIcon", "Person", "PersonIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "PlayArrow", "PlayIcon", "Refresh", "RefreshIcon", "Computer", "ComputerIcon", "Security", "SecurityIcon", "Language", "LanguageIcon", "LocationOn", "LocationIcon", "Facebook", "FacebookIcon", "<PERSON><PERSON>", "LoginIcon", "CheckCircle", "CheckCircleIcon", "useQuery", "useMutation", "useQueryClient", "toast", "profilesAPI", "useApp", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfileCard", "profile", "onEdit", "onDelete", "onTest", "onFacebookLogin", "onFacebookLoginComplete", "onFacebookLoginTerminate", "_s", "anchorEl", "setAnchorEl", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "isLoggedIn", "facebook_email", "facebook_user_id", "isReady", "status", "getStatusColor", "sx", "height", "children", "display", "justifyContent", "alignItems", "mb", "bgcolor", "width", "mr", "name", "char<PERSON>t", "toUpperCase", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "label", "size", "color", "onClick", "my", "container", "spacing", "item", "xs", "fontSize", "timezone", "proxy_type", "language", "mt", "gap", "startIcon", "textTransform", "open", "Boolean", "onClose", "_c", "Profiles", "_s2", "createDialogOpen", "setCreateDialogOpen", "editDialogOpen", "setEditDialogOpen", "selectedProfile", "setSelectedProfile", "loginInstructionsOpen", "setLoginInstructionsOpen", "loginInstructions", "setLoginInstructions", "loginStatusPolling", "setLoginStatusPolling", "Map", "formData", "setFormData", "browser_type", "user_agent", "screen_resolution", "proxy_enabled", "proxy_host", "proxy_port", "proxy_username", "proxy_password", "queryClient", "setProfiles", "data", "profilesData", "isLoading", "error", "getAll", "then", "response", "onSuccess", "profilesList", "profiles", "onError", "createMutation", "create", "invalidateQueries", "resetForm", "success", "_error$response", "_error$response$data", "console", "errorMessage", "detail", "message", "updateMutation", "id", "update", "_error$response2", "_error$response2$data", "deleteMutation", "delete", "testProxyMutation", "profileId", "testProxy", "result", "response_time", "info", "_error$response3", "_error$response4", "_error$response4$data", "warning", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data", "facebookLoginMutation", "facebookLogin", "instructions", "startLoginStatusPolling", "profile_id", "_error$response7", "_error$response7$data", "facebookLoginCompleteMutation", "facebookData", "facebookLoginComplete", "profile_name", "_error$response8", "_error$response8$data", "facebookLoginTerminateMutation", "facebookLoginTerminate", "stopLoginStatusPolling", "_error$response9", "_error$response9$data", "Array", "isArray", "handleCreate", "handleEdit", "toString", "handleDelete", "window", "confirm", "mutate", "handleTest", "proxyInfo", "profileInfo", "Date", "created_at", "toLocaleDateString", "join", "duration", "setTimeout", "handleSubmit", "submitData", "port", "parseInt", "isNaN", "finalData", "browser_config", "proxy_config", "host", "username", "password", "handleFormChange", "field", "value", "prev", "handleFacebookLogin", "handleFacebookLoginTerminate", "intervalId", "setInterval", "facebookLoginStatus", "login_status", "set", "get", "clearInterval", "newMap", "useEffect", "for<PERSON>ach", "handleFacebookLoginComplete", "email", "user_id", "className", "severity", "length", "textAlign", "py", "map", "sm", "md", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "onChange", "e", "target", "required", "placeholder", "proxyType", "helperText", "type", "inputProps", "min", "max", "authInfo", "disabled", "component", "pl", "instruction", "index", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/bot-follow/facebook-automation-app/frontend/src/pages/Profiles.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Grid,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Avatar,\n  Menu,\n  ListItemIcon,\n  ListItemText,\n  Alert,\n  LinearProgress,\n  FormHelperText,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Person as PersonIcon,\n  MoreVert as MoreVertIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  PlayArrow as PlayIcon,\n  Refresh as RefreshIcon,\n  Computer as ComputerIcon,\n  Security as SecurityIcon,\n  Language as LanguageIcon,\n  LocationOn as LocationIcon,\n  Facebook as FacebookIcon,\n  Login as LoginIcon,\n  CheckCircle as CheckCircleIcon,\n} from '@mui/icons-material';\nimport { useQuery, useMutation, useQueryClient } from 'react-query';\nimport toast from 'react-hot-toast';\n\nimport { profilesAPI } from '../services/api';\nimport { useApp } from '../contexts/AppContext';\n\nfunction ProfileCard({ profile, onEdit, onDelete, onTest, onFacebookLogin, onFacebookLoginComplete, onFacebookLoginTerminate }) {\n  const [anchorEl, setAnchorEl] = useState(null);\n\n  const handleMenuClick = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const isLoggedIn = profile.facebook_email || profile.facebook_user_id;\n  const isReady = profile.status === 'active' && isLoggedIn;\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Avatar\n              sx={{\n                bgcolor: 'primary.main',\n                width: 48,\n                height: 48,\n                mr: 2,\n              }}\n            >\n              {profile.name.charAt(0).toUpperCase()}\n            </Avatar>\n            <Box>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                {profile.name}\n              </Typography>\n              <Chip\n                label={profile.status || 'inactive'}\n                size=\"small\"\n                color={getStatusColor(profile.status)}\n              />\n            </Box>\n          </Box>\n          <IconButton onClick={handleMenuClick}>\n            <MoreVertIcon />\n          </IconButton>\n        </Box>\n\n        <Divider sx={{ my: 2 }} />\n\n        <Grid container spacing={2}>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <ComputerIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Browser\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              Chrome\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LocationIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Location\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.timezone || 'UTC'}\n            </Typography>\n          </Grid>\n          <Grid item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <SecurityIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Proxy\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.proxy_type && profile.proxy_type !== 'no_proxy'\n                ? profile.proxy_type.toUpperCase()\n                : 'No Proxy'}\n            </Typography>\n          </Grid>\n          <Box item xs={6}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n              <LanguageIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Language\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n              {profile.language || 'en-US'}\n            </Typography>\n          </Box>\n\n        </Grid>\n\n        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <FacebookIcon fontSize=\"small\" color=\"action\" />\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ fontSize: '0.75rem' }}>\n              Facebook:\n            </Typography>\n            {isLoggedIn ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <CheckCircleIcon fontSize=\"small\" sx={{ color: '#4caf50' }} />\n                <Typography variant=\"body2\" sx={{ fontWeight: 500, color: '#4caf50' }}>\n                  Logged In\n                </Typography>\n              </Box>\n            ) : (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                <LoginIcon fontSize=\"small\" sx={{ color: '#ff9800' }} />\n                <Typography variant=\"body2\" sx={{ fontWeight: 500, color: '#ff9800' }}>\n                  Not Logged In\n                </Typography>\n              </Box>\n            )}\n          </Box>\n        </Box>\n\n        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>\n          <Button\n            size=\"small\"\n            variant=\"outlined\"\n            startIcon={<PlayIcon />}\n            onClick={() => onTest(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Test\n          </Button>\n          <Button\n            size=\"small\"\n            variant=\"text\"\n            startIcon={<EditIcon />}\n            onClick={() => onEdit(profile)}\n            sx={{ textTransform: 'none' }}\n          >\n            Edit\n          </Button>\n        </Box>\n\n        <Menu\n          anchorEl={anchorEl}\n          open={Boolean(anchorEl)}\n          onClose={handleMenuClose}\n        >\n          <MenuItem onClick={() => { onEdit(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <EditIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Edit Profile</ListItemText>\n          </MenuItem>\n          <MenuItem onClick={() => { onTest(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <PlayIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Test Browser</ListItemText>\n          </MenuItem>\n\n          <Divider />\n\n          {!isLoggedIn ? (\n            <MenuItem onClick={() => { onFacebookLogin(profile); handleMenuClose(); }}>\n              <ListItemIcon>\n                <FacebookIcon fontSize=\"small\" sx={{ color: '#1877f2' }} />\n              </ListItemIcon>\n              <ListItemText>Login to Facebook</ListItemText>\n            </MenuItem>\n          ) : (\n            <MenuItem onClick={() => { onFacebookLoginComplete(profile); handleMenuClose(); }}>\n              <ListItemIcon>\n                <CheckCircleIcon fontSize=\"small\" sx={{ color: '#4caf50' }} />\n              </ListItemIcon>\n              <ListItemText>Complete Login</ListItemText>\n            </MenuItem>\n          )}\n\n          <MenuItem onClick={() => { onFacebookLoginTerminate(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <RefreshIcon fontSize=\"small\" sx={{ color: '#ff9800' }} />\n            </ListItemIcon>\n            <ListItemText>Terminate Browser Session</ListItemText>\n          </MenuItem>\n\n          <Divider />\n\n          <MenuItem onClick={() => { onDelete(profile); handleMenuClose(); }}>\n            <ListItemIcon>\n              <DeleteIcon fontSize=\"small\" />\n            </ListItemIcon>\n            <ListItemText>Delete Profile</ListItemText>\n          </MenuItem>\n        </Menu>\n      </CardContent>\n    </Card>\n  );\n}\n\nfunction Profiles() {\n  const [createDialogOpen, setCreateDialogOpen] = useState(false);\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [selectedProfile, setSelectedProfile] = useState(null);\n  const [loginInstructionsOpen, setLoginInstructionsOpen] = useState(false);\n  const [loginInstructions, setLoginInstructions] = useState([]);\n  const [loginStatusPolling, setLoginStatusPolling] = useState(new Map()); // profileId -> intervalId\n  const [formData, setFormData] = useState({\n    name: '',\n    browser_type: 'chrome',\n    user_agent: '',\n    screen_resolution: '1920x1080',\n    timezone: 'UTC',\n    language: 'en-US',\n    proxy_enabled: false,\n    proxy_type: 'no_proxy',\n    proxy_host: '',\n    proxy_port: '',\n    proxy_username: '',\n    proxy_password: '',\n  });\n\n  const queryClient = useQueryClient();\n  const { setProfiles } = useApp();\n\n  // Fetch profiles\n  const { data: profilesData, isLoading, error } = useQuery(\n    'profiles',\n    () => profilesAPI.getAll().then(response => response.data),\n    {\n      onSuccess: (data) => {\n        // Handle both array and object with profiles property\n        const profilesList = data?.profiles || data || [];\n        setProfiles(profilesList);\n      },\n      onError: (error) => {\n        toast.error('Failed to load profiles');\n      },\n    }\n  );\n\n  // Create profile mutation\n  const createMutation = useMutation(profilesAPI.create, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      setCreateDialogOpen(false);\n      resetForm();\n      toast.success('Profile created successfully');\n    },\n    onError: (error) => {\n      console.error('Create profile error:', error);\n      const errorMessage = error?.response?.data?.detail ||\n                          error?.message ||\n                          'Failed to create profile';\n      toast.error(errorMessage);\n    },\n  });\n\n  // Update profile mutation\n  const updateMutation = useMutation(\n    ({ id, data }) => profilesAPI.update(id, data),\n    {\n      onSuccess: () => {\n        queryClient.invalidateQueries('profiles');\n        setEditDialogOpen(false);\n        resetForm();\n        toast.success('Profile updated successfully');\n      },\n      onError: (error) => {\n        console.error('Update profile error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to update profile';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  // Delete profile mutation\n  const deleteMutation = useMutation(profilesAPI.delete, {\n    onSuccess: () => {\n      queryClient.invalidateQueries('profiles');\n      toast.success('Profile deleted successfully');\n    },\n    onError: (error) => {\n      toast.error('Failed to delete profile');\n    },\n  });\n\n\n\n  // Test proxy mutation\n  const testProxyMutation = useMutation(\n    (profileId) => profilesAPI.testProxy(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'success') {\n          toast.success(`Proxy test successful! Response time: ${result.response_time}s`);\n        } else if (result.status === 'no_proxy') {\n          toast.info('No proxy configured for this profile');\n        } else {\n          toast.error(`Proxy test failed: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Proxy test error:', error);\n\n        // Handle specific error cases\n        if (error?.response?.status === 404) {\n          toast.warning('Proxy test endpoint not available. This feature may not be implemented yet.');\n        } else if (error?.response?.data?.message === \"Endpoint not found\") {\n          toast.warning('Test feature not available in current backend version.');\n        } else {\n          const errorMessage = error?.response?.data?.detail ||\n                              error?.response?.data?.message ||\n                              error?.message ||\n                              'Proxy test failed';\n          toast.error(errorMessage);\n        }\n      },\n    }\n  );\n\n  // Facebook login mutations\n  const facebookLoginMutation = useMutation(\n    (profileId) => profilesAPI.facebookLogin(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'login_initiated' || result.status === 'browser_launched') {\n          toast.success('Antidetect browser launched! Complete Facebook login manually in the browser window.');\n          // Show instructions dialog\n          setLoginInstructions(result.instructions || []);\n          setLoginInstructionsOpen(true);\n\n          // Start polling for login status\n          startLoginStatusPolling(result.profile_id);\n        } else if (result.status === 'session_exists') {\n          toast.info('Browser session already active. Complete login in the existing browser window.');\n          setLoginInstructions([\n            \"Browser session is already running\",\n            \"Complete Facebook login in the existing browser window\",\n            \"Click 'Complete Login' button when done\"\n          ]);\n          setLoginInstructionsOpen(true);\n        } else {\n          toast.error(`Failed to start login: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Facebook login error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to start Facebook login';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const facebookLoginCompleteMutation = useMutation(\n    ({ profileId, facebookData }) => profilesAPI.facebookLoginComplete(profileId, facebookData),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        if (result.status === 'login_complete') {\n          toast.success(`Facebook login completed! Profile \"${result.profile_name}\" is now ready.`);\n          queryClient.invalidateQueries('profiles');\n        } else {\n          toast.error(`Failed to complete login: ${result.message}`);\n        }\n      },\n      onError: (error) => {\n        console.error('Facebook login complete error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to complete Facebook login';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const facebookLoginTerminateMutation = useMutation(\n    (profileId) => profilesAPI.facebookLoginTerminate(profileId),\n    {\n      onSuccess: (response) => {\n        const result = response.data;\n        toast.success('Browser session terminated successfully.');\n        stopLoginStatusPolling(result.profile_id);\n        setLoginInstructionsOpen(false);\n      },\n      onError: (error) => {\n        console.error('Facebook login terminate error:', error);\n        const errorMessage = error?.response?.data?.detail ||\n                            error?.message ||\n                            'Failed to terminate browser session';\n        toast.error(errorMessage);\n      },\n    }\n  );\n\n  const profiles = Array.isArray(profilesData?.profiles)\n    ? profilesData.profiles\n    : Array.isArray(profilesData)\n    ? profilesData\n    : [];\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      browser_type: 'chrome',\n      user_agent: '',\n      screen_resolution: '1920x1080',\n      timezone: 'UTC',\n      language: 'en-US',\n      proxy_enabled: false,\n      proxy_type: 'no_proxy',\n      proxy_host: '',\n      proxy_port: '',\n      proxy_username: '',\n      proxy_password: '',\n    });\n    setSelectedProfile(null);\n  };\n\n  const handleCreate = () => {\n    setCreateDialogOpen(true);\n    resetForm();\n  };\n\n  const handleEdit = (profile) => {\n    setSelectedProfile(profile);\n\n    setFormData({\n      name: profile.name || '',\n      browser_type: 'chrome', // Default since not in current API response\n      user_agent: profile.user_agent || '',\n      screen_resolution: profile.screen_resolution || '1920x1080',\n      timezone: profile.timezone || 'UTC',\n      language: profile.language || 'en-US',\n      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',\n      proxy_type: profile.proxy_type || 'no_proxy',\n      proxy_host: profile.proxy_host || '',\n      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',\n      proxy_username: '', // Not in current API response\n      proxy_password: '', // Don't populate password for security\n    });\n    setEditDialogOpen(true);\n  };\n\n  const handleDelete = (profile) => {\n    if (window.confirm(`Are you sure you want to delete profile \"${profile.name}\"?`)) {\n      deleteMutation.mutate(profile.id);\n    }\n  };\n\n  const handleTest = (profile) => {\n    // Show profile information\n    const proxyInfo = profile.proxy_type && profile.proxy_type !== 'no_proxy'\n      ? `${profile.proxy_type.toUpperCase()}${profile.proxy_host ? ` (${profile.proxy_host}:${profile.proxy_port})` : ''}`\n      : 'No Proxy';\n\n    // Create a detailed info message\n    const profileInfo = [\n      `Profile: ${profile.name}`,\n      `Status: ${profile.status}`,\n      `Proxy: ${proxyInfo}`,\n      `Language: ${profile.language || 'en-US'}`,\n      `Timezone: ${profile.timezone || 'UTC'}`,\n      `Created: ${new Date(profile.created_at).toLocaleDateString()}`\n    ].join('\\n');\n\n    // Show info first\n    toast.success(`Profile Information:\\n${profileInfo}`, { duration: 5000 });\n\n    // Try to test proxy if configured\n    if (profile.proxy_type && profile.proxy_type !== 'no_proxy' && profile.proxy_host) {\n      setTimeout(() => {\n        testProxyMutation.mutate(profile.id);\n      }, 1000);\n    }\n  };\n\n  const handleSubmit = () => {\n    // Prepare form data with proper type conversion\n    const submitData = { ...formData };\n\n    // Convert empty strings to null for optional fields\n    if (submitData.proxy_host === '') submitData.proxy_host = null;\n    if (submitData.proxy_port === '') submitData.proxy_port = null;\n    if (submitData.proxy_username === '') submitData.proxy_username = null;\n    if (submitData.proxy_password === '') submitData.proxy_password = null;\n    if (submitData.user_agent === '') submitData.user_agent = null;\n\n    // Convert port to integer if provided\n    if (submitData.proxy_port) {\n      const port = parseInt(submitData.proxy_port);\n      if (isNaN(port)) {\n        toast.error('Proxy port must be a valid number');\n        return;\n      }\n      submitData.proxy_port = port;\n    }\n\n    // Validate proxy configuration\n    if (formData.proxy_type !== 'no_proxy') {\n      if (!formData.proxy_host || !formData.proxy_port) {\n        toast.error('Proxy host and port are required when proxy is enabled');\n        return;\n      }\n\n      const port = parseInt(formData.proxy_port);\n      if (isNaN(port) || port < 1 || port > 65535) {\n        toast.error('Proxy port must be a valid number between 1 and 65535');\n        return;\n      }\n    }\n\n    // Prepare final data structure\n    const finalData = {\n      name: submitData.name,\n      user_agent: submitData.user_agent,\n      browser_config: {\n        browser_type: submitData.browser_type || 'chrome',\n        screen_resolution: submitData.screen_resolution || '1920x1080',\n        timezone: submitData.timezone || 'UTC',\n        language: submitData.language || 'en-US'\n      },\n      proxy_config: submitData.proxy_type !== 'no_proxy' ? {\n        proxy_type: submitData.proxy_type,\n        host: submitData.proxy_host,\n        port: submitData.proxy_port,\n        username: submitData.proxy_username,\n        password: submitData.proxy_password\n      } : null\n    };\n\n    if (selectedProfile) {\n      updateMutation.mutate({ id: selectedProfile.id, data: finalData });\n    } else {\n      createMutation.mutate(finalData);\n    }\n  };\n\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleFacebookLogin = (profile) => {\n    facebookLoginMutation.mutate(profile.id);\n  };\n\n  const handleFacebookLoginTerminate = (profile) => {\n    if (window.confirm(`Are you sure you want to terminate the browser session for \"${profile.name}\"?`)) {\n      facebookLoginTerminateMutation.mutate(profile.id);\n    }\n  };\n\n  // Login status polling functions\n  const startLoginStatusPolling = (profileId) => {\n    // Clear any existing polling for this profile\n    stopLoginStatusPolling(profileId);\n\n    const intervalId = setInterval(async () => {\n      try {\n        const response = await profilesAPI.facebookLoginStatus(profileId);\n        const status = response.data.login_status;\n\n        if (status.status === 'browser_closed' || status.status === 'expired') {\n          toast.warning('Browser session ended. Please restart login process.');\n          stopLoginStatusPolling(profileId);\n        } else if (status.status === 'no_session') {\n          stopLoginStatusPolling(profileId);\n        }\n        // Continue polling if status is 'in_progress' or 'active'\n      } catch (error) {\n        console.error('Error polling login status:', error);\n        // Don't stop polling on error, might be temporary\n      }\n    }, 10000); // Poll every 10 seconds\n\n    setLoginStatusPolling(prev => new Map(prev.set(profileId, intervalId)));\n  };\n\n  const stopLoginStatusPolling = (profileId) => {\n    const intervalId = loginStatusPolling.get(profileId);\n    if (intervalId) {\n      clearInterval(intervalId);\n      setLoginStatusPolling(prev => {\n        const newMap = new Map(prev);\n        newMap.delete(profileId);\n        return newMap;\n      });\n    }\n  };\n\n  // Cleanup polling on component unmount\n  React.useEffect(() => {\n    return () => {\n      loginStatusPolling.forEach((intervalId) => {\n        clearInterval(intervalId);\n      });\n    };\n  }, [loginStatusPolling]);\n\n  const handleFacebookLoginComplete = (profile) => {\n    // Stop polling for this profile\n    stopLoginStatusPolling(profile.id);\n\n    // For now, complete without additional data\n    // In a real implementation, you might collect Facebook user data\n    facebookLoginCompleteMutation.mutate({\n      profileId: profile.id,\n      facebookData: {\n        email: \"<EMAIL>\", // This would be extracted from browser\n        username: \"facebook_user\",\n        user_id: \"123456789\"\n      }\n    });\n  };\n\n  if (error) {\n    return (\n      <Box className=\"fade-in\">\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          Failed to load profiles. Please check your connection to the backend.\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box className=\"fade-in\">\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n          Profile Management\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => queryClient.invalidateQueries('profiles')}\n            sx={{ textTransform: 'none' }}\n          >\n            Refresh\n          </Button>\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={handleCreate}\n            sx={{ textTransform: 'none' }}\n          >\n            New Profile\n          </Button>\n        </Box>\n      </Box>\n\n      {isLoading && <LinearProgress sx={{ mb: 3 }} />}\n\n\n\n      {profiles.length === 0 && !isLoading ? (\n        <Card>\n          <CardContent sx={{ textAlign: 'center', py: 8 }}>\n            <PersonIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n            <Typography variant=\"h6\" sx={{ mb: 1 }}>\n              No Profiles Found\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              Create your first browser profile to get started\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={handleCreate}\n              sx={{ textTransform: 'none' }}\n            >\n              Create Profile\n            </Button>\n          </CardContent>\n        </Card>\n      ) : (\n        <Grid container spacing={3}>\n          {profiles.map((profile) => (\n            <Grid item xs={12} sm={6} md={4} key={profile.id}>\n              <ProfileCard\n                profile={profile}\n                onEdit={handleEdit}\n                onDelete={handleDelete}\n                onTest={handleTest}\n                onFacebookLogin={handleFacebookLogin}\n                onFacebookLoginComplete={handleFacebookLoginComplete}\n                onFacebookLoginTerminate={handleFacebookLoginTerminate}\n              />\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Create/Edit Profile Dialog */}\n      <Dialog\n        open={createDialogOpen || editDialogOpen}\n        onClose={() => {\n          setCreateDialogOpen(false);\n          setEditDialogOpen(false);\n          resetForm();\n        }}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          {selectedProfile ? 'Edit Profile' : 'Create New Profile'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Grid container spacing={3}>\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Profile Name\"\n                  value={formData.name}\n                  onChange={(e) => handleFormChange('name', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Browser Type</InputLabel>\n                  <Select\n                    value={formData.browser_type}\n                    onChange={(e) => handleFormChange('browser_type', e.target.value)}\n                    label=\"Browser Type\"\n                  >\n                    <MenuItem value=\"chrome\">Chrome</MenuItem>\n                    <MenuItem value=\"firefox\">Firefox</MenuItem>\n                    <MenuItem value=\"edge\">Edge</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Screen Resolution</InputLabel>\n                  <Select\n                    value={formData.screen_resolution}\n                    onChange={(e) => handleFormChange('screen_resolution', e.target.value)}\n                    label=\"Screen Resolution\"\n                  >\n                    <MenuItem value=\"1920x1080\">1920x1080</MenuItem>\n                    <MenuItem value=\"1366x768\">1366x768</MenuItem>\n                    <MenuItem value=\"1440x900\">1440x900</MenuItem>\n                    <MenuItem value=\"1280x720\">1280x720</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Timezone</InputLabel>\n                  <Select\n                    value={formData.timezone}\n                    onChange={(e) => handleFormChange('timezone', e.target.value)}\n                    label=\"Timezone\"\n                  >\n                    <MenuItem value=\"UTC\">UTC</MenuItem>\n                    <MenuItem value=\"Asia/Ho_Chi_Minh\">Asia/Ho_Chi_Minh</MenuItem>\n                    <MenuItem value=\"America/New_York\">America/New_York</MenuItem>\n                    <MenuItem value=\"Europe/London\">Europe/London</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Language</InputLabel>\n                  <Select\n                    value={formData.language}\n                    onChange={(e) => handleFormChange('language', e.target.value)}\n                    label=\"Language\"\n                  >\n                    <MenuItem value=\"en-US\">English (US)</MenuItem>\n                    <MenuItem value=\"vi-VN\">Vietnamese</MenuItem>\n                    <MenuItem value=\"zh-CN\">Chinese (Simplified)</MenuItem>\n                    <MenuItem value=\"ja-JP\">Japanese</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"User Agent (Optional)\"\n                  value={formData.user_agent}\n                  onChange={(e) => handleFormChange('user_agent', e.target.value)}\n                  placeholder=\"Leave empty for automatic generation\"\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n                  Proxy Configuration\n                </Typography>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Proxy Type</InputLabel>\n                  <Select\n                    value={formData.proxy_type}\n                    onChange={(e) => {\n                      const proxyType = e.target.value;\n                      handleFormChange('proxy_type', proxyType);\n                      handleFormChange('proxy_enabled', proxyType !== 'no_proxy');\n                    }}\n                    label=\"Proxy Type\"\n                  >\n                    <MenuItem value=\"no_proxy\">\n                      <Box>\n                        <Typography variant=\"body2\">No Proxy (Local Network)</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Direct connection without proxy\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"http\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTP</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Standard HTTP proxy (ports: 8080, 3128, 8888)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"https\">\n                      <Box>\n                        <Typography variant=\"body2\">HTTPS</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Encrypted HTTPS proxy (ports: 8080, 3128, 443)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"socks5\">\n                      <Box>\n                        <Typography variant=\"body2\">SOCKS5</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          High anonymity proxy (ports: 1080, 1081, 9050)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                    <MenuItem value=\"ssh\">\n                      <Box>\n                        <Typography variant=\"body2\">SSH</Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Secure SSH tunnel (ports: 22, 2222)\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  </Select>\n                  <FormHelperText>\n                    {formData.proxy_type === 'no_proxy' && 'Using direct connection'}\n                    {formData.proxy_type === 'http' && 'Fast, suitable for web browsing'}\n                    {formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data'}\n                    {formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols'}\n                    {formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials'}\n                  </FormHelperText>\n                </FormControl>\n              </Grid>\n\n              {formData.proxy_type !== 'no_proxy' && (\n                <>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Host\"\n                      value={formData.proxy_host}\n                      onChange={(e) => handleFormChange('proxy_host', e.target.value)}\n                      required\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"e.g., ssh.example.com or *************\"\n                          : \"e.g., proxy.example.com or *************\"\n                      }\n                      helperText=\"IP address or domain name of the proxy server\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label=\"Proxy Port\"\n                      value={formData.proxy_port}\n                      onChange={(e) => handleFormChange('proxy_port', e.target.value)}\n                      type=\"number\"\n                      required\n                      placeholder={\n                        formData.proxy_type === 'http' ? \"8080, 3128, 8888\" :\n                        formData.proxy_type === 'https' ? \"8080, 3128, 443\" :\n                        formData.proxy_type === 'socks5' ? \"1080, 1081, 9050\" :\n                        formData.proxy_type === 'ssh' ? \"22, 2222\" : \"Port number\"\n                      }\n                      helperText={`Common ${formData.proxy_type.toUpperCase()} ports`}\n                      inputProps={{ min: 1, max: 65535 }}\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Username\" : \"Proxy Username\"}\n                      value={formData.proxy_username}\n                      onChange={(e) => handleFormChange('proxy_username', e.target.value)}\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH username for authentication\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Required for SSH connections\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <TextField\n                      fullWidth\n                      label={formData.proxy_type === 'ssh' ? \"SSH Password\" : \"Proxy Password\"}\n                      value={formData.proxy_password}\n                      onChange={(e) => handleFormChange('proxy_password', e.target.value)}\n                      type=\"password\"\n                      placeholder={\n                        formData.proxy_type === 'ssh'\n                          ? \"SSH password or leave empty for key-based auth\"\n                          : \"Leave empty if no authentication required\"\n                      }\n                      helperText={\n                        formData.proxy_type === 'ssh'\n                          ? \"Password or private key authentication\"\n                          : \"Optional - only if proxy requires authentication\"\n                      }\n                    />\n                  </Grid>\n\n                  <Grid item xs={12}>\n                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>\n                      <Button\n                        variant=\"outlined\"\n                        size=\"small\"\n                        onClick={() => {\n                          if (selectedProfile) {\n                            testProxyMutation.mutate(selectedProfile.id);\n                          } else {\n                            // Test proxy configuration without saving\n                            const proxyInfo = `${formData.proxy_type.toUpperCase()} proxy: ${formData.proxy_host}:${formData.proxy_port}`;\n                            const authInfo = formData.proxy_username ? ` (Auth: ${formData.proxy_username})` : ' (No Auth)';\n                            toast.info(`Proxy Configuration:\\n${proxyInfo}${authInfo}\\n\\nSave profile first to test connection.`, { duration: 4000 });\n                          }\n                        }}\n                        disabled={!formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading}\n                        sx={{ textTransform: 'none' }}\n                      >\n                        {testProxyMutation.isLoading ? 'Testing...' : selectedProfile ? 'Test Connection' : 'Preview Config'}\n                      </Button>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        Test proxy connection to ensure it works with antidetect browser\n                      </Typography>\n                    </Box>\n                  </Grid>\n                </>\n              )}\n            </Grid>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => {\n              setCreateDialogOpen(false);\n              setEditDialogOpen(false);\n              resetForm();\n            }}\n          >\n            Cancel\n          </Button>\n          <Button\n            variant=\"contained\"\n            onClick={handleSubmit}\n            disabled={!formData.name || createMutation.isLoading || updateMutation.isLoading}\n          >\n            {selectedProfile ? 'Update' : 'Create'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Facebook Login Instructions Dialog */}\n      <Dialog\n        open={loginInstructionsOpen}\n        onClose={() => setLoginInstructionsOpen(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <FacebookIcon sx={{ color: '#1877f2' }} />\n          Facebook Login Instructions\n        </DialogTitle>\n        <DialogContent>\n          <Alert severity=\"info\" sx={{ mb: 2 }}>\n            An antidetect browser window will open. Follow these steps to complete Facebook login:\n          </Alert>\n\n          <Box component=\"ol\" sx={{ pl: 2 }}>\n            {loginInstructions.map((instruction, index) => (\n              <Box component=\"li\" key={index} sx={{ mb: 1 }}>\n                <Typography variant=\"body2\">{instruction}</Typography>\n              </Box>\n            ))}\n          </Box>\n\n          <Alert severity=\"warning\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              <strong>Important:</strong> After successfully logging in to Facebook,\n              click the \"Complete Login\" button in the profile menu to update the profile status to \"Ready\".\n            </Typography>\n          </Alert>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setLoginInstructionsOpen(false)}>\n            Got it\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n}\n\nexport default Profiles;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,cAAc,EACdC,cAAc,QACT,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,QAAQ,EACrBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,YAAY,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,aAAa;AACnE,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,MAAM,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,SAASC,WAAWA,CAAC;EAAEC,OAAO;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,MAAM;EAAEC,eAAe;EAAEC,uBAAuB;EAAEC;AAAyB,CAAC,EAAE;EAAAC,EAAA;EAC9H,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAM2E,eAAe,GAAIC,KAAK,IAAK;IACjCF,WAAW,CAACE,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BJ,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMK,UAAU,GAAGd,OAAO,CAACe,cAAc,IAAIf,OAAO,CAACgB,gBAAgB;EACrE,MAAMC,OAAO,GAAGjB,OAAO,CAACkB,MAAM,KAAK,QAAQ,IAAIJ,UAAU;EAEzD,MAAMK,cAAc,GAAID,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACEtB,OAAA,CAAC1D,IAAI;IAACkF,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC3B1B,OAAA,CAACzD,WAAW;MAAAmF,QAAA,gBACV1B,OAAA,CAAC5D,GAAG;QAACoF,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,YAAY;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAC7F1B,OAAA,CAAC5D,GAAG;UAACoF,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD1B,OAAA,CAAC1C,MAAM;YACLkE,EAAE,EAAE;cACFO,OAAO,EAAE,cAAc;cACvBC,KAAK,EAAE,EAAE;cACTP,MAAM,EAAE,EAAE;cACVQ,EAAE,EAAE;YACN,CAAE;YAAAP,QAAA,EAEDtB,OAAO,CAAC8B,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACTxC,OAAA,CAAC5D,GAAG;YAAAsF,QAAA,gBACF1B,OAAA,CAAC3D,UAAU;cAACoG,OAAO,EAAC,IAAI;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEZ,EAAE,EAAE;cAAI,CAAE;cAAAJ,QAAA,EACvDtB,OAAO,CAAC8B;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACbxC,OAAA,CAACtD,IAAI;cACHiG,KAAK,EAAEvC,OAAO,CAACkB,MAAM,IAAI,UAAW;cACpCsB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEtB,cAAc,CAACnB,OAAO,CAACkB,MAAM;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxC,OAAA,CAACrD,UAAU;UAACmG,OAAO,EAAEhC,eAAgB;UAAAY,QAAA,eACnC1B,OAAA,CAAC9B,YAAY;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENxC,OAAA,CAAC3C,OAAO;QAACmE,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BxC,OAAA,CAACvD,IAAI;QAACuG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAvB,QAAA,gBACzB1B,OAAA,CAACvD,IAAI;UAACyG,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACf1B,OAAA,CAAC5D,GAAG;YAACoF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxD1B,OAAA,CAACpB,YAAY;cAAC4C,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtExC,OAAA,CAAC3D,UAAU;cAACoG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNxC,OAAA,CAAC3D,UAAU;YAACoG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EAAC;UAErD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPxC,OAAA,CAACvD,IAAI;UAACyG,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACf1B,OAAA,CAAC5D,GAAG;YAACoF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxD1B,OAAA,CAACd,YAAY;cAACsC,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtExC,OAAA,CAAC3D,UAAU;cAACoG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNxC,OAAA,CAAC3D,UAAU;YAACoG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDtB,OAAO,CAACiD,QAAQ,IAAI;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPxC,OAAA,CAACvD,IAAI;UAACyG,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACf1B,OAAA,CAAC5D,GAAG;YAACoF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxD1B,OAAA,CAAClB,YAAY;cAAC0C,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtExC,OAAA,CAAC3D,UAAU;cAACoG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNxC,OAAA,CAAC3D,UAAU;YAACoG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDtB,OAAO,CAACkD,UAAU,IAAIlD,OAAO,CAACkD,UAAU,KAAK,UAAU,GACpDlD,OAAO,CAACkD,UAAU,CAAClB,WAAW,CAAC,CAAC,GAChC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPxC,OAAA,CAAC5D,GAAG;UAAC8G,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAzB,QAAA,gBACd1B,OAAA,CAAC5D,GAAG;YAACoF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxD1B,OAAA,CAAChB,YAAY;cAACwC,EAAE,EAAE;gBAAE4B,QAAQ,EAAE,EAAE;gBAAEnB,EAAE,EAAE,CAAC;gBAAEY,KAAK,EAAE;cAAiB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtExC,OAAA,CAAC3D,UAAU;cAACoG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNxC,OAAA,CAAC3D,UAAU;YAACoG,OAAO,EAAC,OAAO;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAhB,QAAA,EACjDtB,OAAO,CAACmD,QAAQ,IAAI;UAAO;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEF,CAAC,eAEPxC,OAAA,CAAC5D,GAAG;QAACoF,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE7B,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,cAAc,EAAE;QAAgB,CAAE;QAAAF,QAAA,eACzF1B,OAAA,CAAC5D,GAAG;UAACoF,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE4B,GAAG,EAAE;UAAE,CAAE;UAAA/B,QAAA,gBACzD1B,OAAA,CAACZ,YAAY;YAACgE,QAAQ,EAAC,OAAO;YAACP,KAAK,EAAC;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDxC,OAAA,CAAC3D,UAAU;YAACoG,OAAO,EAAC,OAAO;YAACI,KAAK,EAAC,gBAAgB;YAACrB,EAAE,EAAE;cAAE4B,QAAQ,EAAE;YAAU,CAAE;YAAA1B,QAAA,EAAC;UAEhF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZtB,UAAU,gBACTlB,OAAA,CAAC5D,GAAG;YAACoF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE4B,GAAG,EAAE;YAAI,CAAE;YAAA/B,QAAA,gBAC3D1B,OAAA,CAACR,eAAe;cAAC4D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DxC,OAAA,CAAC3D,UAAU;cAACoG,OAAO,EAAC,OAAO;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAEvE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAENxC,OAAA,CAAC5D,GAAG;YAACoF,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE4B,GAAG,EAAE;YAAI,CAAE;YAAA/B,QAAA,gBAC3D1B,OAAA,CAACV,SAAS;cAAC8D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDxC,OAAA,CAAC3D,UAAU;cAACoG,OAAO,EAAC,OAAO;cAACjB,EAAE,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAEvE;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxC,OAAA,CAAC5D,GAAG;QAACoF,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE7B,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBAC1C1B,OAAA,CAACxD,MAAM;UACLoG,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,UAAU;UAClBiB,SAAS,eAAE1D,OAAA,CAACxB,QAAQ;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAMvC,MAAM,CAACH,OAAO,CAAE;UAC/BoB,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxC,OAAA,CAACxD,MAAM;UACLoG,IAAI,EAAC,OAAO;UACZH,OAAO,EAAC,MAAM;UACdiB,SAAS,eAAE1D,OAAA,CAAC5B,QAAQ;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBM,OAAO,EAAEA,CAAA,KAAMzC,MAAM,CAACD,OAAO,CAAE;UAC/BoB,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxC,OAAA,CAACzC,IAAI;QACHqD,QAAQ,EAAEA,QAAS;QACnBgD,IAAI,EAAEC,OAAO,CAACjD,QAAQ,CAAE;QACxBkD,OAAO,EAAE7C,eAAgB;QAAAS,QAAA,gBAEzB1B,OAAA,CAAC5C,QAAQ;UAAC0F,OAAO,EAAEA,CAAA,KAAM;YAAEzC,MAAM,CAACD,OAAO,CAAC;YAAEa,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBAC/D1B,OAAA,CAACxC,YAAY;YAAAkE,QAAA,eACX1B,OAAA,CAAC5B,QAAQ;cAACgF,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfxC,OAAA,CAACvC,YAAY;YAAAiE,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACXxC,OAAA,CAAC5C,QAAQ;UAAC0F,OAAO,EAAEA,CAAA,KAAM;YAAEvC,MAAM,CAACH,OAAO,CAAC;YAAEa,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBAC/D1B,OAAA,CAACxC,YAAY;YAAAkE,QAAA,eACX1B,OAAA,CAACxB,QAAQ;cAAC4E,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfxC,OAAA,CAACvC,YAAY;YAAAiE,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEXxC,OAAA,CAAC3C,OAAO;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAEV,CAACtB,UAAU,gBACVlB,OAAA,CAAC5C,QAAQ;UAAC0F,OAAO,EAAEA,CAAA,KAAM;YAAEtC,eAAe,CAACJ,OAAO,CAAC;YAAEa,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBACxE1B,OAAA,CAACxC,YAAY;YAAAkE,QAAA,eACX1B,OAAA,CAACZ,YAAY;cAACgE,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACfxC,OAAA,CAACvC,YAAY;YAAAiE,QAAA,EAAC;UAAiB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,gBAEXxC,OAAA,CAAC5C,QAAQ;UAAC0F,OAAO,EAAEA,CAAA,KAAM;YAAErC,uBAAuB,CAACL,OAAO,CAAC;YAAEa,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBAChF1B,OAAA,CAACxC,YAAY;YAAAkE,QAAA,eACX1B,OAAA,CAACR,eAAe;cAAC4D,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACfxC,OAAA,CAACvC,YAAY;YAAAiE,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACX,eAEDxC,OAAA,CAAC5C,QAAQ;UAAC0F,OAAO,EAAEA,CAAA,KAAM;YAAEpC,wBAAwB,CAACN,OAAO,CAAC;YAAEa,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBACjF1B,OAAA,CAACxC,YAAY;YAAAkE,QAAA,eACX1B,OAAA,CAACtB,WAAW;cAAC0E,QAAQ,EAAC,OAAO;cAAC5B,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAU;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACfxC,OAAA,CAACvC,YAAY;YAAAiE,QAAA,EAAC;UAAyB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eAEXxC,OAAA,CAAC3C,OAAO;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEXxC,OAAA,CAAC5C,QAAQ;UAAC0F,OAAO,EAAEA,CAAA,KAAM;YAAExC,QAAQ,CAACF,OAAO,CAAC;YAAEa,eAAe,CAAC,CAAC;UAAE,CAAE;UAAAS,QAAA,gBACjE1B,OAAA,CAACxC,YAAY;YAAAkE,QAAA,eACX1B,OAAA,CAAC1B,UAAU;cAAC8E,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACfxC,OAAA,CAACvC,YAAY;YAAAiE,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX;AAAC7B,EAAA,CAlNQR,WAAW;AAAA4D,EAAA,GAAX5D,WAAW;AAoNpB,SAAS6D,QAAQA,CAAA,EAAG;EAAAC,GAAA;EAClB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhI,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiI,cAAc,EAAEC,iBAAiB,CAAC,GAAGlI,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACmI,eAAe,EAAEC,kBAAkB,CAAC,GAAGpI,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACqI,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtI,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACuI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1I,QAAQ,CAAC,IAAI2I,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7I,QAAQ,CAAC;IACvC+F,IAAI,EAAE,EAAE;IACR+C,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,EAAE;IACdC,iBAAiB,EAAE,WAAW;IAC9B9B,QAAQ,EAAE,KAAK;IACfE,QAAQ,EAAE,OAAO;IACjB6B,aAAa,EAAE,KAAK;IACpB9B,UAAU,EAAE,UAAU;IACtB+B,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG9F,cAAc,CAAC,CAAC;EACpC,MAAM;IAAE+F;EAAY,CAAC,GAAG5F,MAAM,CAAC,CAAC;;EAEhC;EACA,MAAM;IAAE6F,IAAI,EAAEC,YAAY;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAGrG,QAAQ,CACvD,UAAU,EACV,MAAMI,WAAW,CAACkG,MAAM,CAAC,CAAC,CAACC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACN,IAAI,CAAC,EAC1D;IACEO,SAAS,EAAGP,IAAI,IAAK;MACnB;MACA,MAAMQ,YAAY,GAAG,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,QAAQ,KAAIT,IAAI,IAAI,EAAE;MACjDD,WAAW,CAACS,YAAY,CAAC;IAC3B,CAAC;IACDE,OAAO,EAAGP,KAAK,IAAK;MAClBlG,KAAK,CAACkG,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CACF,CAAC;;EAED;EACA,MAAMQ,cAAc,GAAG5G,WAAW,CAACG,WAAW,CAAC0G,MAAM,EAAE;IACrDL,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzCrC,mBAAmB,CAAC,KAAK,CAAC;MAC1BsC,SAAS,CAAC,CAAC;MACX7G,KAAK,CAAC8G,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAa,eAAA,EAAAC,oBAAA;MAClBC,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAa,eAAA,GAALb,KAAK,CAAEG,QAAQ,cAAAU,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAiBhB,IAAI,cAAAiB,oBAAA,uBAArBA,oBAAA,CAAuBG,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,0BAA0B;MAC9CpH,KAAK,CAACkG,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CAAC,CAAC;;EAEF;EACA,MAAMG,cAAc,GAAGvH,WAAW,CAChC,CAAC;IAAEwH,EAAE;IAAEvB;EAAK,CAAC,KAAK9F,WAAW,CAACsH,MAAM,CAACD,EAAE,EAAEvB,IAAI,CAAC,EAC9C;IACEO,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzCnC,iBAAiB,CAAC,KAAK,CAAC;MACxBoC,SAAS,CAAC,CAAC;MACX7G,KAAK,CAAC8G,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAsB,gBAAA,EAAAC,qBAAA;MAClBR,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAsB,gBAAA,GAALtB,KAAK,CAAEG,QAAQ,cAAAmB,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBzB,IAAI,cAAA0B,qBAAA,uBAArBA,qBAAA,CAAuBN,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,0BAA0B;MAC9CpH,KAAK,CAACkG,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;;EAED;EACA,MAAMQ,cAAc,GAAG5H,WAAW,CAACG,WAAW,CAAC0H,MAAM,EAAE;IACrDrB,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MACzC5G,KAAK,CAAC8G,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IACDL,OAAO,EAAGP,KAAK,IAAK;MAClBlG,KAAK,CAACkG,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC,CAAC;;EAIF;EACA,MAAM0B,iBAAiB,GAAG9H,WAAW,CAClC+H,SAAS,IAAK5H,WAAW,CAAC6H,SAAS,CAACD,SAAS,CAAC,EAC/C;IACEvB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B,IAAIgC,MAAM,CAACrG,MAAM,KAAK,SAAS,EAAE;QAC/B1B,KAAK,CAAC8G,OAAO,CAAC,yCAAyCiB,MAAM,CAACC,aAAa,GAAG,CAAC;MACjF,CAAC,MAAM,IAAID,MAAM,CAACrG,MAAM,KAAK,UAAU,EAAE;QACvC1B,KAAK,CAACiI,IAAI,CAAC,sCAAsC,CAAC;MACpD,CAAC,MAAM;QACLjI,KAAK,CAACkG,KAAK,CAAC,sBAAsB6B,MAAM,CAACX,OAAO,EAAE,CAAC;MACrD;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAgC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAClBnB,OAAO,CAACf,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;;MAEzC;MACA,IAAI,CAAAA,KAAK,aAALA,KAAK,wBAAAgC,gBAAA,GAALhC,KAAK,CAAEG,QAAQ,cAAA6B,gBAAA,uBAAfA,gBAAA,CAAiBxG,MAAM,MAAK,GAAG,EAAE;QACnC1B,KAAK,CAACqI,OAAO,CAAC,6EAA6E,CAAC;MAC9F,CAAC,MAAM,IAAI,CAAAnC,KAAK,aAALA,KAAK,wBAAAiC,gBAAA,GAALjC,KAAK,CAAEG,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBpC,IAAI,cAAAqC,qBAAA,uBAArBA,qBAAA,CAAuBhB,OAAO,MAAK,oBAAoB,EAAE;QAClEpH,KAAK,CAACqI,OAAO,CAAC,wDAAwD,CAAC;MACzE,CAAC,MAAM;QAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QACL,MAAMvB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAoC,gBAAA,GAALpC,KAAK,CAAEG,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBvC,IAAI,cAAAwC,qBAAA,uBAArBA,qBAAA,CAAuBpB,MAAM,MAC9BjB,KAAK,aAALA,KAAK,wBAAAsC,gBAAA,GAALtC,KAAK,CAAEG,QAAQ,cAAAmC,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBzC,IAAI,cAAA0C,qBAAA,uBAArBA,qBAAA,CAAuBrB,OAAO,MAC9BlB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,mBAAmB;QACvCpH,KAAK,CAACkG,KAAK,CAACgB,YAAY,CAAC;MAC3B;IACF;EACF,CACF,CAAC;;EAED;EACA,MAAMwB,qBAAqB,GAAG5I,WAAW,CACtC+H,SAAS,IAAK5H,WAAW,CAAC0I,aAAa,CAACd,SAAS,CAAC,EACnD;IACEvB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B,IAAIgC,MAAM,CAACrG,MAAM,KAAK,iBAAiB,IAAIqG,MAAM,CAACrG,MAAM,KAAK,kBAAkB,EAAE;QAC/E1B,KAAK,CAAC8G,OAAO,CAAC,sFAAsF,CAAC;QACrG;QACA/B,oBAAoB,CAACgD,MAAM,CAACa,YAAY,IAAI,EAAE,CAAC;QAC/C/D,wBAAwB,CAAC,IAAI,CAAC;;QAE9B;QACAgE,uBAAuB,CAACd,MAAM,CAACe,UAAU,CAAC;MAC5C,CAAC,MAAM,IAAIf,MAAM,CAACrG,MAAM,KAAK,gBAAgB,EAAE;QAC7C1B,KAAK,CAACiI,IAAI,CAAC,gFAAgF,CAAC;QAC5FlD,oBAAoB,CAAC,CACnB,oCAAoC,EACpC,wDAAwD,EACxD,yCAAyC,CAC1C,CAAC;QACFF,wBAAwB,CAAC,IAAI,CAAC;MAChC,CAAC,MAAM;QACL7E,KAAK,CAACkG,KAAK,CAAC,0BAA0B6B,MAAM,CAACX,OAAO,EAAE,CAAC;MACzD;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAA6C,gBAAA,EAAAC,qBAAA;MAClB/B,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAA6C,gBAAA,GAAL7C,KAAK,CAAEG,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBhD,IAAI,cAAAiD,qBAAA,uBAArBA,qBAAA,CAAuB7B,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,gCAAgC;MACpDpH,KAAK,CAACkG,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAM+B,6BAA6B,GAAGnJ,WAAW,CAC/C,CAAC;IAAE+H,SAAS;IAAEqB;EAAa,CAAC,KAAKjJ,WAAW,CAACkJ,qBAAqB,CAACtB,SAAS,EAAEqB,YAAY,CAAC,EAC3F;IACE5C,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B,IAAIgC,MAAM,CAACrG,MAAM,KAAK,gBAAgB,EAAE;QACtC1B,KAAK,CAAC8G,OAAO,CAAC,sCAAsCiB,MAAM,CAACqB,YAAY,iBAAiB,CAAC;QACzFvD,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAC;MAC3C,CAAC,MAAM;QACL5G,KAAK,CAACkG,KAAK,CAAC,6BAA6B6B,MAAM,CAACX,OAAO,EAAE,CAAC;MAC5D;IACF,CAAC;IACDX,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAmD,gBAAA,EAAAC,qBAAA;MAClBrC,OAAO,CAACf,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAmD,gBAAA,GAALnD,KAAK,CAAEG,QAAQ,cAAAgD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBtD,IAAI,cAAAuD,qBAAA,uBAArBA,qBAAA,CAAuBnC,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,mCAAmC;MACvDpH,KAAK,CAACkG,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAMqC,8BAA8B,GAAGzJ,WAAW,CAC/C+H,SAAS,IAAK5H,WAAW,CAACuJ,sBAAsB,CAAC3B,SAAS,CAAC,EAC5D;IACEvB,SAAS,EAAGD,QAAQ,IAAK;MACvB,MAAM0B,MAAM,GAAG1B,QAAQ,CAACN,IAAI;MAC5B/F,KAAK,CAAC8G,OAAO,CAAC,0CAA0C,CAAC;MACzD2C,sBAAsB,CAAC1B,MAAM,CAACe,UAAU,CAAC;MACzCjE,wBAAwB,CAAC,KAAK,CAAC;IACjC,CAAC;IACD4B,OAAO,EAAGP,KAAK,IAAK;MAAA,IAAAwD,gBAAA,EAAAC,qBAAA;MAClB1C,OAAO,CAACf,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMgB,YAAY,GAAG,CAAAhB,KAAK,aAALA,KAAK,wBAAAwD,gBAAA,GAALxD,KAAK,CAAEG,QAAQ,cAAAqD,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiB3D,IAAI,cAAA4D,qBAAA,uBAArBA,qBAAA,CAAuBxC,MAAM,MAC9BjB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkB,OAAO,KACd,qCAAqC;MACzDpH,KAAK,CAACkG,KAAK,CAACgB,YAAY,CAAC;IAC3B;EACF,CACF,CAAC;EAED,MAAMV,QAAQ,GAAGoD,KAAK,CAACC,OAAO,CAAC7D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEQ,QAAQ,CAAC,GAClDR,YAAY,CAACQ,QAAQ,GACrBoD,KAAK,CAACC,OAAO,CAAC7D,YAAY,CAAC,GAC3BA,YAAY,GACZ,EAAE;EAEN,MAAMa,SAAS,GAAGA,CAAA,KAAM;IACtBzB,WAAW,CAAC;MACV9C,IAAI,EAAE,EAAE;MACR+C,YAAY,EAAE,QAAQ;MACtBC,UAAU,EAAE,EAAE;MACdC,iBAAiB,EAAE,WAAW;MAC9B9B,QAAQ,EAAE,KAAK;MACfE,QAAQ,EAAE,OAAO;MACjB6B,aAAa,EAAE,KAAK;MACpB9B,UAAU,EAAE,UAAU;MACtB+B,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFjB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMmF,YAAY,GAAGA,CAAA,KAAM;IACzBvF,mBAAmB,CAAC,IAAI,CAAC;IACzBsC,SAAS,CAAC,CAAC;EACb,CAAC;EAED,MAAMkD,UAAU,GAAIvJ,OAAO,IAAK;IAC9BmE,kBAAkB,CAACnE,OAAO,CAAC;IAE3B4E,WAAW,CAAC;MACV9C,IAAI,EAAE9B,OAAO,CAAC8B,IAAI,IAAI,EAAE;MACxB+C,YAAY,EAAE,QAAQ;MAAE;MACxBC,UAAU,EAAE9E,OAAO,CAAC8E,UAAU,IAAI,EAAE;MACpCC,iBAAiB,EAAE/E,OAAO,CAAC+E,iBAAiB,IAAI,WAAW;MAC3D9B,QAAQ,EAAEjD,OAAO,CAACiD,QAAQ,IAAI,KAAK;MACnCE,QAAQ,EAAEnD,OAAO,CAACmD,QAAQ,IAAI,OAAO;MACrC6B,aAAa,EAAEhF,OAAO,CAACkD,UAAU,IAAIlD,OAAO,CAACkD,UAAU,KAAK,UAAU;MACtEA,UAAU,EAAElD,OAAO,CAACkD,UAAU,IAAI,UAAU;MAC5C+B,UAAU,EAAEjF,OAAO,CAACiF,UAAU,IAAI,EAAE;MACpCC,UAAU,EAAElF,OAAO,CAACkF,UAAU,GAAGlF,OAAO,CAACkF,UAAU,CAACsE,QAAQ,CAAC,CAAC,GAAG,EAAE;MACnErE,cAAc,EAAE,EAAE;MAAE;MACpBC,cAAc,EAAE,EAAE,CAAE;IACtB,CAAC,CAAC;IACFnB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMwF,YAAY,GAAIzJ,OAAO,IAAK;IAChC,IAAI0J,MAAM,CAACC,OAAO,CAAC,4CAA4C3J,OAAO,CAAC8B,IAAI,IAAI,CAAC,EAAE;MAChFoF,cAAc,CAAC0C,MAAM,CAAC5J,OAAO,CAAC8G,EAAE,CAAC;IACnC;EACF,CAAC;EAED,MAAM+C,UAAU,GAAI7J,OAAO,IAAK;IAC9B;IACA,MAAM8J,SAAS,GAAG9J,OAAO,CAACkD,UAAU,IAAIlD,OAAO,CAACkD,UAAU,KAAK,UAAU,GACrE,GAAGlD,OAAO,CAACkD,UAAU,CAAClB,WAAW,CAAC,CAAC,GAAGhC,OAAO,CAACiF,UAAU,GAAG,KAAKjF,OAAO,CAACiF,UAAU,IAAIjF,OAAO,CAACkF,UAAU,GAAG,GAAG,EAAE,EAAE,GAClH,UAAU;;IAEd;IACA,MAAM6E,WAAW,GAAG,CAClB,YAAY/J,OAAO,CAAC8B,IAAI,EAAE,EAC1B,WAAW9B,OAAO,CAACkB,MAAM,EAAE,EAC3B,UAAU4I,SAAS,EAAE,EACrB,aAAa9J,OAAO,CAACmD,QAAQ,IAAI,OAAO,EAAE,EAC1C,aAAanD,OAAO,CAACiD,QAAQ,IAAI,KAAK,EAAE,EACxC,YAAY,IAAI+G,IAAI,CAAChK,OAAO,CAACiK,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,CAChE,CAACC,IAAI,CAAC,IAAI,CAAC;;IAEZ;IACA3K,KAAK,CAAC8G,OAAO,CAAC,yBAAyByD,WAAW,EAAE,EAAE;MAAEK,QAAQ,EAAE;IAAK,CAAC,CAAC;;IAEzE;IACA,IAAIpK,OAAO,CAACkD,UAAU,IAAIlD,OAAO,CAACkD,UAAU,KAAK,UAAU,IAAIlD,OAAO,CAACiF,UAAU,EAAE;MACjFoF,UAAU,CAAC,MAAM;QACfjD,iBAAiB,CAACwC,MAAM,CAAC5J,OAAO,CAAC8G,EAAE,CAAC;MACtC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMwD,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAMC,UAAU,GAAG;MAAE,GAAG5F;IAAS,CAAC;;IAElC;IACA,IAAI4F,UAAU,CAACtF,UAAU,KAAK,EAAE,EAAEsF,UAAU,CAACtF,UAAU,GAAG,IAAI;IAC9D,IAAIsF,UAAU,CAACrF,UAAU,KAAK,EAAE,EAAEqF,UAAU,CAACrF,UAAU,GAAG,IAAI;IAC9D,IAAIqF,UAAU,CAACpF,cAAc,KAAK,EAAE,EAAEoF,UAAU,CAACpF,cAAc,GAAG,IAAI;IACtE,IAAIoF,UAAU,CAACnF,cAAc,KAAK,EAAE,EAAEmF,UAAU,CAACnF,cAAc,GAAG,IAAI;IACtE,IAAImF,UAAU,CAACzF,UAAU,KAAK,EAAE,EAAEyF,UAAU,CAACzF,UAAU,GAAG,IAAI;;IAE9D;IACA,IAAIyF,UAAU,CAACrF,UAAU,EAAE;MACzB,MAAMsF,IAAI,GAAGC,QAAQ,CAACF,UAAU,CAACrF,UAAU,CAAC;MAC5C,IAAIwF,KAAK,CAACF,IAAI,CAAC,EAAE;QACfhL,KAAK,CAACkG,KAAK,CAAC,mCAAmC,CAAC;QAChD;MACF;MACA6E,UAAU,CAACrF,UAAU,GAAGsF,IAAI;IAC9B;;IAEA;IACA,IAAI7F,QAAQ,CAACzB,UAAU,KAAK,UAAU,EAAE;MACtC,IAAI,CAACyB,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,EAAE;QAChD1F,KAAK,CAACkG,KAAK,CAAC,wDAAwD,CAAC;QACrE;MACF;MAEA,MAAM8E,IAAI,GAAGC,QAAQ,CAAC9F,QAAQ,CAACO,UAAU,CAAC;MAC1C,IAAIwF,KAAK,CAACF,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,KAAK,EAAE;QAC3ChL,KAAK,CAACkG,KAAK,CAAC,uDAAuD,CAAC;QACpE;MACF;IACF;;IAEA;IACA,MAAMiF,SAAS,GAAG;MAChB7I,IAAI,EAAEyI,UAAU,CAACzI,IAAI;MACrBgD,UAAU,EAAEyF,UAAU,CAACzF,UAAU;MACjC8F,cAAc,EAAE;QACd/F,YAAY,EAAE0F,UAAU,CAAC1F,YAAY,IAAI,QAAQ;QACjDE,iBAAiB,EAAEwF,UAAU,CAACxF,iBAAiB,IAAI,WAAW;QAC9D9B,QAAQ,EAAEsH,UAAU,CAACtH,QAAQ,IAAI,KAAK;QACtCE,QAAQ,EAAEoH,UAAU,CAACpH,QAAQ,IAAI;MACnC,CAAC;MACD0H,YAAY,EAAEN,UAAU,CAACrH,UAAU,KAAK,UAAU,GAAG;QACnDA,UAAU,EAAEqH,UAAU,CAACrH,UAAU;QACjC4H,IAAI,EAAEP,UAAU,CAACtF,UAAU;QAC3BuF,IAAI,EAAED,UAAU,CAACrF,UAAU;QAC3B6F,QAAQ,EAAER,UAAU,CAACpF,cAAc;QACnC6F,QAAQ,EAAET,UAAU,CAACnF;MACvB,CAAC,GAAG;IACN,CAAC;IAED,IAAIlB,eAAe,EAAE;MACnB2C,cAAc,CAAC+C,MAAM,CAAC;QAAE9C,EAAE,EAAE5C,eAAe,CAAC4C,EAAE;QAAEvB,IAAI,EAAEoF;MAAU,CAAC,CAAC;IACpE,CAAC,MAAM;MACLzE,cAAc,CAAC0D,MAAM,CAACe,SAAS,CAAC;IAClC;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzCvG,WAAW,CAACwG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAME,mBAAmB,GAAIrL,OAAO,IAAK;IACvCkI,qBAAqB,CAAC0B,MAAM,CAAC5J,OAAO,CAAC8G,EAAE,CAAC;EAC1C,CAAC;EAED,MAAMwE,4BAA4B,GAAItL,OAAO,IAAK;IAChD,IAAI0J,MAAM,CAACC,OAAO,CAAC,+DAA+D3J,OAAO,CAAC8B,IAAI,IAAI,CAAC,EAAE;MACnGiH,8BAA8B,CAACa,MAAM,CAAC5J,OAAO,CAAC8G,EAAE,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMuB,uBAAuB,GAAIhB,SAAS,IAAK;IAC7C;IACA4B,sBAAsB,CAAC5B,SAAS,CAAC;IAEjC,MAAMkE,UAAU,GAAGC,WAAW,CAAC,YAAY;MACzC,IAAI;QACF,MAAM3F,QAAQ,GAAG,MAAMpG,WAAW,CAACgM,mBAAmB,CAACpE,SAAS,CAAC;QACjE,MAAMnG,MAAM,GAAG2E,QAAQ,CAACN,IAAI,CAACmG,YAAY;QAEzC,IAAIxK,MAAM,CAACA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,CAACA,MAAM,KAAK,SAAS,EAAE;UACrE1B,KAAK,CAACqI,OAAO,CAAC,sDAAsD,CAAC;UACrEoB,sBAAsB,CAAC5B,SAAS,CAAC;QACnC,CAAC,MAAM,IAAInG,MAAM,CAACA,MAAM,KAAK,YAAY,EAAE;UACzC+H,sBAAsB,CAAC5B,SAAS,CAAC;QACnC;QACA;MACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;QACde,OAAO,CAACf,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD;MACF;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEXjB,qBAAqB,CAAC2G,IAAI,IAAI,IAAI1G,GAAG,CAAC0G,IAAI,CAACO,GAAG,CAACtE,SAAS,EAAEkE,UAAU,CAAC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMtC,sBAAsB,GAAI5B,SAAS,IAAK;IAC5C,MAAMkE,UAAU,GAAG/G,kBAAkB,CAACoH,GAAG,CAACvE,SAAS,CAAC;IACpD,IAAIkE,UAAU,EAAE;MACdM,aAAa,CAACN,UAAU,CAAC;MACzB9G,qBAAqB,CAAC2G,IAAI,IAAI;QAC5B,MAAMU,MAAM,GAAG,IAAIpH,GAAG,CAAC0G,IAAI,CAAC;QAC5BU,MAAM,CAAC3E,MAAM,CAACE,SAAS,CAAC;QACxB,OAAOyE,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACAhQ,KAAK,CAACiQ,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXvH,kBAAkB,CAACwH,OAAO,CAAET,UAAU,IAAK;QACzCM,aAAa,CAACN,UAAU,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAAC/G,kBAAkB,CAAC,CAAC;EAExB,MAAMyH,2BAA2B,GAAIjM,OAAO,IAAK;IAC/C;IACAiJ,sBAAsB,CAACjJ,OAAO,CAAC8G,EAAE,CAAC;;IAElC;IACA;IACA2B,6BAA6B,CAACmB,MAAM,CAAC;MACnCvC,SAAS,EAAErH,OAAO,CAAC8G,EAAE;MACrB4B,YAAY,EAAE;QACZwD,KAAK,EAAE,kBAAkB;QAAE;QAC3BnB,QAAQ,EAAE,eAAe;QACzBoB,OAAO,EAAE;MACX;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIzG,KAAK,EAAE;IACT,oBACE9F,OAAA,CAAC5D,GAAG;MAACoQ,SAAS,EAAC,SAAS;MAAA9K,QAAA,eACtB1B,OAAA,CAACtC,KAAK;QAAC+O,QAAQ,EAAC,OAAO;QAACjL,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAEvC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACExC,OAAA,CAAC5D,GAAG;IAACoQ,SAAS,EAAC,SAAS;IAAA9K,QAAA,gBACtB1B,OAAA,CAAC5D,GAAG;MAACoF,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF1B,OAAA,CAAC3D,UAAU;QAACoG,OAAO,EAAC,IAAI;QAACjB,EAAE,EAAE;UAAEkB,UAAU,EAAE;QAAI,CAAE;QAAAhB,QAAA,EAAC;MAElD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxC,OAAA,CAAC5D,GAAG;QAACoF,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAE8B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBACnC1B,OAAA,CAACxD,MAAM;UACLiG,OAAO,EAAC,UAAU;UAClBiB,SAAS,eAAE1D,OAAA,CAACtB,WAAW;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BM,OAAO,EAAEA,CAAA,KAAM2C,WAAW,CAACe,iBAAiB,CAAC,UAAU,CAAE;UACzDhF,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxC,OAAA,CAACxD,MAAM;UACLiG,OAAO,EAAC,WAAW;UACnBiB,SAAS,eAAE1D,OAAA,CAAClC,OAAO;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAE4G,YAAa;UACtBlI,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELqD,SAAS,iBAAI7F,OAAA,CAACrC,cAAc;MAAC6D,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE;IAAE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAI9C4D,QAAQ,CAACsG,MAAM,KAAK,CAAC,IAAI,CAAC7G,SAAS,gBAClC7F,OAAA,CAAC1D,IAAI;MAAAoF,QAAA,eACH1B,OAAA,CAACzD,WAAW;QAACiF,EAAE,EAAE;UAAEmL,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAlL,QAAA,gBAC9C1B,OAAA,CAAChC,UAAU;UAACwD,EAAE,EAAE;YAAE4B,QAAQ,EAAE,EAAE;YAAEP,KAAK,EAAE,gBAAgB;YAAEf,EAAE,EAAE;UAAE;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpExC,OAAA,CAAC3D,UAAU;UAACoG,OAAO,EAAC,IAAI;UAACjB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAExC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxC,OAAA,CAAC3D,UAAU;UAACoG,OAAO,EAAC,OAAO;UAACI,KAAK,EAAC,gBAAgB;UAACrB,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxC,OAAA,CAACxD,MAAM;UACLiG,OAAO,EAAC,WAAW;UACnBiB,SAAS,eAAE1D,OAAA,CAAClC,OAAO;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,OAAO,EAAE4G,YAAa;UACtBlI,EAAE,EAAE;YAAEmC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAC/B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEPxC,OAAA,CAACvD,IAAI;MAACuG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvB,QAAA,EACxB0E,QAAQ,CAACyG,GAAG,CAAEzM,OAAO,iBACpBJ,OAAA,CAACvD,IAAI;QAACyG,IAAI;QAACC,EAAE,EAAE,EAAG;QAAC2J,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAArL,QAAA,eAC9B1B,OAAA,CAACG,WAAW;UACVC,OAAO,EAAEA,OAAQ;UACjBC,MAAM,EAAEsJ,UAAW;UACnBrJ,QAAQ,EAAEuJ,YAAa;UACvBtJ,MAAM,EAAE0J,UAAW;UACnBzJ,eAAe,EAAEiL,mBAAoB;UACrChL,uBAAuB,EAAE4L,2BAA4B;UACrD3L,wBAAwB,EAAEgL;QAA6B;UAAArJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD;MAAC,GATkCpC,OAAO,CAAC8G,EAAE;QAAA7E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAU1C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDxC,OAAA,CAACpD,MAAM;MACLgH,IAAI,EAAEM,gBAAgB,IAAIE,cAAe;MACzCN,OAAO,EAAEA,CAAA,KAAM;QACbK,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,iBAAiB,CAAC,KAAK,CAAC;QACxBoC,SAAS,CAAC,CAAC;MACb,CAAE;MACFuG,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAvL,QAAA,gBAET1B,OAAA,CAACnD,WAAW;QAAA6E,QAAA,EACT4C,eAAe,GAAG,cAAc,GAAG;MAAoB;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACdxC,OAAA,CAAClD,aAAa;QAAA4E,QAAA,eACZ1B,OAAA,CAAC5D,GAAG;UAACoF,EAAE,EAAE;YAAE0L,EAAE,EAAE;UAAE,CAAE;UAAAxL,QAAA,eACjB1B,OAAA,CAACvD,IAAI;YAACuG,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAvB,QAAA,gBACzB1B,OAAA,CAACvD,IAAI;cAACyG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChB1B,OAAA,CAAChD,SAAS;gBACRiQ,SAAS;gBACTtK,KAAK,EAAC,cAAc;gBACpB4I,KAAK,EAAExG,QAAQ,CAAC7C,IAAK;gBACrBiL,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,MAAM,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;gBAC1D+B,QAAQ;cAAA;gBAAAjL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPxC,OAAA,CAACvD,IAAI;cAACyG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC2J,EAAE,EAAE,CAAE;cAAApL,QAAA,eACvB1B,OAAA,CAAC/C,WAAW;gBAACgQ,SAAS;gBAAAvL,QAAA,gBACpB1B,OAAA,CAAC9C,UAAU;kBAAAwE,QAAA,EAAC;gBAAY;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCxC,OAAA,CAAC7C,MAAM;kBACLoO,KAAK,EAAExG,QAAQ,CAACE,YAAa;kBAC7BkI,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,cAAc,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBAClE5I,KAAK,EAAC,cAAc;kBAAAjB,QAAA,gBAEpB1B,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,QAAQ;oBAAA7J,QAAA,EAAC;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,SAAS;oBAAA7J,QAAA,EAAC;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5CxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,MAAM;oBAAA7J,QAAA,EAAC;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPxC,OAAA,CAACvD,IAAI;cAACyG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC2J,EAAE,EAAE,CAAE;cAAApL,QAAA,eACvB1B,OAAA,CAAC/C,WAAW;gBAACgQ,SAAS;gBAAAvL,QAAA,gBACpB1B,OAAA,CAAC9C,UAAU;kBAAAwE,QAAA,EAAC;gBAAiB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CxC,OAAA,CAAC7C,MAAM;kBACLoO,KAAK,EAAExG,QAAQ,CAACI,iBAAkB;kBAClCgI,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,mBAAmB,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBACvE5I,KAAK,EAAC,mBAAmB;kBAAAjB,QAAA,gBAEzB1B,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,WAAW;oBAAA7J,QAAA,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,UAAU;oBAAA7J,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,UAAU;oBAAA7J,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,UAAU;oBAAA7J,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPxC,OAAA,CAACvD,IAAI;cAACyG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC2J,EAAE,EAAE,CAAE;cAAApL,QAAA,eACvB1B,OAAA,CAAC/C,WAAW;gBAACgQ,SAAS;gBAAAvL,QAAA,gBACpB1B,OAAA,CAAC9C,UAAU;kBAAAwE,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCxC,OAAA,CAAC7C,MAAM;kBACLoO,KAAK,EAAExG,QAAQ,CAAC1B,QAAS;kBACzB8J,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,UAAU,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBAC9D5I,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhB1B,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,KAAK;oBAAA7J,QAAA,EAAC;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpCxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,kBAAkB;oBAAA7J,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9DxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,kBAAkB;oBAAA7J,QAAA,EAAC;kBAAgB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9DxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,eAAe;oBAAA7J,QAAA,EAAC;kBAAa;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPxC,OAAA,CAACvD,IAAI;cAACyG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC2J,EAAE,EAAE,CAAE;cAAApL,QAAA,eACvB1B,OAAA,CAAC/C,WAAW;gBAACgQ,SAAS;gBAAAvL,QAAA,gBACpB1B,OAAA,CAAC9C,UAAU;kBAAAwE,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCxC,OAAA,CAAC7C,MAAM;kBACLoO,KAAK,EAAExG,QAAQ,CAACxB,QAAS;kBACzB4J,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,UAAU,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBAC9D5I,KAAK,EAAC,UAAU;kBAAAjB,QAAA,gBAEhB1B,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,OAAO;oBAAA7J,QAAA,EAAC;kBAAY;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC/CxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,OAAO;oBAAA7J,QAAA,EAAC;kBAAU;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC7CxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,OAAO;oBAAA7J,QAAA,EAAC;kBAAoB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACvDxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,OAAO;oBAAA7J,QAAA,EAAC;kBAAQ;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPxC,OAAA,CAACvD,IAAI;cAACyG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChB1B,OAAA,CAAChD,SAAS;gBACRiQ,SAAS;gBACTtK,KAAK,EAAC,uBAAuB;gBAC7B4I,KAAK,EAAExG,QAAQ,CAACG,UAAW;gBAC3BiI,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,YAAY,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;gBAChEgC,WAAW,EAAC;cAAsC;gBAAAlL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPxC,OAAA,CAACvD,IAAI;cAACyG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAzB,QAAA,eAChB1B,OAAA,CAAC3D,UAAU;gBAACoG,OAAO,EAAC,IAAI;gBAACjB,EAAE,EAAE;kBAAEM,EAAE,EAAE,CAAC;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAAC;cAEzD;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEPxC,OAAA,CAACvD,IAAI;cAACyG,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC2J,EAAE,EAAE,CAAE;cAAApL,QAAA,eACvB1B,OAAA,CAAC/C,WAAW;gBAACgQ,SAAS;gBAAAvL,QAAA,gBACpB1B,OAAA,CAAC9C,UAAU;kBAAAwE,QAAA,EAAC;gBAAU;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCxC,OAAA,CAAC7C,MAAM;kBACLoO,KAAK,EAAExG,QAAQ,CAACzB,UAAW;kBAC3B6J,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMI,SAAS,GAAGJ,CAAC,CAACC,MAAM,CAAC9B,KAAK;oBAChCF,gBAAgB,CAAC,YAAY,EAAEmC,SAAS,CAAC;oBACzCnC,gBAAgB,CAAC,eAAe,EAAEmC,SAAS,KAAK,UAAU,CAAC;kBAC7D,CAAE;kBACF7K,KAAK,EAAC,YAAY;kBAAAjB,QAAA,gBAElB1B,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,UAAU;oBAAA7J,QAAA,eACxB1B,OAAA,CAAC5D,GAAG;sBAAAsF,QAAA,gBACF1B,OAAA,CAAC3D,UAAU;wBAACoG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAwB;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjExC,OAAA,CAAC3D,UAAU;wBAACoG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,MAAM;oBAAA7J,QAAA,eACpB1B,OAAA,CAAC5D,GAAG;sBAAAsF,QAAA,gBACF1B,OAAA,CAAC3D,UAAU;wBAACoG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC7CxC,OAAA,CAAC3D,UAAU;wBAACoG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,OAAO;oBAAA7J,QAAA,eACrB1B,OAAA,CAAC5D,GAAG;sBAAAsF,QAAA,gBACF1B,OAAA,CAAC3D,UAAU;wBAACoG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAK;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC9CxC,OAAA,CAAC3D,UAAU;wBAACoG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,QAAQ;oBAAA7J,QAAA,eACtB1B,OAAA,CAAC5D,GAAG;sBAAAsF,QAAA,gBACF1B,OAAA,CAAC3D,UAAU;wBAACoG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAM;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC/CxC,OAAA,CAAC3D,UAAU;wBAACoG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACXxC,OAAA,CAAC5C,QAAQ;oBAACmO,KAAK,EAAC,KAAK;oBAAA7J,QAAA,eACnB1B,OAAA,CAAC5D,GAAG;sBAAAsF,QAAA,gBACF1B,OAAA,CAAC3D,UAAU;wBAACoG,OAAO,EAAC,OAAO;wBAAAf,QAAA,EAAC;sBAAG;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC5CxC,OAAA,CAAC3D,UAAU;wBAACoG,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAAC;sBAErD;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACTxC,OAAA,CAACpC,cAAc;kBAAA8D,QAAA,GACZqD,QAAQ,CAACzB,UAAU,KAAK,UAAU,IAAI,yBAAyB,EAC/DyB,QAAQ,CAACzB,UAAU,KAAK,MAAM,IAAI,iCAAiC,EACnEyB,QAAQ,CAACzB,UAAU,KAAK,OAAO,IAAI,sCAAsC,EACzEyB,QAAQ,CAACzB,UAAU,KAAK,QAAQ,IAAI,wCAAwC,EAC5EyB,QAAQ,CAACzB,UAAU,KAAK,KAAK,IAAI,4CAA4C;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAENuC,QAAQ,CAACzB,UAAU,KAAK,UAAU,iBACjCtD,OAAA,CAAAE,SAAA;cAAAwB,QAAA,gBACE1B,OAAA,CAACvD,IAAI;gBAACyG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC2J,EAAE,EAAE,CAAE;gBAAApL,QAAA,eACvB1B,OAAA,CAAChD,SAAS;kBACRiQ,SAAS;kBACTtK,KAAK,EAAC,YAAY;kBAClB4I,KAAK,EAAExG,QAAQ,CAACM,UAAW;kBAC3B8H,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,YAAY,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBAChE+B,QAAQ;kBACRC,WAAW,EACTxI,QAAQ,CAACzB,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC,0CACL;kBACDmK,UAAU,EAAC;gBAA+C;kBAAApL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxC,OAAA,CAACvD,IAAI;gBAACyG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC2J,EAAE,EAAE,CAAE;gBAAApL,QAAA,eACvB1B,OAAA,CAAChD,SAAS;kBACRiQ,SAAS;kBACTtK,KAAK,EAAC,YAAY;kBAClB4I,KAAK,EAAExG,QAAQ,CAACO,UAAW;kBAC3B6H,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,YAAY,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBAChEmC,IAAI,EAAC,QAAQ;kBACbJ,QAAQ;kBACRC,WAAW,EACTxI,QAAQ,CAACzB,UAAU,KAAK,MAAM,GAAG,kBAAkB,GACnDyB,QAAQ,CAACzB,UAAU,KAAK,OAAO,GAAG,iBAAiB,GACnDyB,QAAQ,CAACzB,UAAU,KAAK,QAAQ,GAAG,kBAAkB,GACrDyB,QAAQ,CAACzB,UAAU,KAAK,KAAK,GAAG,UAAU,GAAG,aAC9C;kBACDmK,UAAU,EAAE,UAAU1I,QAAQ,CAACzB,UAAU,CAAClB,WAAW,CAAC,CAAC,QAAS;kBAChEuL,UAAU,EAAE;oBAAEC,GAAG,EAAE,CAAC;oBAAEC,GAAG,EAAE;kBAAM;gBAAE;kBAAAxL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxC,OAAA,CAACvD,IAAI;gBAACyG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC2J,EAAE,EAAE,CAAE;gBAAApL,QAAA,eACvB1B,OAAA,CAAChD,SAAS;kBACRiQ,SAAS;kBACTtK,KAAK,EAAEoC,QAAQ,CAACzB,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEiI,KAAK,EAAExG,QAAQ,CAACQ,cAAe;kBAC/B4H,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,gBAAgB,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBACpEgC,WAAW,EACTxI,QAAQ,CAACzB,UAAU,KAAK,KAAK,GACzB,iCAAiC,GACjC,2CACL;kBACDmK,UAAU,EACR1I,QAAQ,CAACzB,UAAU,KAAK,KAAK,GACzB,8BAA8B,GAC9B;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxC,OAAA,CAACvD,IAAI;gBAACyG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC2J,EAAE,EAAE,CAAE;gBAAApL,QAAA,eACvB1B,OAAA,CAAChD,SAAS;kBACRiQ,SAAS;kBACTtK,KAAK,EAAEoC,QAAQ,CAACzB,UAAU,KAAK,KAAK,GAAG,cAAc,GAAG,gBAAiB;kBACzEiI,KAAK,EAAExG,QAAQ,CAACS,cAAe;kBAC/B2H,QAAQ,EAAGC,CAAC,IAAK/B,gBAAgB,CAAC,gBAAgB,EAAE+B,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAE;kBACpEmC,IAAI,EAAC,UAAU;kBACfH,WAAW,EACTxI,QAAQ,CAACzB,UAAU,KAAK,KAAK,GACzB,gDAAgD,GAChD,2CACL;kBACDmK,UAAU,EACR1I,QAAQ,CAACzB,UAAU,KAAK,KAAK,GACzB,wCAAwC,GACxC;gBACL;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPxC,OAAA,CAACvD,IAAI;gBAACyG,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAzB,QAAA,eAChB1B,OAAA,CAAC5D,GAAG;kBAACoF,EAAE,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAE8B,GAAG,EAAE,CAAC;oBAAE5B,UAAU,EAAE;kBAAS,CAAE;kBAAAH,QAAA,gBACzD1B,OAAA,CAACxD,MAAM;oBACLiG,OAAO,EAAC,UAAU;oBAClBG,IAAI,EAAC,OAAO;oBACZE,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAIwB,eAAe,EAAE;wBACnBkD,iBAAiB,CAACwC,MAAM,CAAC1F,eAAe,CAAC4C,EAAE,CAAC;sBAC9C,CAAC,MAAM;wBACL;wBACA,MAAMgD,SAAS,GAAG,GAAGnF,QAAQ,CAACzB,UAAU,CAAClB,WAAW,CAAC,CAAC,WAAW2C,QAAQ,CAACM,UAAU,IAAIN,QAAQ,CAACO,UAAU,EAAE;wBAC7G,MAAMwI,QAAQ,GAAG/I,QAAQ,CAACQ,cAAc,GAAG,WAAWR,QAAQ,CAACQ,cAAc,GAAG,GAAG,YAAY;wBAC/F3F,KAAK,CAACiI,IAAI,CAAC,yBAAyBqC,SAAS,GAAG4D,QAAQ,4CAA4C,EAAE;0BAAEtD,QAAQ,EAAE;wBAAK,CAAC,CAAC;sBAC3H;oBACF,CAAE;oBACFuD,QAAQ,EAAE,CAAChJ,QAAQ,CAACM,UAAU,IAAI,CAACN,QAAQ,CAACO,UAAU,IAAIkC,iBAAiB,CAAC3B,SAAU;oBACtFrE,EAAE,EAAE;sBAAEmC,aAAa,EAAE;oBAAO,CAAE;oBAAAjC,QAAA,EAE7B8F,iBAAiB,CAAC3B,SAAS,GAAG,YAAY,GAAGvB,eAAe,GAAG,iBAAiB,GAAG;kBAAgB;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC,eACTxC,OAAA,CAAC3D,UAAU;oBAACoG,OAAO,EAAC,SAAS;oBAACI,KAAK,EAAC,gBAAgB;oBAAAnB,QAAA,EAAC;kBAErD;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,eACP,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBxC,OAAA,CAACjD,aAAa;QAAA2E,QAAA,gBACZ1B,OAAA,CAACxD,MAAM;UACLsG,OAAO,EAAEA,CAAA,KAAM;YACbqB,mBAAmB,CAAC,KAAK,CAAC;YAC1BE,iBAAiB,CAAC,KAAK,CAAC;YACxBoC,SAAS,CAAC,CAAC;UACb,CAAE;UAAA/E,QAAA,EACH;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxC,OAAA,CAACxD,MAAM;UACLiG,OAAO,EAAC,WAAW;UACnBK,OAAO,EAAE4H,YAAa;UACtBqD,QAAQ,EAAE,CAAChJ,QAAQ,CAAC7C,IAAI,IAAIoE,cAAc,CAACT,SAAS,IAAIoB,cAAc,CAACpB,SAAU;UAAAnE,QAAA,EAEhF4C,eAAe,GAAG,QAAQ,GAAG;QAAQ;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTxC,OAAA,CAACpD,MAAM;MACLgH,IAAI,EAAEY,qBAAsB;MAC5BV,OAAO,EAAEA,CAAA,KAAMW,wBAAwB,CAAC,KAAK,CAAE;MAC/CuI,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAvL,QAAA,gBAET1B,OAAA,CAACnD,WAAW;QAAC2E,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAE4B,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBACjE1B,OAAA,CAACZ,YAAY;UAACoC,EAAE,EAAE;YAAEqB,KAAK,EAAE;UAAU;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+BAE5C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACdxC,OAAA,CAAClD,aAAa;QAAA4E,QAAA,gBACZ1B,OAAA,CAACtC,KAAK;UAAC+O,QAAQ,EAAC,MAAM;UAACjL,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAEtC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAERxC,OAAA,CAAC5D,GAAG;UAAC4R,SAAS,EAAC,IAAI;UAACxM,EAAE,EAAE;YAAEyM,EAAE,EAAE;UAAE,CAAE;UAAAvM,QAAA,EAC/BgD,iBAAiB,CAACmI,GAAG,CAAC,CAACqB,WAAW,EAAEC,KAAK,kBACxCnO,OAAA,CAAC5D,GAAG;YAAC4R,SAAS,EAAC,IAAI;YAAaxM,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,eAC5C1B,OAAA,CAAC3D,UAAU;cAACoG,OAAO,EAAC,OAAO;cAAAf,QAAA,EAAEwM;YAAW;cAAA7L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC,GAD/B2L,KAAK;YAAA9L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEzB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxC,OAAA,CAACtC,KAAK;UAAC+O,QAAQ,EAAC,SAAS;UAACjL,EAAE,EAAE;YAAEgC,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,eACtC1B,OAAA,CAAC3D,UAAU;YAACoG,OAAO,EAAC,OAAO;YAAAf,QAAA,gBACzB1B,OAAA;cAAA0B,QAAA,EAAQ;YAAU;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,kJAE7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAChBxC,OAAA,CAACjD,aAAa;QAAA2E,QAAA,eACZ1B,OAAA,CAACxD,MAAM;UAACsG,OAAO,EAAEA,CAAA,KAAM2B,wBAAwB,CAAC,KAAK,CAAE;UAAA/C,QAAA,EAAC;QAExD;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACyB,GAAA,CA9zBQD,QAAQ;EAAA,QAsBKrE,cAAc,EACVG,MAAM,EAGmBL,QAAQ,EAgBlCC,WAAW,EAiBXA,WAAW,EAoBXA,WAAW,EAaRA,WAAW,EAiCPA,WAAW,EAmCHA,WAAW,EAsBVA,WAAW;AAAA;AAAA0O,GAAA,GAtL3CpK,QAAQ;AAg0BjB,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAAqK,GAAA;AAAAC,YAAA,CAAAtK,EAAA;AAAAsK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}