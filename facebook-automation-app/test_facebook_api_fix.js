// Test script to verify Facebook login API fix
const axios = require('axios');

const BASE_URL = 'http://localhost:8000';

async function testFacebookAPIFix() {
  console.log('🔧 Testing Facebook API Fix');
  console.log('=' * 50);

  try {
    // Step 1: Get profiles to find a UUID
    console.log('\n1. Getting profiles to find UUID...');
    const profilesResponse = await axios.get(`${BASE_URL}/api/profiles`);
    
    let profiles = [];
    if (Array.isArray(profilesResponse.data)) {
      profiles = profilesResponse.data;
    } else if (profilesResponse.data.profiles) {
      profiles = profilesResponse.data.profiles;
    }
    
    console.log(`✅ Found ${profiles.length} profiles`);
    
    if (profiles.length === 0) {
      console.log('❌ No profiles found. Please create a profile first.');
      return;
    }
    
    const testProfile = profiles[0];
    const profileId = testProfile.id;
    
    console.log(`Using profile: ${testProfile.name}`);
    console.log(`Profile ID: ${profileId}`);
    console.log(`Profile ID type: ${typeof profileId}`);
    console.log(`Is UUID format: ${/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(profileId)}`);
    
    // Step 2: Test Facebook login endpoint
    console.log('\n2. Testing Facebook login endpoint...');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/profiles/${profileId}/facebook-login`);
      console.log(`✅ Facebook login API works!`);
      console.log(`   Status: ${loginResponse.status}`);
      console.log(`   Response status: ${loginResponse.data.status}`);
      console.log(`   Message: ${loginResponse.data.message}`);
      
      if (loginResponse.data.instructions) {
        console.log(`   Instructions count: ${loginResponse.data.instructions.length}`);
      }
      
    } catch (error) {
      if (error.response?.data?.message === "Endpoint not found") {
        console.log(`❌ Still getting "Endpoint not found" error`);
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Response: ${JSON.stringify(error.response.data)}`);
      } else {
        console.log(`⚠️  Different error (this might be expected):`);
        console.log(`   Status: ${error.response?.status}`);
        console.log(`   Error: ${error.response?.data?.detail || error.message}`);
      }
    }
    
    // Step 3: Test Facebook status endpoint
    console.log('\n3. Testing Facebook status endpoint...');
    try {
      const statusResponse = await axios.get(`${BASE_URL}/api/profiles/${profileId}/facebook-status`);
      console.log(`✅ Facebook status API works!`);
      console.log(`   Status: ${statusResponse.status}`);
      console.log(`   Is logged in: ${statusResponse.data.is_logged_in}`);
      console.log(`   Profile status: ${statusResponse.data.status}`);
      
    } catch (error) {
      if (error.response?.data?.message === "Endpoint not found") {
        console.log(`❌ Facebook status endpoint not found`);
      } else {
        console.log(`⚠️  Facebook status error: ${error.response?.data?.detail || error.message}`);
      }
    }
    
    // Step 4: Test Facebook login complete endpoint
    console.log('\n4. Testing Facebook login complete endpoint...');
    try {
      const mockData = {
        email: "<EMAIL>",
        username: "test_user",
        user_id: "123456789"
      };
      
      const completeResponse = await axios.post(
        `${BASE_URL}/api/profiles/${profileId}/facebook-login-complete`,
        mockData
      );
      console.log(`✅ Facebook login complete API works!`);
      console.log(`   Status: ${completeResponse.status}`);
      console.log(`   Response status: ${completeResponse.data.status}`);
      console.log(`   Message: ${completeResponse.data.message}`);
      
    } catch (error) {
      if (error.response?.data?.message === "Endpoint not found") {
        console.log(`❌ Facebook login complete endpoint not found`);
      } else {
        console.log(`⚠️  Facebook login complete error: ${error.response?.data?.detail || error.message}`);
      }
    }
    
    // Step 5: Test other endpoints with UUID
    console.log('\n5. Testing other endpoints with UUID...');
    
    const endpointsToTest = [
      { method: 'GET', path: `/api/profiles/${profileId}`, name: 'Get Profile' },
      { method: 'POST', path: `/api/profiles/${profileId}/test-proxy`, name: 'Test Proxy' },
      { method: 'POST', path: `/api/profiles/${profileId}/test-browser`, name: 'Test Browser' },
      { method: 'GET', path: `/api/profiles/${profileId}/zendriver-config`, name: 'Zendriver Config' }
    ];
    
    for (const endpoint of endpointsToTest) {
      try {
        let response;
        if (endpoint.method === 'GET') {
          response = await axios.get(`${BASE_URL}${endpoint.path}`);
        } else {
          response = await axios.post(`${BASE_URL}${endpoint.path}`);
        }
        console.log(`   ✅ ${endpoint.name}: ${response.status}`);
      } catch (error) {
        if (error.response?.data?.message === "Endpoint not found") {
          console.log(`   ❌ ${endpoint.name}: Endpoint not found`);
        } else {
          console.log(`   ⚠️  ${endpoint.name}: ${error.response?.status} - ${error.response?.data?.detail || error.message}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
  
  console.log('\n' + '=' * 50);
  console.log('🎯 Facebook API fix testing completed!');
  console.log('\nSummary:');
  console.log('- If you see "Endpoint not found" errors, the backend may need restart');
  console.log('- If you see other errors, the endpoints exist but may have validation issues');
  console.log('- ✅ means the endpoint is working correctly');
}

// Run the test
testFacebookAPIFix();
