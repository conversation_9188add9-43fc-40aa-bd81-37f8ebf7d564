"""
Profile schemas for API requests and responses
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime

class ProfileBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    user_agent: Optional[str] = None
    screen_resolution: Optional[str] = None
    timezone: Optional[str] = None
    language: Optional[str] = None

class ProfileCreate(ProfileBase):
    # Proxy configuration
    proxy_host: Optional[str] = None
    proxy_port: Optional[int] = Field(None, ge=1, le=65535)
    proxy_username: Optional[str] = None
    proxy_password: Optional[str] = None
    proxy_type: Optional[str] = None
    
    # Facebook account info
    facebook_email: Optional[str] = None
    facebook_password: Optional[str] = None
    facebook_username: Optional[str] = None

class ProfileUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    user_agent: Optional[str] = None
    screen_resolution: Optional[str] = None
    timezone: Optional[str] = None
    language: Optional[str] = None
    
    # Proxy configuration
    proxy_host: Optional[str] = None
    proxy_port: Optional[int] = Field(None, ge=1, le=65535)
    proxy_username: Optional[str] = None
    proxy_password: Optional[str] = None
    proxy_type: Optional[str] = None
    
    # Facebook account info
    facebook_email: Optional[str] = None
    facebook_password: Optional[str] = None
    facebook_username: Optional[str] = None
    
    status: Optional[str] = None

class ProfileResponse(ProfileBase):
    id: str
    status: Optional[str] = None
    profile_path: Optional[str] = None
    last_used_at: Optional[str] = None  # ISO string format
    created_at: Optional[str] = None    # ISO string format
    updated_at: Optional[str] = None    # ISO string format
    login_count: int = 0
    message_sent_count: int = 0

    # Facebook info (without sensitive data)
    facebook_email: Optional[str] = None
    facebook_user_id: Optional[str] = None
    facebook_username: Optional[str] = None

    # Proxy info (without sensitive data)
    proxy_host: Optional[str] = None
    proxy_port: Optional[int] = None
    proxy_type: Optional[str] = None
    proxy_config: Optional[dict] = None

    class Config:
        from_attributes = True

class ProfileList(BaseModel):
    profiles: List[ProfileResponse]
    total: int
    page: int
    per_page: int
    
class ProfileStats(BaseModel):
    total_profiles: int
    active_profiles: int
    inactive_profiles: int
    banned_profiles: int
    error_profiles: int
